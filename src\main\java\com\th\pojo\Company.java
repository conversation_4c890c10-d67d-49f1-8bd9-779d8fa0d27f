package com.th.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class Company {
    private Integer id;
    private String companyName;
    private String companyCode;
    private String username;
    private String password;
    private String website;
    private Timestamp createTime;
    private Timestamp updateTime;

    private String oldPassword;
}
