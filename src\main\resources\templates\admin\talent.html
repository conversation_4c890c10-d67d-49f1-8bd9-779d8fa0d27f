<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>

<link rel="stylesheet" href="/static/admin/simditor/css/simditor.css">
<script src="/static/admin/simditor/js/module.min.js"></script>
<script src="/static/admin/simditor/js/hotkeys.min.js"></script>
<script src="/static/admin/simditor/js/uploader.min.js"></script>
<script src="/static/admin/simditor/js/simditor.min.js"></script>
<script src="/static/admin/simditor/js/jquery.form.js"></script>
<style>
    .m{ width: 100%; margin-left: auto; margin-right: auto; }
    .note-editor.note-frame {border: 1px solid #eee!important;}
    .note-editor .note-editing-area {min-height: 500px;}
</style>

<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('系统管理', '人才招聘')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">

            <div class="container-fluid">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <ul class="nav nav-tabs page-tabs">
                                <li class="active"> <a href="javascript:void(0)">基本</a> </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <div class="edit-form">

                                        <div class="form-group">
                                            <label>人才招聘</label>
                                            <p>
                                                <textarea id="content" placeholder="请输入人才招聘......" autofocus th:text="${configs.get('website_talent').value}"/>
                                                <script>
                                                    var editor = new Simditor({
                                                        textarea: $('#content')
                                                        //optional options
                                                    });
                                                </script>
                                            </p>
                                        </div>
                                        <div class="form-group">
                                            <button class="btn btn-primary m-r-5 down">确 定</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(e) {
        $(".down").on('click',function() {
            var target = {
                website_talent : $("#content").val()
            };

            $.ajax({
                url: "/Admin/configAction",
                type: "POST",
                dataType: "json",
                contentType:"application/json;charset=UTF-8",
                data: JSON.stringify(target),
                success: function(data) {
                    if(data.status == true){
                        alert("保存成功!");
                        window.location.href = "/Admin/index.html";
                    }
                    else{
                        alert("保存失败!");
                    }
                },
                error: function (xhr) {
                    alert(xhr);
                }
            });
        });
    });
</script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>