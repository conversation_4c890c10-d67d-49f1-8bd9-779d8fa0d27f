
/** 1. Basic
*******************************************************************/

html
{
    font-size: 10px !important;
}

body
{
    font-family: 'Roboto', sans-serif;
    font-weight: 300;
    font-style: normal;
    overflow: hidden;

    color: #fff;
    background: #000;

    -webkit-font-smoothing: antialiased;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
a
{
    font-family: 'Roboto', sans-serif;
    font-weight: normal;

    color: #fff;
}

h1
{
    font-size: 6rem;
}

h2
{
    font-size: 5rem;
}

h3
{
    font-size: 4rem;
}

h4
{
    font-size: 3rem;
}

h5
{
    font-size: 2rem;
}

h6
{
    font-size: 1.8rem;
}

p
{
    font-size: 1.4rem;
}
.tlinks{text-indent:-9999px;height:0;line-height:0;font-size:0;overflow:hidden;}
a,
a:hover,
a:focus
{
    cursor: pointer;
    -webkit-transition: all 300ms ease;
         -o-transition: all 300ms ease;
            transition: all 300ms ease;
    text-decoration: none;
}

a:hover
{
    color: #fff;
}

@media (max-width:800px)
{
    html
    {
        font-size: 9px !important;
    }
}

@media (max-width:700px)
{
    html
    {
        font-size: 8px !important;
    }
}

@media (max-width:600px)
{
    html
    {
        font-size: 7px !important;
    }
}

@media (max-width:500px)
{
    html
    {
        font-size: 6px !important;
    }
}



/** 2. Preloader
*******************************************************************/

.preloader
{
    position: fixed;
    z-index: 100000;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;

    background: #fff;
}

.preloader.preloader-hidden
{
    height: 0;
    -webkit-transition: .5s ease .3s;
         -o-transition: .5s ease .3s;
            transition: .5s ease .3s;
}

.preloader .spinner
{
    position: absolute;
    top: 50%;

    width: 100%;
    opacity: 1;

    -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
            transform: translateY(-50%);
    text-align: center;
}

.preloader.preloader-hidden .spinner
{
    opacity: 0;
    -webkit-transition: .3s ease;
         -o-transition: .3s ease;
            transition: .3s ease;
}

.preloader .spinner > div
{
    display: inline-block;

    width: 18px;
    height: 18px;
    margin: 0 6px;

    -webkit-animation: spinner-bounce 1.4s infinite ease-in-out both;
            animation: spinner-bounce 1.4s infinite ease-in-out both;

    border-radius: 100%;
    background-color: #111;
}

.preloader .spinner .bounce-1
{
    -webkit-animation-delay: -.32s;
            animation-delay: -.32s;
}

.preloader .spinner .bounce-2
{
    -webkit-animation-delay: -.16s;
            animation-delay: -.16s;
}

@-webkit-keyframes spinner-bounce
{
    0%,
    80%,
    100%
    {
        -webkit-transform: scale(0);
    }
    40%
    {
        -webkit-transform: scale(1.0);
    }
}

@keyframes spinner-bounce
{
    0%,
    80%,
    100%
    {
        -webkit-transform: scale(0);
                transform: scale(0);
    }

    40%
    {
        -webkit-transform: scale(1.0);
                transform: scale(1.0);
    }
}



/** 3. Hero
*******************************************************************/

.hero
{
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    overflow-y: auto;

    width: 100%;
    height: 100%;
}

.hero .full-height
{
    height: 100vh;
}

.hero .container-fluid
{
    max-width: 1000px;
    padding-right: 25px;
    padding-left: 25px;
}

.hero .animation-container
{
    -webkit-transition: .5s ease;
         -o-transition: .5s ease;
            transition: .5s ease;
}

.hero .animation-container.animation-fade
{
    opacity: 0;
}

.hero .animation-container.animation-fade.run-animation
{
    opacity: 1;
}

.hero .animation-container.animation-fade-up
{
    -webkit-transform: translateY(10vh);
        -ms-transform: translateY(10vh);
            transform: translateY(10vh);

    opacity: 0;
}

.hero .animation-container.animation-fade-up.run-animation
{
    -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
            transform: translateY(0);

    opacity: 1;
}

.hero .animation-container.animation-fade-down
{
    -webkit-transform: translateY(-10vh);
        -ms-transform: translateY(-10vh);
            transform: translateY(-10vh);

    opacity: 0;
}

.hero .animation-container.animation-fade-down.run-animation
{
    -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
            transform: translateY(0);

    opacity: 1;
}

.hero .animation-container.animation-fade-left
{
    -webkit-transform: translateX(10vh);
        -ms-transform: translateX(10vh);
            transform: translateX(10vh);

    opacity: 0;
}

.hero .animation-container.animation-fade-left.run-animation
{
    -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
            transform: translateX(0);

    opacity: 1;
}

.hero .animation-container.animation-fade-right
{
    -webkit-transform: translateX(-10vh);
        -ms-transform: translateX(-10vh);
            transform: translateX(-10vh);

    opacity: 0;
}

.hero .animation-container.animation-fade-right.run-animation
{
    -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
            transform: translateX(0);

    opacity: 1;
}


@media (max-width:750px)
{
    .hero .full-height
    {
        height: auto;
        padding: 0;
        padding: 15rem 0;
    }
}



/** 4. Hero --> Front Content 
*******************************************************************/

.hero .front-content
{
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;
}

.hero .front-content .container-mid
{
    position: absolute;
    top: 50%;
    left: 0;

    width: 100%;

    -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
            transform: translateY(-50%);
}

.hero .front-content .container-mid .phone-slider
{
    width: 70%;

    border-top: 40px solid #fff;
    border-bottom: 40px solid #fff;
    border-radius: 26px;
    -webkit-box-shadow: 10px 10px 16px 8px rgba(0,0,0,.2);
            box-shadow: 10px 10px 16px 8px rgba(0,0,0,.2);
}

.hero .front-content .container-mid .phone-slider .owl-dots
{
    position: absolute;
    bottom: -90px;

    width: 100%;

    text-align: center;
}

.hero .front-content .container-mid .phone-slider .owl-dots .owl-dot
{
    display: inline-block;

    width: 1.5rem;
    height: 1.5rem;
    margin: 0 .6rem;

    -webkit-transition: .1s ease;
         -o-transition: .1s ease;
            transition: .1s ease;

    border: 2px solid #fff;
    border-radius: 100%;
    background: none;
}

.hero .front-content .container-mid .phone-slider .owl-dots .owl-dot.active
{
    background: #fff;
}

.hero .front-content .container-mid img.logo
{
    vertical-align: middle;
}

.hero .front-content .container-mid p.subline
{
    font-size: 1.8rem;
    font-weight: 300;
    line-height: 2em;

    margin-top: 2.6em;
    margin-bottom: 2.6em;

    letter-spacing: .025em;
}

.hero .front-content .container-mid .download-button
{
    font-size: 1.8rem;
    font-weight: 400;
    line-height: 2.75em;

    position: relative;

    display: inline-block;
    overflow: hidden;

    margin-right: 2.2em;
    padding: 0 2.2em;

    cursor: pointer;
    -webkit-transition: all .1s linear;
         -o-transition: all .1s linear;
            transition: all .1s linear;
    letter-spacing: .025em;

    color: #111;
    border: 3px solid #fff;
    border-radius: 4em;
    background: #fff;
}

.hero .front-content .container-mid .download-button:hover
{
    color: #111;
    background: #fff;
}

.hero .front-content .container-mid .download-button:last-child
{
    margin-right: 0;
}

.hero .front-content .container-mid .download-button i
{
    width: 1em;
    margin-right: .6em;
}

.hero .front-content .container-mid .download-button:hover i:before
{
    content: '\f019';
}

.hero .front-content .footer
{
    position: fixed;
    bottom: 0;

    width: 100%;

    text-align: center;

    background: rgba(255, 255, 255, .4);
}

.hero .front-content .footer p
{
    display: inline-block;

    margin: 0;
    padding: 1.8em 0;

    letter-spacing: .025em;

    color: #fff;
}

.hero .front-content .footer p a
{
    font-weight: 500;

    color: inherit;
}

.hero .front-content .footer p.popup-links a
{
    margin: 0 .5em;
}

@media (max-width:750px)
{
    .hero .front-content .container-mid .phone-slider,
    .hero .front-content .container-mid img.logo
    {
        margin: 0 auto;
    }

    .hero .front-content .container-fluid
    {
        max-width: 460px;
        margin: 0 auto;
    }

    .hero .front-content .container-mid
    {
        text-align: center;
    }

    .hero .front-content .container-mid
    {
        position: relative;
        top: 0;
        left: 0;

        width: 100%;

        -webkit-transform: translateY(0%);
            -ms-transform: translateY(0%);
                transform: translateY(0%);
    }
}



/** 5. Hero --> Background Content 
*******************************************************************/

.hero .background-content
{
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;

    overflow: hidden;

    width: 100%;
    height: 100%;
}

.hero .background-content .background-content-inner
{
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 50%;

    width: 100%;
    height: 100%;

    -webkit-transform: translate3d(-50%, -50%, 0px) !important;
            transform: translate3d(-50%, -50%, 0px) !important;
}

.hero .background-content.parallax-on .background-content-inner
{
    width: 110%;
    height: 110%;
}

.hero .background-content .background-overlay
{
    position: absolute;
    z-index: 50;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;

    opacity: .9;
    background: #13cbe8;
}

.hero .background-content .background-img
{
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;

    background: url(../img/background.jpg);
    background-repeat: no-repeat;
    background-size: cover;
}



/** 6. Popup
*******************************************************************/

.featherlight .featherlight-content
{
    max-height: 80%;
    padding: 0;

    border-bottom: 0;
}

.featherlight .featherlight-content  .featherlight-close-icon
{
    font-size: 2.8rem;
    line-height: 1.4em;

    top: 1em;
    right: 1em;

    width: 1.4em;

    outline: none !important;
}

.featherlight .featherlight-content .popup
{
    padding: 8vh 10vw 6vh 10vw;

    text-align: left;
}

.featherlight .featherlight-content .popup .container-fluid
{
    max-width: 600px;
}

.featherlight .featherlight-content .popup h1,
.featherlight .featherlight-content .popup h2,
.featherlight .featherlight-content .popup h3,
.featherlight .featherlight-content .popup h4,
.featherlight .featherlight-content .popup h5,
.featherlight .featherlight-content .popup h6,
.featherlight .featherlight-content .popup p,
.featherlight .featherlight-content .popup a,
.featherlight .featherlight-content .popup span
{
    color: #111;
}

.featherlight .featherlight-content .popup h4
{
    margin-bottom: 2em;
}

.featherlight .featherlight-content .popup p
{
    font-size: 1.6rem;
    line-height: 1.5em;
}



/** 7. Popup --> FAQ
*******************************************************************/

.featherlight .featherlight-content .popup#popup-faq .item
{
    margin-bottom: 4.6rem;
}

.featherlight .featherlight-content .popup#popup-faq .item p.question
{
    font-weight: 500;
}

.featherlight .featherlight-content .popup#popup-faq .item p.answer
{
    font-weight: 300;
}



/** 8. Popup --> TOS and Privacy Policy
*******************************************************************/

.featherlight .featherlight-content .popup#popup-privacy-terms .item
{
    margin-bottom: 4.6rem;
}

.featherlight .featherlight-content .popup#popup-privacy-terms .item p.headline
{
    font-weight: 500;
}

.featherlight .featherlight-content .popup#popup-privacy-terms .item p.question
{
    font-weight: 300;
}