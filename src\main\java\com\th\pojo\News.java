package com.th.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.sql.Timestamp;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class News {
    private Integer id;
    private Integer companyId;
    private String title;
    private String content;
    private String remark;
    private String picture;
    private String catalog;
    private Timestamp createTime;
    private Timestamp updateTime;
}
