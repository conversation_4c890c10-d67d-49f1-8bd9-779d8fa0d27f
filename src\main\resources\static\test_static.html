<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态资源测试</title>
    <link href="/static/moban6072/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/moban6072/css/icofont.css" rel="stylesheet">
    <link href="/static/moban6072/css/style.css" rel="stylesheet">
    <style>
        .test-container {
            padding: 50px;
            text-align: center;
        }
        .test-item {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 moban6072 模板静态资源测试</h1>
        
        <div class="test-item">
            <h3>Bootstrap CSS 测试</h3>
            <div class="btn btn-primary">如果这个按钮有样式，说明Bootstrap CSS加载成功</div>
        </div>
        
        <div class="test-item">
            <h3>图标字体测试</h3>
            <i class="icofont-star"></i>
            <i class="icofont-heart-alt"></i>
            <i class="icofont-search"></i>
            <p>如果上面显示了星星、心形和搜索图标，说明图标字体加载成功</p>
        </div>
        
        <div class="test-item">
            <h3>主题样式测试</h3>
            <button class="btn-theme">主题按钮</button>
            <p>如果这个按钮有橙色背景，说明主题CSS加载成功</p>
        </div>
        
        <div class="test-item">
            <h3>图片测试</h3>
            <img src="/static/moban6072/picture/logo.webp" alt="Logo" style="max-width: 200px;">
            <p>如果上面显示了Logo图片，说明图片资源加载成功</p>
        </div>
        
        <div class="test-item">
            <h3>JavaScript测试</h3>
            <button onclick="testJS()" class="btn btn-success">测试JavaScript</button>
            <p id="js-result">点击按钮测试JavaScript是否正常工作</p>
        </div>
        
        <div class="test-item">
            <h3>测试结果</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script src="/static/moban6072/js/jquery-main.js"></script>
    <script src="/static/moban6072/js/bootstrap.min.js"></script>
    <script src="/static/moban6072/js/custom.js"></script>
    
    <script>
        function testJS() {
            document.getElementById('js-result').innerHTML = '✅ JavaScript工作正常！';
            document.getElementById('js-result').className = 'success';
        }
        
        // 页面加载完成后检查资源
        window.onload = function() {
            let results = [];
            
            // 检查CSS是否加载
            const testElement = document.createElement('div');
            testElement.className = 'btn-theme';
            document.body.appendChild(testElement);
            const styles = window.getComputedStyle(testElement);
            
            if (styles.backgroundColor === 'rgb(255, 94, 20)') {
                results.push('✅ 主题CSS加载成功');
            } else {
                results.push('❌ 主题CSS加载失败');
            }
            
            document.body.removeChild(testElement);
            
            // 检查jQuery是否加载
            if (typeof $ !== 'undefined') {
                results.push('✅ jQuery加载成功');
            } else {
                results.push('❌ jQuery加载失败');
            }
            
            // 显示结果
            document.getElementById('test-results').innerHTML = results.join('<br>');
        };
    </script>
</body>
</html>
