<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板管理 - 超级管理员</title>
    <link rel="stylesheet" href="/admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="/admin/css/font-awesome.min.css">
    <link rel="stylesheet" href="/admin/css/admin.css">
    <style>
        .template-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f9f9f9;
        }
        .template-preview {
            width: 100px;
            height: 60px;
            background: #e9ecef;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #6c757d;
        }
        .current-template {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .premium-badge {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .company-row:hover {
            background-color: #f8f9fa;
        }
        .batch-actions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="sidebar-header">
                    <h4>超级管理员</h4>
                </div>
                <ul class="nav nav-pills nav-stacked">
                    <li><a href="/Admin/index.html">系统概览</a></li>
                    <li class="active"><a href="/Admin/templateManagement.html">模板管理</a></li>
                    <li><a href="/Admin/log.html">系统日志</a></li>
                </ul>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <div class="page-header">
                    <h2>模板管理</h2>
                    <p class="text-muted">管理所有公司的网站模板</p>
                </div>

                <!-- 批量操作区域 -->
                <div class="batch-actions">
                    <h4>批量操作</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <select id="batchTemplate" class="form-control">
                                <option value="">选择模板</option>
                                <option th:each="template : ${templates}" 
                                        th:value="${template.templateCode}" 
                                        th:text="${template.templateName + (template.isPremium ? ' (付费)' : '')}">
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-primary" onclick="batchSetTemplate()">
                                批量设置
                            </button>
                        </div>
                        <div class="col-md-6">
                            <span class="text-muted">已选择 <span id="selectedCount">0</span> 个公司</span>
                        </div>
                    </div>
                </div>

                <!-- 模板预览区域 -->
                <div class="row">
                    <div class="col-md-12">
                        <h4>可用模板</h4>
                        <div class="row">
                            <div class="col-md-3" th:each="template : ${templates}">
                                <div class="template-card">
                                    <div class="template-preview">
                                        <span th:text="${template.templateName}">模板预览</span>
                                    </div>
                                    <h5 th:text="${template.templateName}">模板名称</h5>
                                    <p class="text-muted" th:text="${template.templateDescription}">模板描述</p>
                                    <div>
                                        <span th:if="${template.isPremium}" class="premium-badge">付费</span>
                                        <span th:unless="${template.isPremium}" class="badge badge-success">免费</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 公司列表 -->
                <div class="row">
                    <div class="col-md-12">
                        <h4>公司列表</h4>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        </th>
                                        <th>公司ID</th>
                                        <th>公司名称</th>
                                        <th>网站域名</th>
                                        <th>当前模板</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="company-row" th:each="company : ${pageInfo.list}">
                                        <td>
                                            <input type="checkbox" class="company-checkbox" 
                                                   th:value="${company.id}" onchange="updateSelectedCount()">
                                        </td>
                                        <td th:text="${company.id}">1</td>
                                        <td th:text="${company.companyName}">公司名称</td>
                                        <td th:text="${company.website}">www.example.com</td>
                                        <td>
                                            <span class="badge badge-info" 
                                                  th:text="${@templateService.getCurrentTemplateInfo(company.id)?.templateName ?: '默认模板'}">
                                                当前模板
                                            </span>
                                        </td>
                                        <td>
                                            <select class="form-control form-control-sm" 
                                                    th:id="'template_' + ${company.id}"
                                                    onchange="setCompanyTemplate(this)"
                                                    th:data-company-id="${company.id}">
                                                <option value="">选择模板</option>
                                                <option th:each="template : ${templates}" 
                                                        th:value="${template.templateCode}" 
                                                        th:text="${template.templateName + (template.isPremium ? ' (付费)' : '')}"
                                                        th:selected="${template.templateCode == @templateService.getCurrentTemplate(company.id)}">
                                                </option>
                                            </select>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav th:if="${pageInfo.pages > 1}">
                            <ul class="pagination">
                                <li th:class="${pageInfo.isFirstPage} ? 'disabled' : ''">
                                    <a th:href="@{/Admin/templateManagement.html(pageNo=${pageInfo.prePage})}">上一页</a>
                                </li>
                                <li th:each="nav : ${pageInfo.navigatepageNums}" 
                                    th:class="${nav == pageInfo.pageNum} ? 'active' : ''">
                                    <a th:href="@{/Admin/templateManagement.html(pageNo=${nav})}" th:text="${nav}">1</a>
                                </li>
                                <li th:class="${pageInfo.isLastPage} ? 'disabled' : ''">
                                    <a th:href="@{/Admin/templateManagement.html(pageNo=${pageInfo.nextPage})}">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/admin/js/jquery.min.js"></script>
    <script src="/admin/js/bootstrap.min.js"></script>
    <script>
        // 切换全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.company-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.company-checkbox:checked');
            document.getElementById('selectedCount').textContent = checkboxes.length;
        }

        // 设置单个公司模板
        function setCompanyTemplate(selectElement) {
            const companyId = selectElement.getAttribute('data-company-id');
            const templateCode = selectElement.value;
            
            if (!templateCode) return;

            $.ajax({
                url: '/Admin/setCompanyTemplate',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    companyId: companyId,
                    templateCode: templateCode
                }),
                success: function(response) {
                    if (response.status) {
                        alert('模板设置成功！');
                        location.reload();
                    } else {
                        alert('设置失败：' + response.msg);
                    }
                },
                error: function() {
                    alert('网络错误，请重试');
                }
            });
        }

        // 批量设置模板
        function batchSetTemplate() {
            const templateCode = document.getElementById('batchTemplate').value;
            const checkboxes = document.querySelectorAll('.company-checkbox:checked');
            
            if (!templateCode) {
                alert('请选择模板');
                return;
            }
            
            if (checkboxes.length === 0) {
                alert('请选择要设置的公司');
                return;
            }

            const companyIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
            
            if (confirm(`确定要为 ${companyIds.length} 个公司设置模板吗？`)) {
                $.ajax({
                    url: '/Admin/batchSetTemplate',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        templateCode: templateCode,
                        companyIds: companyIds
                    }),
                    success: function(response) {
                        alert(response.msg);
                        if (response.status) {
                            location.reload();
                        }
                    },
                    error: function() {
                        alert('网络错误，请重试');
                    }
                });
            }
        }
    </script>
</body>
</html>
