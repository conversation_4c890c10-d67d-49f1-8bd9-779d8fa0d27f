<html>
<head>
<!-- 请再head中加入itsoms.min.js -->
<script type="text/javascript" src="http://gw.goto56.com/static/index/itsoms.min.js"></script>

<script>
    $(document).ready(function() {

        /**
         * 页面加载后可立即调用initOms()
         * 
         * 绑定订单系统
         * @param url 订单系统的访问地址
         * @param companyId 企业编号
         */
        initOms('http://hlt.oms.goto56.com', '1530066066670354434')
    });
</script>
</head>

<body>

    <!-- Start 查询轨迹，此代码段html代码块插入到body中合适的地方 Start -->
    <div class="col-lg-5 col-sm-5" style="background-color: #1c4264">
        <section>
            <div style="padding-top: 12px; color:#fff">
                <div style="float: left; ">
                    <i class="iconfont fa fa-truck fa-lg"> 查询轨迹</i>
                </div>
                <div style="float: right; padding-bottom: 3px;">
                    <button type="button" class="btn btn-default" aria-label="Left Align" onclick="queryTrack()">
                        <i class="iconfont fa fa-search fa-lg"></i> 查询
                    </button>
                </div>
            </div>
            <form role="form">
                <div class="form-group">
                    <textarea id="nos" class="form-control" rows="6" placeholder="可查询多个运单号，换行隔开，最多100个运单号"></textarea>
                </div>
            </form>
        </section>
    </div>
    <!-- End -->

</body>
</html>