<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{index/fragment/common :: head}"></div>
<style>
/* 创意风格模板样式 */
body {
    background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    font-family: 'Comic Sans MS', cursive, sans-serif;
}
.creative-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}
.creative-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,255,255,0.1) 10px,
        rgba(255,255,255,0.1) 20px
    );
    animation: move 20s linear infinite;
}
@keyframes move {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
.creative-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    transform: rotate(-1deg);
    transition: all 0.3s ease;
}
.creative-card:nth-child(even) {
    transform: rotate(1deg);
}
.creative-card:hover {
    transform: rotate(0deg) scale(1.02);
}
</style>
<body>
    <!-- 创意风格头部 -->
    <div class="creative-header">
        <div class="container" style="position: relative; z-index: 1;">
            <h1>🎨 创意风格模板</h1>
            <p>充满想象力和创造力的设计风格</p>
            <h2 th:text="${configs.get('website_company').value}">公司名称</h2>
        </div>
    </div>

    <div class="container mt">
        <div class="row">
            <!--<div class="col-lg-6 col-sm-6">-->
                <!--<section>-->
                    <!--<a href="javascript:void(0)">-->
                        <!--<div class="f-box">-->
                            <!--<i class="iconfont fa fa-database"></i>-->
                            <!--<h2>医疗器械</h2>-->
                            <!--<p class="yw">medical equipment</p>-->
                            <!--<u></u>-->
                            <!--<p class="f-text">致力于医疗器械工程和服务，努力成为业内顶级的医疗器械工程和服务提供商</p>-->
                        <!--</div>-->
                    <!--</a>-->
                <!--</section>-->
            <!--</div>-->
            <div th:class="${configs.get('forbid_show_price').value ne '1' ? 'col-lg-5 col-sm-5' : 'col-lg-12 col-sm-12'}" style="background-color: #1c4264;">
                <section>
                    <div style="padding-top: 12px; color:#fff">
                        <div style="float: left; ">
                            <i class="iconfont fa fa-truck fa-lg"> 查询轨迹</i>
                        </div>
                        <div style="float: right; padding-bottom: 3px;">
                            <button type="button" class="btn btn-default" aria-label="Left Align" onclick="queryTrack()">
                                <i class="iconfont fa fa-search fa-lg"></i> 查询
                            </button>
                        </div>
                    </div>
                    <form role="form">
                        <div class="form-group">
                            <textarea id="nos" class="form-control" rows="4" placeholder="可查询多个运单号，换行隔开，最多100个运单号"></textarea>
                        </div>
                        <div style="height:40px;">
<!--                            <div style="float: left; background-color: #fff;">-->
<!--                                <img src="/static/index/img/17track-logo.png" width="100" height="30" alt="image" @click="queryTrack()">-->
<!--                            </div>-->
                            <div style="float: left; background-color: #fff;">
                                <button type="button" onclick="queryTrackFrom17track()" >
                                    <img src="/static/index/img/17track-logo.png" width="100" height="30" alt="image">
                                </button>
                            </div>
                            <div style="float: right; background-color: #fff;">
                                <button type="button" onclick="queryTrackFromTrack718()">
                                    <img class="float: left; " src="/static/index/img/track718-logo.png" width="100" height="30" alt="image">
                                </button>
                            </div>
                        </div>
                    </form>
                </section>
            </div>

            <div th:if="${configs.get('forbid_show_price').value ne '1'}" class="col-lg-6 col-sm-6 col-lg-offset-1" style="background-color: #1c4264;">
                <section style=" color:#fff" >
                    <div style="padding-top: 12px;">
                        <div style="float: left;">
                            <i class="iconfont fa fa-calculator fa-lg"> 查询运费</i>
                        </div>
                        <div style="float: right; padding-bottom: 3px;">
                            <button type="button" class="btn btn-primary" aria-label="Left Align" onclick="queryPrice()">
                                <i class="iconfont fa fa-search fa-lg"></i> 查询
                            </button>
                        </div>
                        <div style="clear: both;"></div>
                    </div>
                    <form class="form-horizontal" style="background-color: rgba(197,197,197,0.09)">
                        <div class="form-group">
                            <label for="country" class="control-label col-md-3">目的地：</label>
                            <div class="col-md-9">
                                <select class="form-control select2" name="country" id="country" data-placeholder="请选择国家">
                                    <option value=""></option><option evalue="ANDORRA" value="AD">[AD]安道尔</option><option evalue="UNITED ARAB EMIRATES" value="AE">[AE]阿联酋</option><option evalue="AFGHANISTAN" value="AF">[AF]阿富汗</option><option evalue="ANTIGUA AND BARBUDA" value="AG">[AG]安提瓜及巴布达</option><option evalue="ANGUILLA" value="AI">[AI]安圭拉岛</option><option evalue="ALBANIA" value="AL">[AL]阿尔巴尼亚</option><option evalue="ARMENIA" value="AM">[AM]亚美尼亚</option><option evalue="NETHERLANDS ANTILLES" value="AN">[AN]荷属安的列斯群岛</option><option evalue="ANGOLA" value="AO">[AO]安哥拉</option><option evalue="ANTARCTICA" value="AQ">[AQ]南极</option><option evalue="ARGENTINA" value="AR">[AR]阿根廷</option><option evalue="SAMOA,USA TERRITORY" value="AS">[AS]美属萨摩亚</option><option evalue="AUSTRIA" value="AT">[AT]奥地利</option><option evalue="AUSTRALIA" value="AU">[AU]澳大利亚</option><option evalue="ARUBA" value="AW">[AW]阿鲁巴</option><option evalue="Azerbaijan" value="AZ">[AZ]阿塞拜疆</option><option evalue="BOSNIA AND HERZEGOVINA" value="BA">[BA]波斯尼亚-黑塞哥维那</option><option evalue="BARBADOS" value="BB">[BB]巴巴多斯</option><option evalue="BANGLADESH" value="BD">[BD]孟加拉国</option><option evalue="BELGIUM" value="BE">[BE]比利时</option><option evalue="BURKINA FASO" value="BF">[BF]布基纳法索</option><option evalue="BULGARIA" value="BG">[BG]保加利亚</option><option evalue="BAHRAIN" value="BH">[BH]巴林</option><option evalue="BURUNDI" value="BI">[BI]布隆迪</option><option evalue="BENIN" value="BJ">[BJ]贝宁</option><option evalue="BERMUDA" value="BM">[BM]百慕达</option><option evalue="BRUNEI DARUSSALAM" value="BN">[BN]文莱</option><option evalue="BOLIVIA" value="BO">[BO]玻利维亚</option><option evalue="BRAZIL" value="BR">[BR]巴西</option><option evalue="BAHAMAS" value="BS">[BS]巴哈马</option><option evalue="BHUTAN" value="BT">[BT]不丹</option><option evalue="BOUVET ISLAND" value="BV">[BV]布维岛</option><option evalue="BOTSWANA" value="BW">[BW]博茨瓦纳</option><option evalue="BELARUS" value="BY">[BY]白俄罗斯</option><option evalue="BELIZE" value="BZ">[BZ]伯利兹</option><option evalue="CANADA" value="CA">[CA]加拿大</option><option evalue="COCOS(KEELING) ISLANDS" value="CC">[CC]科科斯群岛</option><option evalue="CONGO(DEM.REP.OF)" value="CD">[CD]刚果(民主共和国)</option><option evalue="CENTRAL AFRICAN REPUBLIC" value="CF">[CF]中非共和国</option><option evalue="CONGO(REP.OF)" value="CG">[CG]刚果</option><option evalue="SWITZERLAND" value="CH">[CH]瑞士</option><option evalue="COTE D’IVOIRE" value="CI">[CI]科特迪瓦(象牙海岸)</option><option evalue="COOK ISLANDS" value="CK">[CK]库克群岛</option><option evalue="CHILE" value="CL">[CL]智利</option><option evalue="CAMEROON" value="CM">[CM]喀麦隆</option><option evalue="COLOMBIA" value="CO">[CO]哥伦比亚</option><option evalue="Costa Rica" value="CR">[CR]哥斯达黎加</option><option evalue="CUBA" value="CU">[CU]古巴</option><option evalue="CAPE VERDE" value="CV">[CV]佛得角群岛</option><option evalue="CHRISTMAS ISLAND" value="CX">[CX]圣诞岛</option><option evalue="CYPRUS" value="CY">[CY]塞浦路斯</option><option evalue="CZECH REPUBLIC" value="CZ">[CZ]捷克</option><option evalue="DUBAI" value="DB">[DB]迪拜</option><option evalue="GERMANY" value="DE">[DE]德国</option><option evalue="DJIBOUTI" value="DJ">[DJ]吉布提</option><option evalue="DENMARK" value="DK">[DK]丹麦</option><option evalue="DOMINICA" value="DM">[DM]多米尼克</option><option evalue="DOMINICAN REPUBLIC" value="DO">[DO]多米尼加共和国</option><option evalue="ALGERIA" value="DZ">[DZ]阿尔及利亚</option><option evalue="ECUADOR" value="EC">[EC]厄瓜多尔</option><option evalue="ESTONIA" value="EE">[EE]爱沙尼亚</option><option evalue="EGYPT" value="EG">[EG]埃及</option><option evalue="WESTERN SAHARA" value="EH">[EH]西撒哈拉</option><option evalue="ERITREA" value="ER">[ER]厄立特里亚</option><option evalue="SPAIN" value="ES">[ES]西班牙</option><option evalue="ETHIOPIA" value="ET">[ET]埃塞俄比亚</option><option evalue="FINLAND" value="FI">[FI]芬兰</option><option evalue="FIJI" value="FJ">[FJ]斐济</option><option evalue="FALKLAND ISLAND(MALVINAS)" value="FK">[FK]福克兰群岛</option><option evalue="MICRONESIA" value="FM">[FM]密克罗尼西亚</option><option evalue="FAROE ISLANDS" value="FO">[FO]法罗群岛</option><option evalue="FRANCE" value="FR">[FR]法国</option><option evalue="FRANCE,METROPOLITAN" value="FX">[FX]法属美特罗波利坦</option><option evalue="GABON" value="GA">[GA]加蓬</option><option evalue="UNITED KINGDOM" value="GB">[GB]英国</option><option evalue="Grenada" value="GD">[GD]格林纳达</option><option evalue="GEORGIA" value="GE">[GE]格鲁吉亚</option><option evalue="FRENCH GUIANA" value="GF">[GF]法属圭亚那</option><option evalue="GUERNSEY" value="GG">[GG]根西岛</option><option evalue="GHANA" value="GH">[GH]加纳</option><option evalue="GIBRALTAR" value="GI">[GI]直布罗陀</option><option evalue="GREENLAND" value="GL">[GL]格陵兰岛</option><option evalue="GAMBIA" value="GM">[GM]冈比亚</option><option evalue="GUINEA REPUBLIC" value="GN">[GN]几内亚</option><option evalue="GUADELOUPE" value="GP">[GP]瓜德罗普</option><option evalue="EQUATORIAL GUINEA" value="GQ">[GQ]赤道几内亚</option><option evalue="GREECE" value="GR">[GR]希腊</option><option evalue="SOUTH GEORGIA AND THE SOUTH SANDWICH ISL" value="GS">[GS]南乔治亚岛和南桑德韦奇岛</option><option evalue="Guatemala" value="GT">[GT]危地马拉</option><option evalue="Guam" value="GU">[GU]关岛</option><option evalue="GUINEA-BISSAU" value="GW">[GW]几内亚比绍</option><option evalue="GUYANA (BRITISH)" value="GY">[GY]圭亚那</option><option evalue="HONG KONG" value="HK">[HK]中国香港</option><option evalue="HEARD ISLAND AND MCDONALD ISLANDS" value="HM">[HM]赫德岛和麦克唐岛</option><option evalue="Honduras" value="HN">[HN]洪都拉斯</option><option evalue="CROATIA" value="HR">[HR]克罗地亚</option><option evalue="Haiti" value="HT">[HT]海地</option><option evalue="HUNGARY" value="HU">[HU]匈牙利</option><option evalue="INDONESIA" value="ID">[ID]印度尼西亚</option><option evalue="IRELAND" value="IE">[IE]爱尔兰</option><option evalue="ISRAEL" value="IL">[IL]以色列</option><option evalue="INDIA" value="IN">[IN]印度</option><option evalue="BRITISH INDIAN OCEAN TERRITORY" value="IO">[IO]英属印度洋地区</option><option evalue="IRAQ" value="IQ">[IQ]伊拉克</option><option evalue="IRAN(ISLAMIC REPUBLIC OF)" value="IR">[IR]伊朗</option><option evalue="ICELAND" value="IS">[IS]冰岛</option><option evalue="ITALY" value="IT">[IT]意大利</option><option evalue="JAMAICA" value="JM">[JM]牙买加</option><option evalue="JORDAN" value="JO">[JO]约旦</option><option evalue="JAPAN" value="JP">[JP]日本</option><option evalue="KENYA" value="KE">[KE]肯尼亚</option><option evalue="KYRGYZSTAN" value="KG">[KG]吉尔吉斯斯坦</option><option evalue="CAMBODIA" value="KH">[KH]柬埔寨</option><option evalue="KIRIBATI" value="KI">[KI]基里巴斯</option><option evalue="COMOROS" value="KM">[KM]科摩罗</option><option evalue="SAINT KITTS AND NEVIS" value="KN">[KN]圣基茨和尼维斯</option><option evalue="KOREA,DEMOCRATIC PEOPLE'S REPUBLIC OF" value="KP">[KP]北韩</option><option evalue="KOREA,REPUBLIC OF" value="KR">[KR]韩国</option><option evalue="Republic of Ivory Co" value="KT">[KT]科特迪瓦共和国</option><option evalue="KUWAIT" value="KW">[KW]科威特</option><option evalue="CAYMAN ISLANDS" value="KY">[KY]开曼群岛</option><option evalue="KAZAKHSTAN" value="KZ">[KZ]哈萨克斯坦</option><option evalue="LAO PEOPLE'S DEMOCRATIC REPUBLIC" value="LA">[LA]老挝</option><option evalue="LEBANON" value="LB">[LB]黎巴嫩</option><option evalue="SAINT LUCIA" value="LC">[LC]圣卢西亚</option><option evalue="LIECHTENSTEIN" value="LI">[LI]列支敦士登</option><option evalue="SRI LANKA" value="LK">[LK]斯里兰卡</option><option evalue="LIBERIA" value="LR">[LR]利比里亚</option><option evalue="LESOTHO" value="LS">[LS]莱索托</option><option evalue="LITHUANIA" value="LT">[LT]立陶宛</option><option evalue="LUXEMBOURG" value="LU">[LU]卢森堡</option><option evalue="LATVIA" value="LV">[LV]拉脱维亚</option><option evalue="LIBYA" value="LY">[LY]利比亚</option><option evalue="MOROCCO" value="MA">[MA]摩洛哥</option><option evalue="Monaco" value="MC">[MC]摩纳哥</option><option evalue="Moldova,Republic of" value="MD">[MD]摩尔多瓦</option><option evalue="MONTENEGRO" value="ME">[ME]黑山</option><option evalue="MADAGASCAR" value="MG">[MG]马达加斯加</option><option evalue="MARSHALL ISLANDS" value="MH">[MH]马绍尔群岛</option><option evalue="MACEDONIA(REP.OF)(FORMER YOGOSLAVIA)" value="MK">[MK]马其顿</option><option evalue="MALI" value="ML">[ML]马里</option><option evalue="MYANMAR" value="MM">[MM]缅甸</option><option evalue="MONGOLIA" value="MN">[MN]蒙古</option><option evalue="MACAU" value="MO">[MO]中国澳门</option><option evalue="MARIANA ISLANDS" value="MP">[MP]马里亚纳群岛</option><option evalue="MARTINIQUE" value="MQ">[MQ]马提尼克</option><option evalue="MAURITANIA" value="MR">[MR]毛里塔尼亚</option><option evalue="MONTSERRAT" value="MS">[MS]蒙特塞拉特</option><option evalue="MALTA" value="MT">[MT]马耳他</option><option evalue="MAURITIUS" value="MU">[MU]毛里求斯</option><option evalue="Maldives" value="MV">[MV]马尔代夫</option><option evalue="MALAWI" value="MW">[MW]马拉维</option><option evalue="MEXICO" value="MX">[MX]墨西哥</option><option evalue="MALAYSIA" value="MY">[MY]马来西亚</option><option evalue="MOZAMBIQUE" value="MZ">[MZ]莫桑比克</option><option evalue="NAMIBIA" value="NA">[NA]纳米比亚</option><option evalue="NEW CALEDONIA" value="NC">[NC]新喀里多尼亚</option><option evalue="NIGER" value="NE">[NE]尼日尔</option><option evalue="NORFOLK ISLAND" value="NF">[NF]诺福克岛</option><option evalue="NIGERIA" value="NG">[NG]尼日利亚</option><option evalue="NICARAGUA" value="NI">[NI]尼加拉瓜</option><option evalue="NETHERLANDS" value="NL">[NL]荷兰</option><option evalue="NORWAY" value="NO">[NO]挪威</option><option evalue="NEPAL" value="NP">[NP]尼泊尔</option><option evalue="Nauru" value="NR">[NR]瑙鲁</option><option evalue="NIUE" value="NU">[NU]纽埃</option><option evalue="NEW ZEALAND" value="NZ">[NZ]新西兰</option><option evalue="OMAN" value="OM">[OM]阿曼</option><option evalue="PANAMA" value="PA">[PA]巴拿马</option><option evalue="PERU" value="PE">[PE]秘鲁</option><option evalue="FRENCH POLYNESIA" value="PF">[PF]法属波利尼西亚</option><option evalue="PAPUA NEW GUINEA" value="PG">[PG]巴布亚新几内亚</option><option evalue="PHILIPPINES" value="PH">[PH]菲律宾</option><option evalue="PAKISTAN" value="PK">[PK]巴基斯坦</option><option evalue="POLAND" value="PL">[PL]波兰</option><option evalue="SAINT PIERRE AND MIQUELON" value="PM">[PM]圣皮埃尔和密克隆群岛</option><option evalue="PITCAIRN" value="PN">[PN]皮特凯恩岛</option><option evalue="PUERTO RICO" value="PR">[PR]波多黎各</option><option evalue="Palestine State" value="PS">[PS]巴勒斯坦</option><option evalue="PORTUGAL" value="PT">[PT]葡萄牙</option><option evalue="PARAGUAY" value="PY">[PY]巴拉圭</option><option evalue="QATAR" value="QA">[QA]卡塔尔</option><option evalue="REUNION" value="RE">[RE]留尼汪岛</option><option evalue="ROMANIA" value="RO">[RO]罗马尼亚</option><option evalue="SERBIA (Republic of)" value="RS">[RS]塞尔维亚</option><option evalue="RUSSIAN FEDERATION" value="RU">[RU]俄罗斯</option><option evalue="RWANDA" value="RW">[RW]卢旺达</option><option evalue="SAUDI ARABIA" value="SA">[SA]沙特阿拉伯</option><option evalue="SOLOMON ISLANDS" value="SB">[SB]所罗门群岛</option><option evalue="SEYCHELLES" value="SC">[SC]塞舌尔</option><option evalue="SUDAN" value="SD">[SD]苏丹</option><option evalue="SWEDEN" value="SE">[SE]瑞典</option><option evalue="SINGAPORE" value="SG">[SG]新加坡</option><option evalue="SAINT HELENA" value="SH">[SH]圣赫勒拿岛</option><option evalue="SLOVENIA" value="SI">[SI]斯洛文尼亚</option><option evalue="SPITSBERGEN(SVALBARD)" value="SJ">[SJ]斯匹次卑尔根群岛</option><option evalue="SLOVAKIA REPUBLIC (Slovakia)" value="SK">[SK]斯洛伐克</option><option evalue="SIERRA LEONE" value="SL">[SL]塞拉里昂</option><option evalue="SAN MARINO" value="SM">[SM]圣马力诺</option><option evalue="SENEGAL" value="SN">[SN]塞内加尔</option><option evalue="SOMALIA" value="SO">[SO]索马里</option><option evalue="SURINAME" value="SR">[SR]苏里南</option><option evalue="SAO TOME AND PRINCIPE" value="ST">[ST]圣多美和普林西比</option><option evalue="EL SALVADOR" value="SV">[SV]萨尔瓦多</option><option evalue="SYRIAN ARAB REPUBLIC" value="SY">[SY]阿拉伯叙利亚共和国(叙利亚)</option><option evalue="SWAZILAND" value="SZ">[SZ]斯威士兰</option><option evalue="TURKS AND CAICOS ISLANDS" value="TC">[TC]特克斯和凯科斯群岛</option><option evalue="CHAD" value="TD">[TD]乍得</option><option evalue="FRENCH SOUTHERN TERRITORIES" value="TF">[TF]法属南部领土</option><option evalue="TOGO" value="TG">[TG]多哥</option><option evalue="THAILAND" value="TH">[TH]泰国</option><option evalue="TAJIKISTAN" value="TJ">[TJ]塔吉克</option><option evalue="TOKELAU" value="TK">[TK]托克劳</option><option evalue="TURKMENISTAN" value="TM">[TM]土库曼</option><option evalue="TUNISIA" value="TN">[TN]突尼斯</option><option evalue="TONGA" value="TO">[TO]汤加</option><option evalue="EAST TIMOR" value="TL">[TL]东帝汶</option><option evalue="TURKEY" value="TR">[TR]土耳其</option><option evalue="TRINIDAD AND TOBAGO" value="TT">[TT]千里达和多巴哥</option><option evalue="TUVALU" value="TV">[TV]图瓦卢</option><option evalue="TAIWAN" value="TW">[TW]中国台湾</option><option evalue="TANZANIA" value="TZ">[TZ]坦桑尼亚</option><option evalue="UKRAINE" value="UA">[UA]乌克兰</option><option evalue="UGANDA" value="UG">[UG]乌干达</option><option evalue="UNITED STATES MINOR OUTLYING ISLANDS" value="UM">[UM]美国本土外小岛屿</option><option evalue="UNITED STATES" value="US">[US]美国</option><option evalue="URUGUAY" value="UY">[UY]乌拉圭</option><option evalue="Uzbekistan" value="UZ">[UZ]乌兹别克斯坦</option><option evalue="VATICAN CITY STATE(HOLY SEE)" value="VA">[VA]梵蒂冈</option><option evalue="SAINT VINCENT AND THE GRENADINES" value="VC">[VC]圣文森特和格林纳丁斯</option><option evalue="VENEZUELA" value="VE">[VE]委内瑞拉</option><option evalue="TORTOLA(BRITISH VIRGIN ISLANDS)" value="VG">[VG]托尔托拉岛(英属处女群岛)</option><option evalue="VIRGIN ISLANDS (US)" value="VI">[VI]美属处女群岛</option><option evalue="VIETNAM" value="VN">[VN]越南</option><option evalue="VANUATU" value="VU">[VU]瓦努阿图</option><option evalue="WALLIS AND FUTUNA ISLANDS" value="WF">[WF]瓦利斯群岛和富图纳群岛</option><option evalue="SAMOA,WESTERN" value="WS">[WS]西萨摩亚</option><option evalue="CANARY ISLANDS" value="XA">[XA]加那利群岛</option><option evalue="TRISTAN DA CUNHA" value="XB">[XB]特里斯坦-达库尼亚岛</option><option evalue="Channel Islands" value="XC">[XC]海峡群岛</option><option evalue="Ascension Island" value="XD">[XD]阿森松</option><option evalue="GAZA AND KHAN YUNIS" value="XE">[XE]加沙及汗尤尼斯</option><option evalue="CORSICA" value="XF">[XF]科西嘉岛</option><option evalue="SPANISH TERRITORIES OF N.AFRICA" value="XG">[XG]北非西班牙属土</option><option evalue="AZORES" value="XH">[XH]亚速尔</option><option evalue="MADEIRA" value="XI">[XI]马德拉</option><option evalue="BALEARIC ISLANDS" value="XJ">[XJ]巴利阿里群岛</option><option evalue="CAROLINE ISLANDS" value="XK">[XK]帕劳</option><option evalue="New Zealand Islands Territories" value="XL">[XL]新西兰属土岛屿(库克群岛)</option><option evalue="WAKE ISLAND" value="XM">[XM]威克岛</option><option evalue="KOSOVO" value="KV">[KV]科索沃</option><option evalue="YEMEN(REPUBLIC OF)" value="YE">[YE]也门</option><option evalue="Mayotte" value="YT">[YT]马约特</option><option evalue="YUGOSLAVIA" value="YU">[YU]南斯拉夫</option><option evalue="SOUTH AFRICA" value="ZA">[ZA]南非</option><option evalue="ZAMBIA" value="ZM">[ZM]赞比亚</option><option evalue="ZIMBABWE" value="ZW">[ZW]津巴布韦</option><option evalue="CHINA" value="CN">[CN]中国</option><option evalue="ALAND ISLANDS" value="AX">[AX]奥兰群岛</option><option evalue="SAINT BARTHÉLEMY" value="BL">[BL]圣巴泰勒米岛</option><option evalue="ISLE OF MAN" value="IM">[IM]马恩岛</option><option evalue="JERSEY" value="JE">[JE]泽西岛</option><option evalue="SAINT MARTIN" value="MF">[MF]圣马丁</option><option evalue="PALAU" value="PW">[PW]帛琉(帕劳)</option><option evalue="NEVIS" value="XN">[XN]尼维斯</option><option evalue="SOMALILAND" value="XS">[XS]索马里兰</option><option evalue="ST. BARTHELEMY" value="XY">[XY]圣巴特勒米岛</option><option evalue="SOUTH SUDAN" value="SS">[SS]南苏丹</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="postcode" class="control-label col-md-3">邮编：</label>
                            <div class="col-md-9">
                                <input class="form-control" name="postcode" id="postcode" placeholder="邮编">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="weightD" class="control-label col-md-3">重量(KG)：</label>
                            <div class="col-md-9 input-group" style="padding-right: 15px;">
                                <input type="text" class="form-control" name="weight" id="weightD" placeholder="重量(KG)">
                                <span class="input-group-addon"></span>
                                <input class="form-control" style="width:65px;" name="length" id="lengthD" placeholder="长(cm)">
                                <span class="input-group-addon">✖️</span>
                                <input class="form-control" style="width:65px;" name="width" id="widthD" placeholder="宽(cm)">
                                <span class="input-group-addon">✖️</span>
                                <input class="form-control" style="width:65px;" name="height" id="heightD" placeholder="高(cm)">
                            </div>
                        </div>
                    </form>
                </section>
            </div>
        </div>
    </div>

    <!-- 渠道 -->
    <div class="container">
        <div class="biaoti">
            <h2>渠道</h2>
            <!--<p>系统集成和运维外包在内的整体服务方案</p>-->
            <!--<div class="english"><span>CASE</span></div>-->
        </div>
    </div>

    <div class="line"></div>

    <div class="container mt">
        <div class="row">
            <div class="col-xs-12 col-sm-4 col-md-4" th:each="project : ${projects}">
                <div class="recent-work-wrap">
                    <a th:href="'/Project/projectDetail/id/' + ${project.id}">
                        <img class="img-responsive" th:src="'/UploadFilePath/project/' + ${project.picture}" ondragstart="return false" th:alt="${project.title}">
                        <div class="overlay">
                            <div class="recent-work-inner">
                                <h3 th:text="${project.title}"/>
                                <p th:text="${project.remark}"/>
                            </div>
                        </div>
                    </a>
                    <h4 th:text="${project.title}" style="text-align: center; color: #343434"/>

                </div>
            </div>
        </div>
        <a href="/Project/projects.html" class="more">查看更多</a>
    </div>

    <!-- 合作伙伴 -->
    <div class="container mt">
        <div class="biaoti">
            <h2>合作伙伴</h2>
            <p>价值创新，创造双赢</p>
            <!--<div class="english"><span>Partner</span></div>-->
        </div>
    </div>

    <div class="container partner mt">
        <div class="row">
            <div class="col-lg-2 col-md-2 col-xs-4" th:each="partner : ${partners}">
                <a th:href="${partner.url}" target="_blank">
                    <img th:src="'/UploadFilePath/partner/' + ${partner.picture}" th:title="${partner.company}" ondragstart="return false"/>
                </a>
            </div>
        </div>
    </div>

    <!-- 地址与留言 -->
    <div class="add mt">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-3 col-xs-12">
                    <h2>我们的地址：</h2>
                    <p th:text="${configs.get('website_address').value}"/>
                    <h2 class="contact">联系方式：</h2>
                    <p th:text="'电话：' + ${configs.get('website_phone').value}"/>
                    <p th:text="'传真：' + ${configs.get('website_fax').value}"/>
                    <p th:text="'邮箱：' + ${configs.get('website_email').value}"/>
                    <p th:text="'联系人：' + ${configs.get('website_admin').value}"/>
                </div>
                <div class="col-lg-4 col-md-3 col-xs-12">

                </div>
                <div class="col-lg-4 col-md-3 col-xs-12">
                    <h2>留言</h2>
                    <form method="post" class="liuyan" action="javascript:void(0)">
                        <div class="form-group">
                            <label for="exampleInputEmail1">姓名</label>
                            <input type="text" class="form-control" id="exampleInputEmail1" placeholder="">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">电话</label>
                            <input type="password" class="form-control" id="exampleInputPassword1" placeholder="">
                        </div>
                        <label for="exampleInputPassword1">给我们留言</label>
                        <textarea class="form-control" rows="3"></textarea>
                        <button type="submit" class="btn btn-default">提交留言</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚与备案 -->
    <div th:replace="~{index/fragment/common :: copyright}"></div>
    <input type="hidden" id="omsCompanyId" th:value="${configs.get('oms_company_id').value}">
    <input type="hidden" id="omsUrl" th:value="${configs.get('oms_url').value}">
</body>

<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
    function queryTrack() {
        var nos = $("#nos").val()
        nos = nos.replace(/\n|\s+/g, ',').trim()
        if (nos === '') {
            alert('请输入运单号')
            return
        }
        var omsUrl = $('#omsUrl').val()
        var omsCompanyId = $('#omsCompanyId').val()

        url = omsUrl + '/guest-track?companyId=' + omsCompanyId + '&nos=' + nos
        window.open(url, '_blank')
    }

    function queryTrackFrom17track() {
        var nos = $("#nos").val()
        nos = nos.replace(/\n|\s+/g, ',').trim()
        if (nos === '') {
            alert('请输入运单号')
            return
        }
        url = "https://t.17track.net/zh-cn#nums=" + nos
        window.open(url, '_blank')
    }

    function queryTrackFromTrack718() {
        var nos = $("#nos").val()
        nos = nos.replace(/\n|\s+/g, ',').trim()
        if (nos === '') {
            alert('请输入运单号')
            return
        }
        url = "https://www.track718.com/zh-CN/detail?nums=" + nos
        window.open(url, '_blank')
    }

    function queryPrice() {
        var country = $("#country").val()
        var postcode = $("#postcode").val()
        var weightD = $("#weightD").val()
        var lengthD = $("#lengthD").val()
        var widthD = $("#widthD").val()
        var heightD = $("#heightD").val()

        var omsUrl = $('#omsUrl').val()
        var omsCompanyId = $('#omsCompanyId').val()
        url = omsUrl + '/guest-price-calculate?companyId=' + omsCompanyId
            + '&country=' + country
            + '&postcode=' + postcode
            + '&weightD=' + weightD
            + '&lengthD=' + lengthD
            + '&widthD=' + widthD
            + '&heightD=' + heightD
        window.open(url, '_blank')
    }
</script>

</html>