<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>

<link rel="stylesheet" href="/static/admin/simditor/css/simditor.css">
<script src="/static/admin/simditor/js/module.min.js"></script>
<script src="/static/admin/simditor/js/hotkeys.min.js"></script>
<script src="/static/admin/simditor/js/uploader.min.js"></script>
<script src="/static/admin/simditor/js/simditor.min.js"></script>
<script src="/static/admin/simditor/js/jquery.form.js"></script>
<style>
    .m{ width: 100%; margin-left: auto; margin-right: auto; }
    .note-editor.note-frame {border: 1px solid #eee!important;}
    .note-editor .note-editing-area {min-height: 500px;}
</style>

<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('系统管理', '系统设置')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">

            <div class="container-fluid">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <ul class="nav nav-tabs page-tabs">
                                <li class="active"> <a href="javascript:void(0)">基本</a> </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <div class="edit-form">
                                        <div class="form-group">
                                            <label>
                                                <input type="checkbox" th:if="${configs.get('forbid_show_price').value eq '1'}" id="forbid_show_price" checked="checked" value="1"/>
                                                <input type="checkbox" th:if="${configs.get('forbid_show_price').value ne '1'}" id="forbid_show_price" value="0">
                                                禁止显示查询运费
                                            </label>
                                        </div>
                                        <div class="form-group">
                                            <label>轮播图1（点击上传图片）</label>
                                            <img id="previewImg1" style="cursor: pointer;" th:src="'/UploadFilePath/banner/' + ${configs.get('website_banner1').value}" height="250">
                                            <input class="form-control" type="hidden" id="website_banner1" th:value="${configs.get('website_banner1').value}" placeholder="轮播图1" autocomplete="off">
                                        </div>
                                        <div class="form-group">
                                            <label>轮播图2（点击上传图片）</label>
                                            <img id="previewImg2" style="cursor: pointer;" th:src="'/UploadFilePath/banner/' + ${configs.get('website_banner2').value}" height="250">
                                            <input class="form-control" type="hidden" id="website_banner2" th:value="${configs.get('website_banner2').value}" placeholder="轮播图2" autocomplete="off">
                                        </div>
                                        <div class="form-group">
                                            <label>轮播图3（点击上传图片）</label>
                                            <img id="previewImg3" style="cursor: pointer;" th:src="'/UploadFilePath/banner/' + ${configs.get('website_banner3').value}" height="250">
                                            <input class="form-control" type="hidden" id="website_banner3" th:value="${configs.get('website_banner3').value}" placeholder="轮播图3" autocomplete="off">
                                        </div>

                                        <div class="form-group">
                                            <button class="btn btn-primary m-r-5 down">确 定</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form id='uploadFileForm1' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadPhoto1" value="点击上传图片"/>
                <input type="text" name="fileType" value="banner">
            </form>
            <form id='uploadFileForm2' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadPhoto2" value="点击上传图片"/>
                <input type="text" name="fileType" value="banner">
            </form>
            <form id='uploadFileForm3' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadPhoto3" value="点击上传图片"/>
                <input type="text" name="fileType" value="banner">
            </form>
        </main>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(e) {

        /**
         * down - click
         */
        $(".down").on('click',function() {
            var target = {
                forbid_show_price: $('#forbid_show_price').prop("checked") ? "1" : "0",
                website_banner1 : $("#website_banner1").val(),
                website_banner2 : $("#website_banner2").val(),
                website_banner3 : $("#website_banner3").val()
            };

            $.ajax({
                url: "/Admin/configAction",
                type: "POST",
                dataType: "json",
                contentType:"application/json;charset=UTF-8",
                data: JSON.stringify(target),
                success: function(data) {
                    if(data.status == true){
                        alert("保存成功!");
                        window.location.href = "/Admin/index.html";
                    }
                    else{
                        alert("保存失败!");
                    }
                },
                error: function (xhr) {
                    alert(xhr);
                }
            });
        });

        /**
         * previewImg1 - click
         */
        $("#previewImg1").click(function() {
            $("#uploadPhoto1").click();
        });

        /**
         * previewImg2 - click
         */
        $("#previewImg2").click(function() {
            $("#uploadPhoto2").click();
        });

        /**
         * previewImg3 - click
         */
        $("#previewImg3").click(function() {
            $("#uploadPhoto3").click();
        });

        /**
         * uploadPhoto1 - change
         */
        $("#uploadPhoto1").change(function() {
            var website_banner1 = $("#website_banner1");
            $("#uploadFileForm1").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        $("#previewImg1").prop("src",'/UploadFilePath/banner/' + newFileName);
                        website_banner1.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });
        
        /**
         * uploadPhoto2 - change
         */
        $("#uploadPhoto2").change(function() {
            var website_banner2 = $("#website_banner2");
            $("#uploadFileForm2").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        $("#previewImg2").prop("src",'/UploadFilePath/banner/' + newFileName);
                        website_banner2.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });

        /**
         * uploadPhoto3 - change
         */
        $("#uploadPhoto3").change(function() {
            var website_banner3 = $("#website_banner3");
            $("#uploadFileForm3").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        $("#previewImg3").prop("src",'/UploadFilePath/banner/' + newFileName);
                        website_banner3.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });

    });




</script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>