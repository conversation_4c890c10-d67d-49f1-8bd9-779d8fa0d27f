<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{index/fragment/common :: head}"></div>
<body>

<!-- 欢迎 -->
<div th:replace="~{index/fragment/common :: welcome}"></div>

<!-- 导航 -->
<div th:replace="~{index/fragment/common :: header}"></div>

<!-- 图片轮播 -->
<div th:replace="~{index/fragment/common :: jslider}"></div>

<!-- 内容 -->
<div class="pst_bg">
    <div class="pst">
        <i class="fa fa-home"></i>
        您当前的位置：
        <a href="/News/industry.html">新闻中心</a>
        <i class="fa fa-arrow-right"></i>
        <a href="/News/company.html">公司新闻</a>
    </div>
</div>
<div class="scd clearfix">
    <div class="scd_l">
        <div class="s_name">
            新闻中心
        </div>
        <div th:replace="~{index/fragment/common :: news_slide}"></div>
    </div>
    <div class="scd_r">
        <div class="r_name"><span>公司新闻</span></div>
        <div class="new">

            <dl class="clearfix" th:each="news : ${pageInfo.getList()}">
                <dt><a th:href="'/News/newsDetail/id/' + ${news.id} + '.html'" target="_blank"><img th:src="'/UploadFilePath/news/' + ${news.picture}" th:alt="${news.title}"></a></dt>
                <dd>
                    <div class="title">
                        <a th:href="'/News/newsDetail/id/' + ${news.id} + '.html'" target="_blank">
                            <p th:text="${news.title}"/>
                            <em th:text="${#dates.format(news.createTime,'yyyy-MM-dd')}"/>
                        </a>
                    </div>
                    <div class="des" th:text="${news.remark}"/>
                    <a th:href="'/News/newsDetail/id/' + ${news.id} + '.html'" target="_blank" class="more1">+ 查看详情</a>
                </dd>
            </dl>

            <div class="space_hx">&nbsp;</div>
            <div class="pages">
                <a th:href="@{/News/company.html(pageNo=${pageInfo.getPrePage()})}" class="prev" th:if="${pageInfo.isHasPreviousPage()} eq true">上一页</a>
                <a th:href="'javascript:void(0)'" class="prev" style="cursor: not-allowed" th:if="${pageInfo.isHasPreviousPage()} eq false">上一页</a>

                <a class="now" href="javascript:void(0)" th:text="${pageInfo.pageNum}"/>

                <a th:href="@{/News/company.html(pageNo=${pageInfo.getNextPage()})}" class="next" th:if="${pageInfo.isHasNextPage()} eq true">下一页</a>
                <a th:href="'javascript:void(0)'" class="next" style="cursor: not-allowed" th:if="${pageInfo.isHasNextPage()} eq false">下一页</a>
            </div>
        </div>
    </div>
</div>

<!-- 页脚与备案 -->
<div th:replace="~{index/fragment/common :: copyright}"></div>

</body>
</html>
