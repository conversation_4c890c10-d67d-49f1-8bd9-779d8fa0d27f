<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>
<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('资讯管理', '日志信息')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-toolbar clearfix">
                                <form class="pull-right search-bar" method="get" action="/Admin/log.html" role="form">
                                    <div class="input-group">
                                        <div class="input-group-btn">
                                            <button class="btn btn-default dropdown-toggle" id="search-btn" data-toggle="dropdown" type="button" aria-haspopup="true" aria-expanded="false">
                                                标题 <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a tabindex="-1" href="javascript:void(0)" data-field="title">名称</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <input type="text" class="form-control" value="" name="search" placeholder="请输入用户名称" autocomplete="off">
                                    </div>
                                </form>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>编号</th>
                                            <th>登录IP</th>
                                            <th>登录设备</th>
                                            <th>浏览器</th>
                                            <th>登录用户</th>
                                            <th>登录时间</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="log : ${pageInfo.getList()}">
                                            <td th:text="${log.id}"/>
                                            <td th:text="${log.ip}"/>
                                            <td th:text="${log.os}"/>
                                            <td th:text="${log.browser}"/>
                                            <td th:text="${log.root}"/>
                                            <td th:text="${#dates.format(log.createTime, 'yyyy-MM-dd HH:mm:ss')}"/>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <ul class="pagination">
                                    <li th:if="${pageInfo.isHasPreviousPage()} eq true"><a th:href="@{/Admin/log(pageNo=${pageInfo.getPrePage()})}"><span>«</span></a></li>
                                    <li class="disabled" href="javascript:void(0)" th:if="${pageInfo.isHasPreviousPage()} eq false"><span>«</span></li>
                                    <li class="active" href="javascript:void(0)" th:utext="'<span>' + ${pageInfo.pageNum} + '</span>'"/>
                                    <li th:if="${pageInfo.isHasNextPage()} eq true"><a th:href="@{/Admin/log(pageNo=${pageInfo.getNextPage()})}"><span>»</span></a></li>
                                    <li class="disabled" href="javascript:void(0)" th:if="${pageInfo.isHasNextPage()} eq false"><span>»</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>