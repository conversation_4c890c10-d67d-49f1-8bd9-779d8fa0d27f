<html>
<head>
<!-- 请再head中加入itsoms.min.js -->
<script type="text/javascript" src="http://gw.goto56.com/static/index/itsoms.min.js"></script>

<script>

    $(document).ready(function() {
        /**
         * 页面加载后可立即调用initOms()
         * 
         * 绑定订单系统
         * @param url 订单系统的访问地址
         * @param companyId 企业编号
         */
        initOms('http://hlt.oms.goto56.com', '1530066066670354434')
    });

</script>
</head>

<body>
    <!-- Start 查询轨迹，此代码段html代码块插入到body中合适的地方 Start -->
    <div class="col-lg-6 col-sm-6" style="background-color: #1c4264;">
        <section style=" color:#fff" >
            <div style="padding-top: 12px;">
                <div style="float: left;">
                    <i class="iconfont fa fa-calculator fa-lg"> 查询运费</i>
                </div>
                <div style="float: right; padding-bottom: 3px;">
                    <button type="button" class="btn btn-primary" aria-label="Left Align" onclick="queryPrice('')">
                        <i class="iconfont fa fa-search fa-lg"></i> 查询
                    </button>
                </div>
                <div style="clear: both;"></div>
            </div>
            <form class="form-horizontal" style="background-color: rgba(197,197,197,0.09)">
                <div class="form-group">
                    <label for="country" class="control-label col-md-3">目的地：</label>
                    <div class="col-md-9">
                        <select class="form-control select2" name="country" id="country"></select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="postcode" class="control-label col-md-3">邮编：</label>
                    <div class="col-md-9">
                        <input class="form-control" name="postcode" id="postcode" placeholder="邮编">
                    </div>
                </div>
                <div class="form-group">
                    <label for="weightD" class="control-label col-md-3">重量(KG)：</label>
                    <div class="col-md-9 input-group" style="padding-right: 15px;">
                        <input type="text" class="form-control" name="weight" id="weightD" placeholder="重量(KG)">
                        <span class="input-group-addon"></span>
                        <input class="form-control" style="width:65px;" name="length" id="lengthD" placeholder="长(cm)">
                        <span class="input-group-addon">✖️</span>
                        <input class="form-control" style="width:65px;" name="width" id="widthD" placeholder="宽(cm)">
                        <span class="input-group-addon">✖️</span>
                        <input class="form-control" style="width:65px;" name="height" id="heightD" placeholder="高(cm)">
                    </div>
                </div>
            </form>
        </section>
    </div>
    <!-- End -->
</body>
</html>