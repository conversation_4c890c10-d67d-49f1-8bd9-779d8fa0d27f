<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>

<link rel="stylesheet" href="/static/admin/simditor/css/simditor.css">
<script src="/static/admin/simditor/js/module.min.js"></script>
<script src="/static/admin/simditor/js/hotkeys.min.js"></script>
<script src="/static/admin/simditor/js/uploader.min.js"></script>
<script src="/static/admin/simditor/js/simditor.min.js"></script>
<script src="/static/admin/simditor/js/jquery.form.js"></script>
<style>
    .m{ width: 100%; margin-left: auto; margin-right: auto; }
    .note-editor.note-frame {border: 1px solid #eee!important;}
    .note-editor .note-editing-area {min-height: 500px;}
</style>

<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('资讯管理', '修改资讯')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">

            <div class="container-fluid">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <ul class="nav nav-tabs page-tabs">
                                <li class="active"> <a href="javascript:void(0)">基本</a> </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <form action="/Admin/updateNewsAction" method="post" name="edit-form" class="edit-form">
                                        <input th:value="${news.id}" name="id" type="hidden"/>
                                        <div class="form-group">
                                            <label>资讯名称</label>
                                            <input class="form-control" type="text" name="title" th:value="${news.title}" placeholder="请输入资讯名称" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>资讯摘要</label>
                                            <textarea name="remark" class="form-control" th:text="${news.remark}" placeholder="请输入资讯摘要" style="height: 200px;"/>
                                        </div>
                                        <div class="form-group">
                                            <label>资讯类别</label>
                                            <select class="form-control" name="catalog">
                                                <option value="行业资讯" th:if="${news.catalog eq '行业资讯'}" selected>行业资讯</option>
                                                <option value="公司新闻" th:if="${news.catalog eq '行业资讯'}">公司新闻</option>
                                                <option value="公司新闻" th:if="${news.catalog eq '公司新闻'}" selected>公司新闻</option>
                                                <option value="行业资讯" th:if="${news.catalog eq '公司新闻'}">行业资讯</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>图片预览（点击上传图片）</label>
                                            <img id="previewImg" style="cursor: pointer;" th:src="'/UploadFilePath/news/'+${news.picture}" th:error="'/UploadFilePath/error.jpg'" height="300">
                                        </div>
                                        <div class="form-group">
                                            <label>资讯图片</label>
                                            <input class="form-control" id="newFileNameCode" type="text" th:value="${news.picture}" name="pic" placeholder="-" autocomplete="off" required readonly>
                                        </div>
                                        <div class="form-group">
                                            <label>资讯内容</label>
                                            <p>
                                                <textarea name="content" id="editor" placeholder="请输入资讯内容...... 注：请在Word文档中编辑好内容后再粘贴到这里" autofocus th:text="${news.content}"/>
                                                <script>
                                                    var editor = new Simditor({
                                                        textarea: $('#editor')
                                                        //optional options
                                                    });
                                                </script>
                                            </p>
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary m-r-5">确 定</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form id='uploadFileForm' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadPhoto" value="点击上传图片"/>
                <input type="text" name="fileType" value="news">
            </form>
        </main>
    </div>
</div>
<script type="text/javascript">
    $("#previewImg").click(function(){
        $("#uploadPhoto").click();
    });

    $(document).ready(function(e) {
        var newFileNameCode = $("#newFileNameCode");

        $("#uploadPhoto").change(function() {
            $("#uploadFileForm").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        $("#previewImg").prop("src",'/UploadFilePath/news/' + newFileName);
                        newFileNameCode.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });
    });
</script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>