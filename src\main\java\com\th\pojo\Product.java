package com.th.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class Product {
    private Integer id;
    private Integer companyId;
    private String title;
    private String remark;
    private String isOpenA;
    private String android;
    private String isOpenI;
    private String ios;
    private String picture;
    private Timestamp createTime;
    private Timestamp updateTime;
}
