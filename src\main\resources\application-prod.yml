server:
  port: 8901

spring:
  datasource:
    #---- mysql数据库配置 ----#
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: <EMAIL>
#    url: ***********************************************************************************************************************************************
#    username: root
#    password: JieLianAlice912@ITS
    #---- / mysql数据库配置 ----#

    #---- druid数据库连接池配置 ----#
    druid:
      # 初始化连接池数量
      initial-size: 5
      # 最小连接池数量
      min-idle: 5
      # 最大连接池数量
      max-active: 30
      # 配置获取连接等待超时的时间，单位毫秒
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 验证数据库连接的有效性，若返回结果不为空，则说明连接可用
      validation-query: select 1
      # 在检查闲置连接时同时检查连接可用性
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache，PSCache对支持游标的数据库性能提升巨大，比如说Oracle，而在MySQL下建议关闭
      pool-prepared-statements: true
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      # 合并多个DruidDataSource的监控数据
      #useGlobalDataSourceStat: true
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connect-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 采集web-jdbc关联监控的数据
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.txt,*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        reset-enable: false
        login-username: root
        login-password: 123456
        allow:
        deny:
    #---- / druid数据库连接池配置 ----#

  #---- 文件上传大小限制 ----#
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
  #---- / 文件上传大小限制 ----#

  #---- thymeleaf前端模板配置 ----#
  thymeleaf:
    mode: HTML
    encoding: UTF-8
    cache: false
    prefix: classpath:/templates/
    suffix: .html
  #---- / thymeleaf前端模板配置 ----#

#---- 配置文件上传到服务器的路径 ----#
system:
  # 后台系统管理员账号
  username: "admin"
  # 后台系统管理员密码
  password: "123456"
  # Linux路径
  upload-file-path: "/alidata/apps/its-company-website/upload/"
#---- / 配置文件上传到服务器的路径 ----#
