package com.th.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * 模板实体类
 * 用于管理网站模板信息
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class Template {
    private Integer id;
    private String templateCode;        // 模板代码，如 default, modern, business
    private String templateName;        // 模板名称，如 默认模板, 现代风格, 商务风格
    private String templateDescription; // 模板描述
    private String templateVersion;     // 模板版本
    private String templateAuthor;      // 模板作者
    private String previewImage;        // 预览图片路径
    private String templatePath;        // 模板文件路径
    private Boolean isActive;           // 是否启用
    private Boolean isPremium;          // 是否为付费模板
    private Integer sortOrder;          // 排序顺序
    private Timestamp createTime;       // 创建时间
    private Timestamp updateTime;       // 更新时间
}
