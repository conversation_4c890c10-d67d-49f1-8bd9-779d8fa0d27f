package com.th.mapper;

import com.th.pojo.Company;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface MgrMapper {

    @Select({"select * from `company` where username = #{username} and id = #{companyId}"})
    Company selectCompanyByUsername(@Param("username") String username, @Param("companyId") Integer companyId);

    @Select({"select * from `company` where website like CONCAT(#{website},'%')"})
    Company selectCompanyByWebsite(String website);

    @Select({"select * from `company` where id = #{id}"})
    Company selectCompanyById(Integer id);

    @Update({"update `company` set",
            "password = #{password}",
            "where id = #{id}"})
    boolean updatePassword(@Param("id") Integer id, @Param("password") String password);

}
