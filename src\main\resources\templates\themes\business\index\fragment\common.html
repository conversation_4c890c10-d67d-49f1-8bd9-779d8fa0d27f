<!doctype html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

    <!-- 页头 -->
    <head th:fragment="head">
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title th:text="${configs.get('website_title').value}"/>
        <meta name="author" content="Vegeta"/>
        <meta name="keywords" th:content="${configs.get('seo_keywords').value}"/>
        <meta name="description" th:content="${configs.get('seo_description').value}"/>
        <link rel="shortcut icon" th:href="@{'/static/index/img/favicon.ico'}">
        <script type="text/javascript" src="/static/index/js/jquery-2.1.4.min.js"></script>
        <link rel="stylesheet" type="text/css" href="/static/index/css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="/static/index/css/jSlider.min.css">
        <link rel="stylesheet" href="/static/index/css/font-awesome.min.css">
        <link rel="stylesheet" type="text/css" href="/static/index/css/style.css">
        <link rel="stylesheet" type="text/css" href="/static/index/css/jquery.bxslider.css">
        <link rel="stylesheet" type="text/css" href="/static/index/css/select2.min.css"/>
        <script type="text/javascript" src="/static/index/js/bootstrap.min.js"></script>
        <script type="text/javascript" src="/static/index/js/jquery.jSlider.min.js"></script>
        <script type="text/javascript" src="/static/index/js/hover-dropdown.js"></script>
        <script type="text/javascript" src="/static/index/js/select2.min.js"></script>
    </head>

    <!-- 欢迎 -->
    <div class="top hidden-xs" th:fragment="welcome">
        <div class="container">
            <div class="top-fl"><i class="fa fa-th" th:text="'&nbsp;Hi，欢迎来到' + ${configs.get('website_company').value} + '！'"/></div>
            <div class="top-fr" th:text="'客服热线：' + ${configs.get('website_phone').value}"/>
        </div>
    </div>

    <!-- 导航 -->
    <header class="header-frontend" th:fragment="header">
        <div class="navbar navbar-default navbar-static-top">
            <div class="container">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target=".navbar-collapse">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="/"><img th:src="'/UploadFilePath/logo/' + ${configs.get('website_logo').value}" /></a>
                </div>
                <div class="navbar-collapse collapse" style="height: 1px;">
                    <ul class="nav navbar-nav">
                        <li><a href="/Index/index.html">首页</a></li>
                        <li><a href="/About/about.html">关于我们</a></li>
                        <li class="dropdown">
                            <a href="/News/industry.html" class="dropdown-toggle " data-toggle="dropdown" data-hover="dropdown" data-delay="0" data-close-others="false">新闻中心 <b class=" icon-angle-down"></b></a>
                            <ul class="dropdown-menu">
                                <li><a href="/News/industry.html">行业资讯</a></li>
                                <li><a href="/News/company.html">公司新闻</a></li>
                            </ul>
                        </li>
                        <li><a href="/Project/projects.html">渠道</a></li>
                        <li><a href="/Contact/contact.html">联系我们</a></li>
                        <li><a href="/Talent/talent.html">人才招聘</a></li>
                        <!--<li><a href="/Download/download.html">下载中心</a></li>-->
                        <li><a th:href="${configs.get('oms_url').value}" target="_blank">会员登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <script>
            var uri = window.location.pathname;
            console.log(uri);

            if (uri != null && uri != "") {
                // 获取 navbar-nav 第一层的 li
                var lis = $(".navbar-nav > li");
                // console.log(lis);

                lis.each(function(e) {
                    var li = $(this);
                    // console.log(li);

                    var a = li.find("a");
                    // console.log(a);

                    var href = a.attr("href");
                    // console.log(href);

                    var action = href.split("/");
                    // console.log(action);

                    var controllerName = action[1];
                    console.log(controllerName);

                    if ("Index".indexOf(controllerName) != -1 && uri == "/") {
                        li.addClass("active");
                    }

                    if (uri.indexOf(controllerName) != -1 && uri != "/") {
                        li.addClass("active");
                    }
                });
            }
        </script>
    </header>

    <!-- 图片轮播 -->
    <div class="jSlider" id="slider" data-loop="true" data-delay="3000" data-speed="500" th:fragment="jslider">
        <div><img th:src="'/UploadFilePath/banner/' + ${configs.get('website_banner1').value}" ondragstart="return false"></div>
        <div><img th:src="'/UploadFilePath/banner/' + ${configs.get('website_banner2').value}" ondragstart="return false"></div>
        <div><img th:src="'/UploadFilePath/banner/' + ${configs.get('website_banner3').value}" ondragstart="return false"></div>
    </div>

    <!-- 页脚与备案 -->
    <div th:fragment="copyright">
        <!-- 页脚 -->
        <div class="copyright hidden-md hidden-xs">
            <div class="container">
                <div class="row">
                    <div class="copy-left">
                        <img th:src="'/UploadFilePath/logo/' + ${configs.get('website_logo').value}" ondragstart="return false" width="150"/>
                        <!--<p style="font-size: 13px;">周一至周五 9:00 - 18:00</p>-->
                        <!--<a href="javascript:void(0)" class="kf">在线客服</a>-->
                    </div>
                    <ul class="copy-mid">
                        <li>
                            <h2>关于我们</h2>
                            <dl>
                                <dt><a href="/About/about.html">公司简介</a></dt>
                                <!--<dt><a href="/About/scope.html">经营范围</a></dt>-->
                                <!--<dt><a href="/About/civilization.html">企业文化</a></dt>-->
                                <!--<dt><a href="/About/setup.html">人才体系</a></dt>-->
                                <dt><a href="/About/partner.html">合作伙伴</a></dt>
                            </dl>
                        </li>
                        <li>
                            <h2>新闻中心</h2>
                            <dl>
                                <dt><a href="/News/industry.html">行业资讯</a></dt>
                                <dt><a href="/News/company.html">公司新闻</a></dt>
                            </dl>
                        </li>
                        <li>
                            <h2>渠道</h2>
                            <dl>
                                <dt><a href="/Project/projects.html">所有渠道</a></dt>
                            </dl>
                        </li>
                        <!--<li>-->
                            <!--<h2>服务支持</h2>-->
                            <!--<dl>-->
                                <!--<dt><a href="/Download/download.html">下载中心</a></dt>-->
                            <!--</dl>-->
                        <!--</li>-->
                    </ul>
                </div>
            </div>
        </div>

        <!-- 备案 -->
        <a href="https://beian.miit.gov.cn/" target="_blank"><div class="copy hidden-md hidden-xs" th:text="' All Rights Reserved. ' + ${configs.get('website_record').value}"/></a>
        <div class="copy hidden-lg">
            <p th:text="'All Rights Reserved.'"/>
            <a href="https://beian.miit.gov.cn/" target="_blank"><p th:text="${configs.get('website_record').value}"/></a>
        </div>
    </div>

    <!-- 关于我们侧边栏 -->
    <ul class="s_nav" th:fragment="about_slide">
        <li><a href="/About/about.html">公司简介</a></li>
        <li><a href="/About/scope.html">经营范围</a></li>
        <li><a href="/About/civilization.html">企业文化</a></li>
        <li><a href="/About/setup.html">人才体系</a></li>
        <li><a href="/About/partner.html">合作伙伴</a></li>
        <script>
            var uri = window.location.pathname;
            // console.log(uri);

            if (uri != null && uri != "") {
                // 获取 s_nav 第一层的 li
                var lis = $(".s_nav > li");
                // console.log(lis);

                lis.each(function(e) {
                    var li = $(this);
                    // console.log(li);

                    var a = li.find("a");
                    // console.log(a);

                    var href = a.attr("href");
                    // console.log(href);

                    var action = href.split("/");
                    // console.log(action);

                    var methodName = action[2];
                    console.log(methodName);

                    if (uri.indexOf(methodName) != -1) {
                        li.addClass("now");
                    }
                });
            }
        </script>
    </ul>

    <!-- 联系我们侧边栏 -->
    <ul class="s_nav" th:fragment="contact_slide">
        <li><a href="/Contact/contact.html">联系方式</a></li>
        <li><a href="/Contact/online.html">在线留言</a></li>
        <script>
            var uri = window.location.pathname;
            // console.log(uri);

            if (uri != null && uri != "") {
                // 获取 s_nav 第一层的 li
                var lis = $(".s_nav > li");
                // console.log(lis);

                lis.each(function(e) {
                    var li = $(this);
                    // console.log(li);

                    var a = li.find("a");
                    // console.log(a);

                    var href = a.attr("href");
                    // console.log(href);

                    var action = href.split("/");
                    // console.log(action);

                    var methodName = action[2];
                    console.log(methodName);

                    if (uri.indexOf(methodName) != -1) {
                        li.addClass("now");
                    }
                });
            }
        </script>
    </ul>

    <!-- 人才招聘侧边栏 -->
    <ul class="s_nav" th:fragment="talent_slide">
        <li><a href="/Talent/talent.html">人才中心</a></li>
        <script>
            var uri = window.location.pathname;
            // console.log(uri);

            if (uri != null && uri != "") {
                // 获取 s_nav 第一层的 li
                var lis = $(".s_nav > li");
                // console.log(lis);

                lis.each(function(e) {
                    var li = $(this);
                    // console.log(li);

                    var a = li.find("a");
                    // console.log(a);

                    var href = a.attr("href");
                    // console.log(href);

                    var action = href.split("/");
                    // console.log(action);

                    var methodName = action[2];
                    console.log(methodName);

                    if (uri.indexOf(methodName) != -1) {
                        li.addClass("now");
                    }
                });
            }
        </script>
    </ul>

    <!-- 新闻中心侧边栏 -->
    <ul class="s_nav" th:fragment="news_slide">
        <li><a href="/News/industry.html">行业资讯</a></li>
        <li><a href="/News/company.html">公司新闻</a></li>
        <script>
            var uri = window.location.pathname;
            // console.log(uri);

            if (uri != null && uri != "") {
                // 获取 s_nav 第一层的 li
                var lis = $(".s_nav > li");
                // console.log(lis);

                lis.each(function(e) {
                    var li = $(this);
                    // console.log(li);

                    var a = li.find("a");
                    // console.log(a);

                    var href = a.attr("href");
                    // console.log(href);

                    var action = href.split("/");
                    // console.log(action);

                    var methodName = action[2];
                    console.log(methodName);

                    if (uri.indexOf(methodName) != -1) {
                        li.addClass("now");
                    }
                });
            }
        </script>
    </ul>

    <!-- 渠道侧边栏 -->
    <ul class="s_nav" th:fragment="project_slide">
        <li th:each="project : ${pageInfo.getList()}">
            <a th:href="'/Project/projectDetail/id/' + ${project.id} + '.html'">
                <span th:text="${project.title}"></span>
            </a>
        </li>

        <!--<li><a href="/Project/projects.html">工程渠道</a></li>-->

        <script>
            var uri = window.location.pathname;
            // console.log(uri);

            if (uri != null && uri != "") {
                // 获取 s_nav 第一层的 li
                var lis = $(".s_nav > li");
                // console.log(lis);

                lis.each(function(e) {
                    var li = $(this);
                    // console.log(li);

                    var a = li.find("a");
                    // console.log(a);

                    var href = a.attr("href");
                    // console.log(href);

                    var action = href.split("/");
                    // console.log(action);

                    var methodName = action[2];
                    console.log(methodName);

                    if (uri.indexOf(methodName) != -1) {
                        li.addClass("now");
                    }
                });
            }
        </script>
    </ul>

    <!-- 下载中心侧边栏 -->
    <ul class="s_nav" th:fragment="product_slide">
        <li><a href="/Download/download.html">产品列表</a></li>
        <script>
            var uri = window.location.pathname;
            // console.log(uri);

            if (uri != null && uri != "") {
                // 获取 s_nav 第一层的 li
                var lis = $(".s_nav > li");
                // console.log(lis);

                lis.each(function(e) {
                    var li = $(this);
                    // console.log(li);

                    var a = li.find("a");
                    // console.log(a);

                    var href = a.attr("href");
                    // console.log(href);

                    var action = href.split("/");
                    // console.log(action);

                    var methodName = action[2];
                    console.log(methodName);

                    if (uri.indexOf(methodName) != -1) {
                        li.addClass("now");
                    }
                });
            }
        </script>
    </ul>

</html>