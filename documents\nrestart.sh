#!/bin/sh

if [ $# -lt 1 ]; then
    echo "Error, run ./restart.sh (name of jar package)"
        echo "Example: run ./restart.sh its-saas-admin-server.jar"
        echo "Tips: You can use the tab key when entering name of jar package"
    exit 1
fi

proc_index=$2
jar_package_name=$1
proc_jar_package_name=$proc_index-$jar_package_name

jar_package_dir=$PWD
package_name_prefix=its-company-website
active_profiles=prod
jmx_port=890$proc_index
http_port=899$proc_index
jvmopt=" -server -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m -XX:+DisableExplicitGC -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -Xms2048m -Xmx2048m -Xmn256m -Xss512k -XX:SurvivorRatio=8 -Djdk.xml.entityExpansionLimit=0 -Xdebug -Xrunjdwp:transport=dt_socket,address=$jmx_port,server=y,suspend=n -Dfile.encoding=utf-8 -Djava.io.tmpdir=/alidata/apps/its-company-website/temp"

occupied_port=$(ps -ef|grep $proc_jar_package_name|grep java|awk '{print $2}')

if [ -n "$occupied_port" ]; then
  echo "occupied port:$occupied_port,killing...."
  kill -9 $occupied_port
  echo "occupied port:$occupied_port,killed."
fi

jar_package_log_name=`ls $jar_package_name|awk 'NR==1{print}'|cut -d . -f 1`
#jar_package_log_name=`ls $proc_jar_package_name|awk 'NR==1{print}'|cut -d . -f 1`

cp -f $jar_package_dir/$jar_package_name $jar_package_dir/$proc_jar_package_name
echo "[$proc_jar_package_name] copy completed."

java $jvmopt -jar $jar_package_dir/$proc_jar_package_name --spring.profiles.active=$active_profiles --server.port=$http_port >> $jar_package_dir/logs/$jar_package_log_name-$(date +%Y-%m-%d).log &
echo "[$proc_jar_package_name] Restart Success."
echo "tail -f $jar_package_dir/logs/$jar_package_log_name-$(date +%Y-%m-%d).log"
