<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>

<link rel="stylesheet" href="/static/admin/simditor/css/simditor.css">
<script src="/static/admin/simditor/js/module.min.js"></script>
<script src="/static/admin/simditor/js/hotkeys.min.js"></script>
<script src="/static/admin/simditor/js/uploader.min.js"></script>
<script src="/static/admin/simditor/js/simditor.min.js"></script>
<script src="/static/admin/simditor/js/jquery.form.js"></script>
<style>
    .m{ width: 100%; margin-left: auto; margin-right: auto; }
    .note-editor.note-frame {border: 1px solid #eee!important;}
    .note-editor .note-editing-area {min-height: 500px;}
</style>

<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('产品管理', '添加产品')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <ul class="nav nav-tabs page-tabs">
                                <li class="active"> <a href="javascript:void(0)">基本</a> </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <form action="/Admin/addProductAction" method="post" name="edit-form" class="edit-form">
                                        <div class="form-group">
                                            <label>产品名称</label>
                                            <input class="form-control" type="text" name="title" placeholder="请输入产品名称" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>是否开启安卓下载</label>
                                            <select class="form-control" name="isOpenA">
                                                <option value="1">是</option>
                                                <option value="0">否</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>安卓版本文件名</label>
                                            <button class="btn btn-primary" id="uploadAndroidBtn">点击此上传安卓版本App</button>
                                            <input class="form-control" readonly id="newFileNameAndroidCode" type="text" name="android" placeholder="-" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>是否开启安卓下载</label>
                                            <select class="form-control" name="isOpenI">
                                                <option value="1">是</option>
                                                <option value="0">否</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>苹果版本文件名</label>
                                            <button class="btn btn-primary" id="uploadIosBtn">点击此上传苹果版本App</button>
                                            <input class="form-control" readonly id="newFileNameIosCode" type="text" name="ios" placeholder="-" autocomplete="off">
                                        </div>
                                        <div class="form-group">
                                            <label>图片预览（点击上传图片）</label>
                                            <img id="previewImg" style="cursor: pointer;" th:src="'/UploadFilePath/error.jpg'" height="300">
                                        </div>
                                        <div class="form-group">
                                            <label>产品图片</label>
                                            <input class="form-control" id="newFileNameCode" type="text" name="pic" placeholder="-" autocomplete="off" required readonly>
                                        </div>
                                        <div class="form-group">
                                            <label>产品简介</label>
                                            <textarea name="remark" class="form-control" placeholder="请输入产品简介...... 注：请在Word文档中编辑好内容后再粘贴到这里" style="height: 200px;"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary m-r-5">确 定</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form id='uploadFileForm' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadPhoto" value="点击上传图片"/>
                <input type="text" name="fileType" value="product">
            </form>

            <form id='uploadFileFormForAndroid' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadAndroid" value="点击上传安卓App"/>
                <input type="text" name="fileType" value="product">
            </form>

            <form id='uploadFileFormForIos' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadIos" value="点击上传苹果App"/>
                <input type="text" name="fileType" value="product">
            </form>
        </main>
    </div>
</div>
<script type="text/javascript">
    $("#previewImg").click(function(){
        $("#uploadPhoto").click();
    });

    $("#uploadAndroidBtn").click(function(){
        $("#uploadAndroid").click();
    });

    $("#uploadIosBtn").click(function(){
        $("#uploadIos").click();
    });

    $(document).ready(function(e) {
        var newFileNameCode = $("#newFileNameCode");
        $("#uploadPhoto").change(function() {
            $("#uploadFileForm").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        $("#previewImg").prop("src",'/UploadFilePath/product/' + newFileName);
                        newFileNameCode.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });

        var newFileNameAndroidCode = $("#newFileNameAndroidCode");
        $("#uploadAndroid").change(function() {
            $("#uploadFileFormForAndroid").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        newFileNameAndroidCode.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });

        var newFileNameIosCode = $("#newFileNameIosCode");
        $("#uploadIos").change(function() {
            $("#uploadFileFormForIos").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        newFileNameIosCode.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });
    });
</script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>