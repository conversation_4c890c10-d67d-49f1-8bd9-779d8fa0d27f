package com.th.controller;

import com.th.common.Constant;
import com.th.service.TemplateService;
import com.th.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * 模板测试控制器
 * 用于测试模板切换功能
 */
@Controller
@RequestMapping("test")
public class TemplateTestController {

    @Autowired
    private TemplateService templateService;

    @Autowired
    private AdminService adminService;

    /**
     * 获取公司ID
     */
    private Integer getCompanyId(HttpSession session) {
        Integer companyId = (Integer) session.getAttribute(Constant.COMPANY_ID);
        return companyId != null ? companyId : 1; // 默认使用公司ID=1进行测试
    }

    /**
     * 测试页面 - 显示当前模板信息
     */
    @GetMapping("template")
    @ResponseBody
    public Map<String, Object> testTemplate(HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        Integer companyId = getCompanyId(session);
        
        String currentTemplate = templateService.getCurrentTemplate(companyId);
        String templatePath = templateService.buildTemplatePath(companyId, "index/index.html");
        
        result.put("companyId", companyId);
        result.put("currentTemplate", currentTemplate);
        result.put("templatePath", templatePath);
        result.put("availableTemplates", Constant.AVAILABLE_TEMPLATES);
        
        return result;
    }

    /**
     * 设置模板 - 用于测试模板切换
     */
    @GetMapping("setTemplate")
    @ResponseBody
    public Map<String, Object> setTemplate(@RequestParam String template, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        Integer companyId = getCompanyId(session);
        
        try {
            boolean success = templateService.setCompanyTemplate(companyId, template);
            result.put("success", success);
            result.put("message", success ? "模板设置成功" : "模板设置失败");
            result.put("companyId", companyId);
            result.put("newTemplate", template);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "设置失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试所有模板
     */
    @GetMapping("allTemplates")
    @ResponseBody
    public Map<String, Object> getAllTemplates() {
        Map<String, Object> result = new HashMap<>();
        result.put("templates", templateService.getAllAvailableTemplates());
        return result;
    }

    /**
     * 模板测试页面
     */
    @GetMapping("page")
    public String testPage(HttpSession session) {
        return "test_template.html";
    }
}
