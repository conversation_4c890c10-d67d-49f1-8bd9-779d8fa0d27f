<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" th:content="${configs.get('seo_description').value}">
    <meta name="keywords" th:content="${configs.get('seo_keywords').value}">
    <meta name="author" content="企业门户网站">

    <title th:text="'联系我们 - ' + ${configs.get('website_title').value} ?: '企业门户网站'">联系我们 - 企业门户网站</title>

    <!--== Google Fonts ==-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="/static/moban6072/css/css2.css" rel="stylesheet">

    <!--== Bootstrap CSS ==-->
    <link href="/static/moban6072/css/bootstrap.min.css" rel="stylesheet">
    <!--== Icofont CSS ==-->
    <link href="/static/moban6072/css/icofont.css" rel="stylesheet">
    <!--== Swiper CSS ==-->
    <link href="/static/moban6072/css/swiper.min.css" rel="stylesheet">
    <!--== Main Style CSS ==-->
    <link href="/static/moban6072/css/style.css" rel="stylesheet">

</head>

<body>

<!--wrapper start-->
<div class="wrapper page-contact-wrapper">

  <!--== Start Header Wrapper ==-->
  <header class="header-wrapper">
    <div class="header-area header-default header-transparent sticky-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-4 col-sm-6 col-lg-2">
            <div class="header-logo-area">
              <a href="/">
                <img class="logo-main" th:src="'/UploadFilePath/config/' + ${configs.get('website_logo').value}" 
                     th:alt="${configs.get('website_company').value}" width="161" height="48">
                <img class="logo-light" th:src="'/UploadFilePath/config/' + ${configs.get('website_logo').value}" 
                     th:alt="${configs.get('website_company').value}" width="161" height="48">
              </a>
            </div>
          </div>
          <div class="col-lg-7 d-none d-lg-block">
            <div class="header-navigation-area">
              <ul class="main-menu nav position-relative">
                <li><a href="/">首页</a></li>
                <li class="has-submenu"><a href="/About/about.html">关于我们</a>
                  <ul class="submenu-nav">
                    <li><a href="/About/about.html">公司简介</a></li>
                    <li><a href="/About/scope.html">经营范围</a></li>
                    <li><a href="/About/civilization.html">企业文化</a></li>
                    <li><a href="/About/setup.html">人才体系</a></li>
                    <li><a href="/About/partner.html">合作伙伴</a></li>
                  </ul>
                </li>

                <li class="has-submenu"><a href="/News/company.html">新闻中心</a>
                  <ul class="submenu-nav">
                    <li><a href="/News/company.html">公司新闻</a></li>
                    <li><a href="/News/industry.html">行业资讯</a></li>
                  </ul>
                </li>
                <li class="active"><a href="/Contact/contact.html">联系我们</a></li>
              </ul>
            </div>
          </div>
          <div class="col-8 col-sm-6 col-lg-3">
            <div class="header-action-area">
              <div class="header-action-btn">
                <a class="btn-theme" href="/Contact/contact.html"><span>获取报价</span></a>
              </div>
              <button class="btn-menu d-lg-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasWithBothOptions" aria-controls="offcanvasWithBothOptions">
                <span class="icon-line"></span>
                <span class="icon-line"></span>
                <span class="icon-line"></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!--== End Header Wrapper ==-->
  
  <main class="main-content">
    <!--== Start Page Title Area ==-->
    <section class="page-title-area bg-img" th:data-bg-img="'/UploadFilePath/config/' + ${configs.get('website_banner1').value}">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-12 m-auto">
            <div class="page-title-content text-center">
              <h2 class="title">联系我们</h2>
              <div class="bread-crumbs"><a href="/"> 首页 </a><span class="breadcrumb-sep"> // </span><span class="active"> 联系我们</span></div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-overlay3"></div>
    </section>
    <!--== End Page Title Area ==-->

    <!--== Start Contact Area ==-->
    <section class="contact-area">
      <div class="contact-page-wrap">
        <div class="container container-info">
          <div class="row">
            <div class="col-lg-6">
              <div class="section-title">
                <h2 class="title">联系我们</h2>
                <p>欢迎与我们取得联系，我们将竭诚为您提供专业的服务和支持。</p>
              </div>
              <div class="contact-form mb-md-70">
                <form class="contact-form-wrapper" id="contact-form" action="/Contact/contact.html" method="post">
                  <div class="row">
                    <div class="col-lg-12">
                      <div class="row">
                        <div class="col-md-6">
                          <div class="form-group">
                            <input class="form-control" type="text" name="name" placeholder="姓名" required>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-group">
                            <input class="form-control" type="email" name="email" placeholder="邮箱" required>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-group">
                            <input class="form-control" type="text" name="phone" placeholder="联系电话" required>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-group">
                            <input class="form-control" type="text" name="company" placeholder="公司名称">
                          </div>
                        </div>
                        <div class="col-md-12">
                          <div class="form-group">
                            <select class="form-control" name="type" required>
                              <option value="">请选择咨询类型</option>
                              <option value="业务咨询">业务咨询</option>
                              <option value="技术支持">技术支持</option>
                              <option value="合作洽谈">合作洽谈</option>
                              <option value="投诉建议">投诉建议</option>
                              <option value="其他">其他</option>
                            </select>
                          </div>
                        </div>
                        <div class="col-md-12">
                          <div class="form-group">
                            <textarea class="form-control" name="content" placeholder="请详细描述您的需求或问题" rows="5" required></textarea>
                          </div>
                        </div>
                        <div class="col-md-12">
                          <div class="form-group mb-0">
                            <button class="btn btn-theme" type="submit">提交咨询 <i class="icofont-rounded-double-right mr-0"></i></button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
              <!-- Message Notification -->
              <div class="form-message">
                <div class="alert alert-success" th:if="${message}" th:text="${message}" style="display: none;"></div>
                <div class="alert alert-danger" th:if="${error}" th:text="${error}" style="display: none;"></div>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="contact-info">
                <div class="row">
                  <div class="col-md-4 col-lg-12">
                    <div class="contact-info-item">
                      <div class="icon">
                        <img src="/static/moban6072/picture/telephone.webp" alt="电话" width="43" height="43">
                      </div>
                      <div class="content">
                        <h4>联系电话</h4>
                        <a th:href="'tel:' + ${configs.get('website_phone').value}" th:text="${configs.get('website_phone').value}">联系电话</a>
                        <span th:if="${configs.get('website_fax').value != null and configs.get('website_fax').value != ''}">
                          <br><small>传真：<span th:text="${configs.get('website_fax').value}">传真号码</span></small>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4 col-lg-12">
                    <div class="contact-info-item">
                      <div class="icon">
                        <img src="/static/moban6072/picture/envelope.webp" alt="邮箱" width="45" height="33">
                      </div>
                      <div class="content">
                        <h4>电子邮箱</h4>
                        <a th:href="'mailto:' + ${configs.get('website_email').value}" th:text="${configs.get('website_email').value}">邮箱地址</a>
                        <span th:if="${configs.get('website_qq').value != null and configs.get('website_qq').value != ''}">
                          <br><small>QQ：<span th:text="${configs.get('website_qq').value}">QQ号码</span></small>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4 col-lg-12">
                    <div class="contact-info-item mb-0">
                      <div class="icon">
                        <img src="/static/moban6072/picture/placeholder.webp" alt="地址" width="30" height="40">
                      </div>
                      <div class="content">
                        <h4>公司地址</h4>
                        <p th:text="${configs.get('website_address').value}">公司地址</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 工作时间 -->
                <div class="row mt-4">
                  <div class="col-12">
                    <div class="contact-info-item">
                      <div class="icon">
                        <i class="icofont-clock-time" style="font-size: 30px; color: #007bff;"></i>
                      </div>
                      <div class="content">
                        <h4>工作时间</h4>
                        <p>周一至周五：9:00 - 18:00<br>
                           周六：9:00 - 17:00<br>
                           周日及法定节假日休息</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!--== End Contact Area ==-->

    <!--== Start Divider Area Wrapper ==-->
    <section class="divider-area divider-default-area">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-sm-8 col-md-7">
            <div class="content">
              <h2 class="title">期待与您的<br> 深入合作</h2>
            </div>
          </div>
          <div class="col-sm-4 col-md-5">
            <div class="divider-btn">
              <a class="btn-theme btn-white" href="/About/about.html">了解更多 <i class="icofont-rounded-double-right"></i></a>
            </div>
          </div>
        </div>
      </div>
      <div class="shape-group">
        <div class="shape-style4">
          <img src="/static/moban6072/picture/42.webp" alt="装饰图片" width="560" height="250">
        </div>
      </div>
    </section>
    <!--== End Divider Area Wrapper ==-->
  </main>

  <!--== Start Footer Area Wrapper ==-->
  <footer class="footer-area default-style">
    <div class="footer-main">
      <div class="container">
        <div class="row">
          <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="widget-item">
              <h4 class="widget-title">最新资讯</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-1">最新资讯</h4>
              <div id="dividerId-1" class="collapse widget-collapse-body">
                <div class="widget-blog-wrap">
                  <!-- 显示公司新闻 -->
                  <div class="blog-post-item" th:each="news, iterStat : ${companyNews}" th:if="${iterStat.index < 2}">
                    <div class="content">
                      <h4 class="title">
                        <i class="icon icofont-minus"></i>
                        <a th:href="'/News/newsDetail/id/' + ${news.id}" th:text="${news.title}">新闻标题</a>
                      </h4>
                      <div class="meta-date">
                        <a href="/News/company.html">
                          <i class="icofont-calendar"></i>
                          <span th:text="${#dates.format(news.createTime, 'yyyy/MM/dd')}">日期</span>
                        </a>
                      </div>
                    </div>
                  </div>
                  <!-- 如果没有新闻，显示默认内容 -->
                  <div class="blog-post-item" th:if="${#lists.isEmpty(companyNews)}">
                    <div class="content">
                      <h4 class="title"><i class="icon icofont-minus"></i> <a href="/News/company.html">欢迎关注我们的最新动态</a></h4>
                      <div class="meta-date"><a href="/News/company.html"><i class="icofont-calendar"></i> 最新</a></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 offset-lg-0 col-xl-3 offset-xl-1">
            <div class="widget-item">
              <h4 class="widget-title">主要服务</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-2">主要服务</h4>
              <div id="dividerId-2" class="collapse widget-collapse-body">
                <nav class="widget-menu-wrap">
                  <ul class="nav-menu nav">
                    <li><a href="/About/scope.html"><i class="icofont-minus"></i>经营范围</a></li>
                    <li><a href="/About/setup.html"><i class="icofont-minus"></i>人才体系</a></li>
                    <li><a href="/About/civilization.html"><i class="icofont-minus"></i>企业文化</a></li>
                    <li><a href="/Contact/contact.html"><i class="icofont-minus"></i>联系我们</a></li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-xl-3">
            <div class="widget-item ml-40 ml-lg-20 md-ml-0">
              <h4 class="widget-title">重要链接</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-3">重要链接</h4>
              <div id="dividerId-3" class="collapse widget-collapse-body">
                <nav class="widget-menu-wrap">
                  <ul class="nav-menu nav">
                    <li><a href="/About/about.html"><i class="icofont-minus"></i>关于我们</a></li>
                    <li><a href="/Contact/contact.html"><i class="icofont-minus"></i>客户支持</a></li>
                    <li><a href="/News/company.html"><i class="icofont-minus"></i>新闻中心</a></li>
                    <li><a href="/Contact/contact.html"><i class="icofont-minus"></i>联系我们</a></li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-2 col-xl-2">
            <div class="widget-item ml-35 lg-ml-0">
              <h4 class="widget-title">联系信息</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-4">联系信息</h4>
              <div id="dividerId-4" class="collapse widget-collapse-body">
                <div class="contact-info">
                  <p><i class="icofont-phone"></i> <span th:text="${configs.get('website_phone').value}">联系电话</span></p>
                  <p><i class="icofont-email"></i> <span th:text="${configs.get('website_email').value}">邮箱地址</span></p>
                  <p><i class="icofont-location-pin"></i> <span th:text="${configs.get('website_address').value}">公司地址</span></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-shape bg-img" data-bg-img="/static/moban6072/assets/img/photos/footer1.webp"></div>
    </div>
    <div class="footer-bottom">
      <div class="container">
        <div class="footer-bottom-content">
          <div class="row">
            <div class="col-md-12">
              <div class="widget-copyright">
                <p>
                  Copyright &copy; 2025 <span th:text="${configs.get('website_company').value}">公司名称</span> 版权所有
                  <span th:if="${configs.get('website_record').value != null and configs.get('website_record').value != ''}"
                        th:text="'| ' + ${configs.get('website_record').value}"></span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <!--== End Footer Area Wrapper ==-->

  <!--== Scroll Top Button ==-->
  <div class="scroll-to-top"><span class="icofont-rounded-double-left icofont-rotate-90"></span></div>

  <!--== Start Side Menu ==-->
  <aside class="off-canvas-area offcanvas offcanvas-end" data-bs-scroll="true" tabindex="-1" id="offcanvasWithBothOptions">
    <div class="offcanvas-header">
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Start Mobile Menu Wrapper -->
      <div class="res-mobile-menu">
        <nav id="offcanvasNav" class="offcanvas-menu">
          <ul>
            <li><a href="/">首页</a></li>
            <li class="has-submenu"><a href="/About/about.html">关于我们</a>
              <ul>
                <li><a href="/About/about.html">公司简介</a></li>
                <li><a href="/About/scope.html">经营范围</a></li>
                <li><a href="/About/civilization.html">企业文化</a></li>
                <li><a href="/About/setup.html">人才体系</a></li>
              </ul>
            </li>

            <li class="has-submenu"><a href="/News/company.html">新闻中心</a>
              <ul>
                <li><a href="/News/company.html">公司新闻</a></li>
                <li><a href="/News/industry.html">行业资讯</a></li>
              </ul>
            </li>
            <li><a href="/Contact/contact.html">联系我们</a></li>
          </ul>
        </nav>
      </div>
      <!-- End Mobile Menu Wrapper -->
    </div>
  </aside>
  <!--== End Side Menu ==-->
</div>

<!--=======================Javascript============================-->

<!--=== Modernizr Min Js ===-->
<script src="/static/moban6072/js/modernizr.js"></script>
<!--=== jQuery Min Js ===-->
<script src="/static/moban6072/js/jquery-main.js"></script>
<!--=== jQuery Migration Min Js ===-->
<script src="/static/moban6072/js/jquery-migrate.js"></script>
<!--=== Popper Min Js ===-->
<script src="/static/moban6072/js/popper.min.js"></script>
<!--=== Bootstrap Min Js ===-->
<script src="/static/moban6072/js/bootstrap.min.js"></script>
<!--=== jquery Swiper Min Js ===-->
<script src="/static/moban6072/js/swiper.min.js"></script>
<!--=== jquery Countdown Js ===-->
<script src="/static/moban6072/js/jquery.countdown.min.js"></script>
<!--=== Isotope Min Js ===-->
<script src="/static/moban6072/js/isotope.pkgd.min.js"></script>

<!--=== Custom Js ===-->
<script src="/static/moban6072/js/custom.js"></script>

<!-- 表单提交处理 -->
<script>
$(document).ready(function() {
    // 显示消息提示
    if ($('.alert-success').length > 0) {
        $('.alert-success').show().delay(5000).fadeOut();
    }
    if ($('.alert-danger').length > 0) {
        $('.alert-danger').show().delay(5000).fadeOut();
    }

    // 表单验证
    $('#contact-form').on('submit', function(e) {
        var isValid = true;
        var requiredFields = ['name', 'email', 'phone', 'type', 'content'];

        requiredFields.forEach(function(field) {
            var input = $('[name="' + field + '"]');
            if (!input.val().trim()) {
                input.addClass('is-invalid');
                isValid = false;
            } else {
                input.removeClass('is-invalid');
            }
        });

        // 邮箱格式验证
        var email = $('[name="email"]').val();
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            $('[name="email"]').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('请填写所有必填字段，并确保邮箱格式正确。');
        }
    });

    // 清除验证样式
    $('input, select, textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
});
</script>

</body>

</html>
