<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>
<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('产品管理', '产品列表')}"></div>

        <!--页面主要内容-->
        <main class="lyear-layout-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-toolbar clearfix">
                                <form class="pull-right search-bar" method="get" action="/Admin/product.html" role="form">
                                    <div class="input-group">
                                        <div class="input-group-btn">
                                            <button class="btn btn-default dropdown-toggle" id="search-btn" data-toggle="dropdown" type="button" aria-haspopup="true" aria-expanded="false">
                                                标题 <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a tabindex="-1" href="javascript:void(0)" data-field="title">名称</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <input type="text" class="form-control" value="" name="search" placeholder="请输入产品标题" autocomplete="off">
                                    </div>
                                </form>
                                <div class="toolbar-btn-action">
                                    <a class="btn btn-primary m-r-5" href="/Admin/addProduct.html"><i class="mdi mdi-plus"></i> 添加产品</a>
                                </div>
                            </div>
                            <div class="card-body">

                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <!--<th>编号</th>-->
                                            <th>产品标题</th>
                                            <th>产品简介</th>
                                            <th>是否开启安卓下载</th>
                                            <th>安卓APP文件名</th>
                                            <th>是否苹果安卓下载</th>
                                            <th>苹果APP文件名</th>
                                            <th>产品图片</th>
                                            <th>创建时间</th>
                                            <th>修改时间</th>
                                            <th style="text-align: center">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="project:${pageInfo.getList()}">
                                            <!--<td th:text="${project.id}"/>-->
                                            <td th:text="${project.title}"/>
                                            <td th:text="${project.remark}"/>

                                            <td th:if="${project.isOpenA eq '1'}" th:text="'是'" style="color: #0FB25F"/>
                                            <td th:if="${project.isOpenA eq '0'}" th:text="'否'" style="color: #f84f4f"/>

                                            <td th:text="${project.android}"/>

                                            <td th:if="${project.isOpenI eq '1'}" th:text="'是'" style="color: #0FB25F"/>
                                            <td th:if="${project.isOpenI eq '0'}" th:text="'否'" style="color: #f84f4f"/>

                                            <td th:text="${project.ios}"/>

                                            <td>
                                                <img th:src="'/UploadFilePath/product/' + ${project.picture}" width="300" height="200"/>
                                            </td>

                                            <td th:text="${#dates.format(project.createTime, 'yyyy-MM-dd HH:mm:ss')}"/>
                                            <td th:text="${#dates.format(project.updateTime, 'yyyy-MM-dd HH:mm:ss')}"/>
                                            <td style="text-align: right">
                                                <div class="btn-group">
                                                    <a class="btn btn-success m-r-5" th:href="'/Admin/updateProduct.html?id='+${project.id}"><i class="mdi mdi-update"></i> 修改</a>
                                                </div>
                                                <div class="btn-group">
                                                    <a class="btn btn-danger m-r-5" th:href="'/Admin/deleteProduct?id='+${project.id}"><i class="mdi mdi-delete"></i> 删除</a>
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <ul class="pagination">
                                    <li th:if="${pageInfo.isHasPreviousPage()} eq true"><a th:href="@{/Admin/product(pageNo=${pageInfo.getPrePage()})}"><span>«</span></a></li>
                                    <li class="disabled" href="javascript:void(0)" th:if="${pageInfo.isHasPreviousPage()} eq false"><span>«</span></li>
                                    <li class="active" href="javascript:void(0)" th:utext="'<span>'+${pageInfo.pageNum}+'</span>'"/>
                                    <li th:if="${pageInfo.isHasNextPage()} eq true"><a th:href="@{/Admin/product(pageNo=${pageInfo.getNextPage()})}"><span>»</span></a></li>
                                    <li class="disabled" href="javascript:void(0)" th:if="${pageInfo.isHasNextPage()} eq false"><span>»</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>