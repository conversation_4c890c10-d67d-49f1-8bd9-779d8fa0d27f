<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" th:content="${configs.get('seo_description').value}">
    <meta name="keywords" th:content="${configs.get('seo_keywords').value}">
    <meta name="author" content="企业门户网站">

    <title th:text="${configs.get('website_title').value} ?: '企业门户网站'">企业门户网站</title>

    <!--== Google Fonts ==-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="/static/moban6072/css/css2.css" rel="stylesheet">

    <!--== Bootstrap CSS ==-->
    <link href="/static/moban6072/css/bootstrap.min.css" rel="stylesheet">
    <!--== Icofont CSS ==-->
    <link href="/static/moban6072/css/icofont.css" rel="stylesheet">
    <!--== Swiper CSS ==-->
    <link href="/static/moban6072/css/swiper.min.css" rel="stylesheet">
    <!--== Main Style CSS ==-->
    <link href="/static/moban6072/css/style.css" rel="stylesheet">

</head>

<body>

<!--wrapper start-->
<div class="wrapper home-default-wrapper">

  <!--== Start Header Wrapper ==-->
  <header class="header-wrapper">
    <div class="header-area header-default header-transparent sticky-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-4 col-sm-6 col-lg-2">
            <div class="header-logo-area">
              <a href="/">
                <img class="logo-main" th:src="'/UploadFilePath/config/' + ${configs.get('website_logo').value}"
                     th:alt="${configs.get('website_company').value}" width="161" height="48">
                <img class="logo-light" th:src="'/UploadFilePath/config/' + ${configs.get('website_logo').value}"
                     th:alt="${configs.get('website_company').value}" width="161" height="48">
              </a>
            </div>
          </div>
          <div class="col-lg-7 d-none d-lg-block">
            <div class="header-navigation-area">
              <ul class="main-menu nav position-relative">
                <li><a href="/">首页</a></li>
                <li class="has-submenu"><a href="/About/about.html">关于我们</a>
                  <ul class="submenu-nav">
                    <li><a href="/About/about.html">公司简介</a></li>
                    <li><a href="/About/scope.html">经营范围</a></li>
                    <li><a href="/About/civilization.html">企业文化</a></li>
                    <li><a href="/About/setup.html">人才体系</a></li>
                    <li><a href="/About/partner.html">合作伙伴</a></li>
                  </ul>
                </li>
                <li class="has-submenu"><a href="/Project/projects.html">项目案例</a>
                  <ul class="submenu-nav">
                    <li><a href="/Project/projects.html">项目展示</a></li>
                  </ul>
                </li>
                <li class="has-submenu"><a href="/News/industry.html">新闻中心</a>
                  <ul class="submenu-nav">
                    <li><a href="/News/industry.html">行业资讯</a></li>
                    <li><a href="/News/company.html">公司新闻</a></li>
                  </ul>
                </li>
                <li class="has-submenu"><a href="/Download/download.html">下载中心</a>
                  <ul class="submenu-nav">
                    <li><a href="/Download/download.html">资料下载</a></li>
                  </ul>
                </li>
                <li class="has-submenu"><a href="/Contact/contact.html">联系我们</a>
                  <ul class="submenu-nav">
                    <li><a href="/Contact/contact.html">联系方式</a></li>
                    <li><a href="/Contact/online.html">在线留言</a></li>
                  </ul>
                </li>
                <li><a href="/Talent/talent.html">人才招聘</a></li>
              </ul>
            </div>
          </div>
          <div class="col-8 col-sm-6 col-lg-3">
            <div class="header-action-area">
               
              <div class="header-action-btn">
                <a class="btn-theme" href="contact.html"><span>获取报价</span></a>
              </div>
              <button class="btn-menu d-lg-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasWithBothOptions" aria-controls="offcanvasWithBothOptions">
                <span class="icon-line"></span>
                <span class="icon-line"></span>
                <span class="icon-line"></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!--== End Header Wrapper ==-->
  
  <main class="main-content">
    <!--== Start Hero Area Wrapper ==-->
    <section class="home-slider-area slider-default">
      <div class="home-slider-content">
        <div class="swiper-container home-slider-container">
          <div class="swiper-wrapper">
            <!-- Banner 1 -->
            <div class="swiper-slide" th:if="${configs.get('website_banner1').value != null and configs.get('website_banner1').value != ''}">
              <div class="home-slider-item">
                <div class="slider-content-area bg-img" th:data-bg-img="'/UploadFilePath/config/' + ${configs.get('website_banner1').value}">
                  <div class="container">
                    <div class="row">
                      <div class="col-sm-12 col-md-6 col-lg-5 col-xl-6">
                        <div class="content">
                          <div class="inner-content">
                            <div class="wrap-one">
                              <h2 th:text="${configs.get('website_company').value} + ' - 专业服务'">专业物流服务</h2>
                            </div>
                            <div class="wrap-two">
                              <p th:utext="${configs.get('website_introduce').value}">我们致力于为客户提供高效安全的物流解决方案</p>
                            </div>
                            <div class="wrap-three">
                              <a href="/Contact/contact.html" class="btn-theme">立即联系</a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="bg-overlay"></div>
                </div>
              </div>
            </div>
            <!-- Banner 2 -->
            <div class="swiper-slide" th:if="${configs.get('website_banner2').value != null and configs.get('website_banner2').value != ''}">
              <div class="home-slider-item">
                <div class="slider-content-area bg-img" th:data-bg-img="'/UploadFilePath/config/' + ${configs.get('website_banner2').value}">
                  <div class="container">
                    <div class="row">
                      <div class="col-sm-12 col-md-6 col-lg-5 col-xl-6">
                        <div class="content">
                          <div class="inner-content">
                            <div class="wrap-one">
                              <h2>优质服务，值得信赖</h2>
                            </div>
                            <div class="wrap-two">
                              <p th:utext="${configs.get('website_scope').value}">专业的服务范围和解决方案</p>
                            </div>
                            <div class="wrap-three">
                              <a href="/About/about.html" class="btn-theme">了解更多</a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="bg-overlay"></div>
                </div>
              </div>
            </div>
            <!-- Banner 3 -->
            <div class="swiper-slide" th:if="${configs.get('website_banner3').value != null and configs.get('website_banner3').value != ''}">
              <div class="home-slider-item">
                <div class="slider-content-area bg-img" th:data-bg-img="'/UploadFilePath/config/' + ${configs.get('website_banner3').value}">
                  <div class="container">
                    <div class="row">
                      <div class="col-sm-12 col-md-6 col-lg-5 col-xl-6">
                        <div class="content">
                          <div class="inner-content">
                            <div class="wrap-one">
                              <h2>携手合作，共创未来</h2>
                            </div>
                            <div class="wrap-two">
                              <p>与我们一起开启成功的合作之旅</p>
                            </div>
                            <div class="wrap-three">
                              <a href="/About/partner.html" class="btn-theme">合作伙伴</a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="bg-overlay"></div>
                </div>
              </div>
            </div>
          </div>
          <!-- Add Pagination -->
          <div class="swiper-pagination"></div>
          <!-- Add Navigation -->
          <div class="swiper-button-next"></div>
          <div class="swiper-button-prev"></div>
        </div>
      </div>
    </section>
    <!--== End Hero Area Wrapper ==-->

    <!--== Start Tracking Area Wrapper ==-->
    <div class="tracking-area">
      <div class="container">
        <div class="row">
          <div class="col-lg-6">
            <div class="tracking-form-wrap">
              <h4 class="tracking-title">运单查询</h4>
              <form class="tracking-searchbox">
                <textarea id="nos" class="form-control" rows="3" placeholder="可查询多个运单号，换行隔开，最多100个运单号"></textarea>
                <div class="tracking-buttons">
                  <button class="btn btn-theme" type="button" onclick="queryTrack()">系统查询 <i class="icon icofont-long-arrow-right"></i></button>
                  <button class="btn btn-secondary" type="button" onclick="queryTrackFrom17track()">17Track查询</button>
                  <button class="btn btn-secondary" type="button" onclick="queryTrackFromTrack718()">Track718查询</button>
                </div>
              </form>
            </div>
          </div>
          <div class="col-lg-6" th:if="${configs.get('forbid_show_price').value ne '1'}">
            <div class="price-form-wrap">
              <h4 class="price-title">运费查询</h4>
              <form class="price-searchbox">
                <div class="row">
                  <div class="col-md-6">
                    <select class="form-control select2" name="country" id="country" data-placeholder="请选择国家">
                      <option value="">选择目的地国家</option>
                      <!-- 这里可以添加国家选项，与默认模板保持一致 -->
                    </select>
                  </div>
                  <div class="col-md-6">
                    <input class="form-control" name="postcode" id="postcode" placeholder="邮编">
                  </div>
                </div>
                <div class="row mt-3">
                  <div class="col-md-3">
                    <input type="text" class="form-control" name="weight" id="weightD" placeholder="重量(KG)">
                  </div>
                  <div class="col-md-3">
                    <input class="form-control" name="length" id="lengthD" placeholder="长(cm)">
                  </div>
                  <div class="col-md-3">
                    <input class="form-control" name="width" id="widthD" placeholder="宽(cm)">
                  </div>
                  <div class="col-md-3">
                    <input class="form-control" name="height" id="heightD" placeholder="高(cm)">
                  </div>
                </div>
                <div class="price-buttons mt-3">
                  <button class="btn btn-theme" type="button" onclick="queryPrice()">查询运费 <i class="icon icofont-long-arrow-right"></i></button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--== End Tracking Area Wrapper ==-->

    <!--== Start Service Area Wrapper ==-->
    <section class="service-area service-default-area">
      <div class="container">
        <div class="row">
          <div class="col-lg-12 m-auto">
            <div class="section-title text-center">
              <h4 class="subtitle">优质服务</h4>
              <h2 class="title">项目案例</h2>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12">
            <div class="swiper-container service-slider-container">
              <div class="swiper-wrapper">
                <!-- 动态显示项目数据 -->
                <div class="swiper-slide" th:each="project : ${projects}">
                  <div class="service-item">
                    <div class="thumb">
                      <a th:href="'/Project/projectDetail/id/' + ${project.id}">
                        <img th:src="'/UploadFilePath/project/' + ${project.picture}"
                             th:alt="${project.title}" width="270" height="308">
                      </a>
                    </div>
                    <div class="content">
                      <h4 class="title">
                        <a th:href="'/Project/projectDetail/id/' + ${project.id}" th:text="${project.title}">项目标题</a>
                      </h4>
                      <p th:text="${project.remark}">项目描述</p>
                    </div>
                  </div>
                </div>
                <!-- 如果没有项目数据，显示默认内容 -->
                <div class="swiper-slide" th:if="${#lists.isEmpty(projects)}">
                  <div class="service-item">
                    <div class="thumb">
                      <a href="/Project/projects.html"><img src="/static/moban6072/picture/1.webp" alt="服务项目" width="270" height="308"></a>
                    </div>
                    <div class="content">
                      <h4 class="title"><a href="/Project/projects.html">专业服务</a></h4>
                      <p>我们提供专业的服务解决方案</p>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Add Pagination -->
              <div class="swiper-pagination"></div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12">
            <p class="get-started">我们提供7×24小时客户服务 <a href="/Project/projects.html">查看更多项目</a></p>
          </div>
        </div>
        <div class="shape-group">
          <div class="shape-style2">
            <img src="/static/moban6072/picture/21.webp" alt="Image" width="353" height="918">
          </div>
          <div class="shape-style3">
            <img src="/static/moban6072/picture/31.webp" alt="Image" width="353" height="918">
          </div>
        </div>
      </div>
    </section>
    <!--== End Service Area Wrapper ==-->

    <!--== Start About Area Wrapper ==-->
    <section class="about-area about-default-area">
      <div class="container">
        <div class="row row-gutter-0">
          <div class="col-md-6 col-lg-4">
            <div class="about-content">
              <div class="section-title">
                <h4 class="subtitle">关于我们</h4>
                <h2 class="title" th:text="${configs.get('website_company').value}">专业服务提供商</h2>
                <p th:utext="${configs.get('website_introduce').value}">我们提供全方位的解决方案和专业服务。</p>
                <p th:utext="${configs.get('website_scope').value}">拥有丰富的行业经验，为客户提供高效可靠的服务。</p>
                <a href="/About/about.html" class="btn-theme">了解更多</a>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-4">
            <div class="about-thumb">
              <img src="/static/moban6072/picture/a1.webp" alt="关于我们" width="350" height="570">
            </div>
          </div>
          <div class="col-md-12 col-lg-4">
            <div class="featured-wrp">
              <div class="featured-item">
                <h4 class="title"><a href="/About/setup.html">专业团队</a></h4>
                <p th:utext="${configs.get('website_talent').value}">拥有专业的团队和丰富的经验</p>
              </div>
              <div class="featured-item">
                <h4 class="title"><a href="/About/civilization.html">企业文化</a></h4>
                <p th:utext="${configs.get('website_civilization').value}">秉承优秀的企业文化和价值观</p>
              </div>
              <div class="featured-item">
                <h4 class="title"><a href="/About/scope.html">服务范围</a></h4>
                <p>提供全方位的专业服务解决方案</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!--== End About Area Wrapper ==-->

    

    <!--== Start Divider Area Wrapper ==-->
    <section class="divider-area divider-style1-area">
      <div class="container">
        <div class="row">
          <div class="col-12">
            <div class="divider-wrap">
              <div class="column-left">
                <div class="content">
                  <p>
                    <span th:text="${configs.get('website_company').value}">我们公司</span>是值得信赖的服务提供商。
                    您可以联系我们获取专业服务。
                  </p>
                </div>
              </div>
              <div class="column-right">
                <div class="content">
                  <h2 class="title" th:text="${configs.get('website_phone').value}">联系电话</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!--== End Divider Area Wrapper ==-->

    <!--== Start Divider Area Wrapper ==-->
    <section class="divider-area divider-default-area">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-sm-8 col-md-7">
            <div class="content">
              <h2 class="title">与我们保持联系<br> 我们会持续更新服务</h2>
            </div>
          </div>
          <div class="col-sm-4 col-md-5">
            <div class="divider-btn">
              <a class="btn-theme btn-white" href="">立即注册 <i class="icofont-rounded-double-right"></i></a>
            </div>
          </div>
        </div>
      </div>
      <div class="shape-group">
        <div class="shape-style4">
          <img src="static/picture/42.webp" alt="Image" width="560" height="250">
        </div>
      </div>
    </section>
    <!--== End Divider Area Wrapper ==-->
  </main>

  <!--== Start Footer Area Wrapper ==-->
  <footer class="footer-area default-style">
    <div class="footer-main">
      <div class="container">
        <div class="row">
          <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="widget-item">
              <h4 class="widget-title">最新资讯</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-1">最新资讯</h4>
              <div id="dividerId-1" class="collapse widget-collapse-body">
                <div class="widget-blog-wrap">
                  <!-- 显示公司新闻 -->
                  <div class="blog-post-item" th:each="news, iterStat : ${companyNews}" th:if="${iterStat.index < 2}">
                    <div class="content">
                      <h4 class="title">
                        <i class="icon icofont-minus"></i>
                        <a th:href="'/News/newsDetail/id/' + ${news.id}" th:text="${news.title}">新闻标题</a>
                      </h4>
                      <div class="meta-date">
                        <a href="/News/company.html">
                          <i class="icofont-calendar"></i>
                          <span th:text="${#dates.format(news.createTime, 'yyyy/MM/dd')}">日期</span>
                        </a>
                      </div>
                    </div>
                  </div>
                  <!-- 如果没有新闻，显示默认内容 -->
                  <div class="blog-post-item" th:if="${#lists.isEmpty(companyNews)}">
                    <div class="content">
                      <h4 class="title"><i class="icon icofont-minus"></i> <a href="/News/company.html">欢迎关注我们的最新动态</a></h4>
                      <div class="meta-date"><a href="/News/company.html"><i class="icofont-calendar"></i> 最新</a></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 offset-lg-0 col-xl-3 offset-xl-1">
            <div class="widget-item">
              <h4 class="widget-title">专业服务</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-2">All Services</h4>
              <div id="dividerId-2" class="collapse widget-collapse-body">
                <nav class="widget-menu-wrap">
                  <ul class="nav-menu nav">
                    <li><a href="service-details.html"><i class="icofont-minus"></i>美国专线</a></li>
                    <li><a href="service-details.html"><i class="icofont-minus"></i>美国空派</a></li>
                    <li><a href="service-details.html"><i class="icofont-minus"></i>美国海派</a></li>
                    <li><a href="service-details.html"><i class="icofont-minus"></i>美国空运</a></li>
                    <li><a href="service-details.html"><i class="icofont-minus"></i>美国海运</a></li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-xl-3">
            <div class="widget-item ml-40 ml-lg-20 md-ml-0">
              <h4 class="widget-title">Important</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-3">Important</h4>
              <div id="dividerId-3" class="collapse widget-collapse-body">
                <nav class="widget-menu-wrap">
                  <ul class="nav-menu nav">
                    <li><a href="about.html"><i class="icofont-minus"></i>About Maskat</a></li>
                    <li><a href=""><i class="icofont-minus"></i>Price & Planning</a></li>
                    <li><a href="contact.html"><i class="icofont-minus"></i>Client Support</a></li>
                    <li><a href=""><i class="icofont-minus"></i>Privacy & Policy</a></li>
                    <li><a href="contact.html"><i class="icofont-minus"></i>Contact Us</a></li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-2 col-xl-2">
            <div class="widget-item ml-35 lg-ml-0">
              <h4 class="widget-title">Follow Us</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-4">Follow Us</h4>
              <div id="dividerId-4" class="collapse widget-collapse-body">
                <nav class="widget-menu-wrap">
                  <ul class="nav-menu nav">
                    <li><a href=""><i class="icofont-minus"></i>Facebook</a></li>
                    <li><a href=""><i class="icofont-minus"></i>Twitter</a></li>
                    <li><a href=""><i class="icofont-minus"></i>Instragram</a></li>
                    <li><a href=""><i class="icofont-minus"></i>Youtube</a></li>
                    <li><a href=""><i class="icofont-minus"></i>Medium</a></li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-shape bg-img" data-bg-img="/static/moban6072/assets/img/photos/footer1.webp"></div>
    </div>
    <div class="footer-bottom">
      <div class="container">
        <div class="footer-bottom-content">
          <div class="row">
            <div class="col-md-12">
              <div class="widget-copyright">
                <p>
                  Copyright &copy; 2025 <span th:text="${configs.get('website_company').value}">公司名称</span> 版权所有
                  <span th:if="${configs.get('website_record').value != null and configs.get('website_record').value != ''}"
                        th:text="'| ' + ${configs.get('website_record').value}"></span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <!--== End Footer Area Wrapper ==-->
  
  <!--== Scroll Top Button ==-->
  <div class="scroll-to-top"><span class="icofont-rounded-double-left icofont-rotate-90"></span></div>

  <!--== Start Side Menu ==-->
  <aside class="off-canvas-area offcanvas offcanvas-end" data-bs-scroll="true" tabindex="-1" id="offcanvasWithBothOptions">
    <div class="offcanvas-header">
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Start Mobile Menu Wrapper -->
      <div class="res-mobile-menu">
        <nav id="offcanvasNav" class="offcanvas-menu">
          <ul>
            <li><a class="color-theme" href="">首页</a></li>
            <li><a href="javascript:void(0)">服务</a>
              <ul>
                <li><a href="services.html">Services</a></li>
                <li><a href="service-details.html">Service Details</a></li>
              </ul>
            </li>
            <li><a href="javascript:void(0)">项目</a>
              <ul>
                <li><a href="projects.html">Portfolio</a></li>
                <li><a href="project-details.html">Portfolio Details</a></li>
              </ul>
            </li>
            <li><a href="javascript:void(0)">页面</a>
              <ul>
                <li><a href="about.html">About</a></li>
                <li><a href="coming-soon.html">Coming soon</a></li>
                <li><a href="page-not-found.html">404</a></li>
              </ul>
            </li>
            <li><a href="javascript:void(0)">新闻</a>
              <ul>
                <li><a href="blog.html">Blog 3 Column</a></li>
                <li><a href="blog-4-column.html">Blog 4 Column</a></li>
                <li><a href="blog-left-sidebar.html">Blog Left Sidebar</a></li>
                <li><a href="blog-right-sidebar.html">Blog Right Sidebar</a></li>
                <li><a href="blog.html">Blog No Sidebar</a></li>
                <li><a href="blog-details.html">Blog Details</a></li>
              </ul>
            </li>
            <li><a href="contact.html">联系我们</a></li>
          </ul>
        </nav>
      </div>
      <!-- End Mobile Menu Wrapper -->
    </div>
  </aside>
  <!--== End Side Menu ==-->
</div>

<!--=======================Javascript============================-->

<!--=== Modernizr Min Js ===-->
<script src="/static/moban6072/js/modernizr.js"></script>
<!--=== jQuery Min Js ===-->
<script src="/static/moban6072/js/jquery-main.js"></script>
<!--=== jQuery Migration Min Js ===-->
<script src="/static/moban6072/js/jquery-migrate.js"></script>
<!--=== Popper Min Js ===-->
<script src="/static/moban6072/js/popper.min.js"></script>
<!--=== Bootstrap Min Js ===-->
<script src="/static/moban6072/js/bootstrap.min.js"></script>
<!--=== jquery Swiper Min Js ===-->
<script src="/static/moban6072/js/swiper.min.js"></script>
<!--=== jquery Countdown Js ===-->
<script src="/static/moban6072/js/jquery.countdown.min.js"></script>
<!--=== Isotope Min Js ===-->
<script src="/static/moban6072/js/isotope.pkgd.min.js"></script>

<!--=== Custom Js ===-->
<script src="/static/moban6072/js/custom.js"></script>

<!-- 系统功能JavaScript -->
<script>
    // 运单查询功能
    function queryTrack() {
        var nos = $("#nos").val();
        nos = nos.replace(/\n|\s+/g, ',').trim();
        if (nos === '') {
            alert('请输入运单号');
            return;
        }
        var omsUrl = $('#omsUrl').val();
        var omsCompanyId = $('#omsCompanyId').val();

        url = omsUrl + '/guest-track?companyId=' + omsCompanyId + '&nos=' + nos;
        window.open(url, '_blank');
    }

    function queryTrackFrom17track() {
        var nos = $("#nos").val();
        nos = nos.replace(/\n|\s+/g, ',').trim();
        if (nos === '') {
            alert('请输入运单号');
            return;
        }
        url = "https://t.17track.net/zh-cn#nums=" + nos;
        window.open(url, '_blank');
    }

    function queryTrackFromTrack718() {
        var nos = $("#nos").val();
        nos = nos.replace(/\n|\s+/g, ',').trim();
        if (nos === '') {
            alert('请输入运单号');
            return;
        }
        url = "https://www.track718.com/zh-CN/detail?nums=" + nos;
        window.open(url, '_blank');
    }

    // 运费查询功能
    function queryPrice() {
        var country = $("#country").val();
        var postcode = $("#postcode").val();
        var weightD = $("#weightD").val();
        var lengthD = $("#lengthD").val();
        var widthD = $("#widthD").val();
        var heightD = $("#heightD").val();

        var omsUrl = $('#omsUrl').val();
        var omsCompanyId = $('#omsCompanyId').val();
        url = omsUrl + '/guest-price-calculate?companyId=' + omsCompanyId
            + '&country=' + country
            + '&postcode=' + postcode
            + '&weightD=' + weightD
            + '&lengthD=' + lengthD
            + '&widthD=' + widthD
            + '&heightD=' + heightD;
        window.open(url, '_blank');
    }

    // 初始化Select2（如果需要）
    $(document).ready(function() {
        if (typeof $.fn.select2 !== 'undefined') {
            $('.select2').select2();
        }
    });
</script>

<!-- 隐藏的配置信息 -->
<input type="hidden" id="omsCompanyId" th:value="${configs.get('oms_company_id').value}">
<input type="hidden" id="omsUrl" th:value="${configs.get('oms_url').value}">

</body>

</html>
