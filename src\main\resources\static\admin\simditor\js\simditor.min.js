/*!
* Simditor v2.3.5
* http://simditor.tower.im/
* 2015-11-19
*/
!function(a,b){"function"==typeof define&&define.amd?define("simditor",["jquery","simple-module","simple-hotkeys","simple-uploader"],function(c,d,e,f){return a.Simditor=b(c,d,e,f)}):"object"==typeof exports?module.exports=b(require("jquery"),require("simple-module"),require("simple-hotkeys"),require("simple-uploader")):a.Simditor=b(jQuery,SimpleModule,simple.hotkeys,simple.uploader)}(this,function(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M=function(a,b){function c(){this.constructor=a}for(var d in b)N.call(b,d)&&(a[d]=b[d]);return c.prototype=b.prototype,a.prototype=new c,a.__super__=b.prototype,a},N={}.hasOwnProperty,O=[].indexOf||function(a){for(var b=0,c=this.length;c>b;b++)if(b in this&&this[b]===a)return b;return-1},P=[].slice;return C=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="Selection",c.prototype._range=null,c.prototype._startNodes=null,c.prototype._endNodes=null,c.prototype._containerNode=null,c.prototype._nodes=null,c.prototype._blockNodes=null,c.prototype._rootNodes=null,c.prototype._init=function(){return this.editor=this._module,this._selection=document.getSelection(),this.editor.on("selectionchanged",function(a){return function(b){return a.reset(),a._range=a._selection.getRangeAt(0)}}(this)),this.editor.on("blur",function(a){return function(b){return a.reset()}}(this))},c.prototype.reset=function(){return this._range=null,this._startNodes=null,this._endNodes=null,this._containerNode=null,this._nodes=null,this._blockNodes=null,this._rootNodes=null},c.prototype.clear=function(){var a;try{this._selection.removeAllRanges()}catch(b){a=b}return this.reset()},c.prototype.range=function(a){var b;return a?(this.clear(),this._selection.addRange(a),this._range=a,b=this.editor.util.browser.firefox||this.editor.util.browser.msie,!this.editor.inputManager.focused&&b&&this.editor.body.focus()):!this._range&&this.editor.inputManager.focused&&this._selection.rangeCount&&(this._range=this._selection.getRangeAt(0)),this._range},c.prototype.startNodes=function(){return this._range&&(this._startNodes||(this._startNodes=function(b){return function(){var c;return c=a(b._range.startContainer).parentsUntil(b.editor.body).get(),c.unshift(b._range.startContainer),a(c)}}(this)())),this._startNodes},c.prototype.endNodes=function(){var b;return this._range&&(this._endNodes||(this._endNodes=this._range.collapsed?this.startNodes():(b=a(this._range.endContainer).parentsUntil(this.editor.body).get(),b.unshift(this._range.endContainer),a(b)))),this._endNodes},c.prototype.containerNode=function(){return this._range&&(this._containerNode||(this._containerNode=a(this._range.commonAncestorContainer))),this._containerNode},c.prototype.nodes=function(){return this._range&&(this._nodes||(this._nodes=function(b){return function(){var c;return c=[],b.startNodes().first().is(b.endNodes().first())?c=b.startNodes().get():(b.startNodes().each(function(d,e){var f,g,h,i,j,k,l;return g=a(e),b.endNodes().index(g)>-1?c.push(e):g.parent().is(b.editor.body)||(k=b.endNodes().index(g.parent()))>-1?(f=k&&k>-1?b.endNodes().eq(k-1):b.endNodes().last(),h=g.parent().contents(),l=h.index(g),i=h.index(f),a.merge(c,h.slice(l,i).get())):(h=g.parent().contents(),j=h.index(g),a.merge(c,h.slice(j).get()))}),b.endNodes().each(function(d,e){var f,g,h;return f=a(e),f.parent().is(b.editor.body)||b.startNodes().index(f.parent())>-1?(c.push(e),!1):(g=f.parent().contents(),h=g.index(f),a.merge(c,g.slice(0,h+1)))})),a(a.unique(c))}}(this)())),this._nodes},c.prototype.blockNodes=function(){return this._range?(this._blockNodes||(this._blockNodes=function(a){return function(){return a.nodes().filter(function(b,c){return a.editor.util.isBlockNode(c)})}}(this)()),this._blockNodes):void 0},c.prototype.rootNodes=function(){return this._range?(this._rootNodes||(this._rootNodes=function(b){return function(){return b.nodes().filter(function(c,d){var e;return e=a(d).parent(),e.is(b.editor.body)||e.is("blockquote")})}}(this)()),this._rootNodes):void 0},c.prototype.rangeAtEndOf=function(b,c){var d,e,f,g,h,i;return null==c&&(c=this.range()),c&&c.collapsed?(b=a(b)[0],f=c.endContainer,g=this.editor.util.getNodeLength(f),e=c.endOffset===g-1,h=a(f).contents().last().is("br"),d=c.endOffset===g,e&&h||d?b===f?!0:a.contains(b,f)?(i=!0,a(f).parentsUntil(b).addBack().each(function(b,c){var d,e,f,g;return g=a(c).parent().contents().filter(function(){return!(this!==c&&3===this.nodeType&&!this.nodeValue)}),d=g.last(),f=d.get(0)===c,e=d.is("br")&&d.prev().get(0)===c,f||e?void 0:(i=!1,!1)}),i):!1:!1):void 0},c.prototype.rangeAtStartOf=function(b,c){var d,e;return null==c&&(c=this.range()),c&&c.collapsed?(b=a(b)[0],e=c.startContainer,0!==c.startOffset?!1:b===e?!0:a.contains(b,e)?(d=!0,a(e).parentsUntil(b).addBack().each(function(b,c){var e;return e=a(c).parent().contents().filter(function(){return!(this!==c&&3===this.nodeType&&!this.nodeValue)}),e.first().get(0)!==c?d=!1:void 0}),d):!1):void 0},c.prototype.insertNode=function(b,c){return null==c&&(c=this.range()),c?(b=a(b)[0],c.insertNode(b),this.setRangeAfter(b,c)):void 0},c.prototype.setRangeAfter=function(b,c){return null==c&&(c=this.range()),null!=c?(b=a(b)[0],c.setEndAfter(b),c.collapse(!1),this.range(c)):void 0},c.prototype.setRangeBefore=function(b,c){return null==c&&(c=this.range()),null!=c?(b=a(b)[0],c.setEndBefore(b),c.collapse(!1),this.range(c)):void 0},c.prototype.setRangeAtStartOf=function(b,c){return null==c&&(c=this.range()),b=a(b).get(0),c.setEnd(b,0),c.collapse(!1),this.range(c)},c.prototype.setRangeAtEndOf=function(b,c){var d,e,f,g,h,i,j;return null==c&&(c=this.range()),e=a(b),b=e[0],e.is("pre")?(f=e.contents(),f.length>0?(g=f.last(),i=g.text(),h=this.editor.util.getNodeLength(g[0]),"\n"===i.charAt(i.length-1)?c.setEnd(g[0],h-1):c.setEnd(g[0],h)):c.setEnd(b,0)):(j=this.editor.util.getNodeLength(b),3!==b.nodeType&&j>0&&(d=a(b).contents().last(),d.is("br")?j-=1:3!==d[0].nodeType&&this.editor.util.isEmptyNode(d)&&(d.append(this.editor.util.phBr),b=d[0],j=0)),c.setEnd(b,j)),c.collapse(!1),this.range(c)},c.prototype.deleteRangeContents=function(a){var b,c,d,e;return null==a&&(a=this.range()),e=a.cloneRange(),d=a.cloneRange(),e.collapse(!0),d.collapse(!1),c=this.rangeAtStartOf(this.editor.body,e),b=this.rangeAtEndOf(this.editor.body,d),!a.collapsed&&c&&b?(this.editor.body.empty(),a.setStart(this.editor.body[0],0),a.collapse(!0),this.range(a)):a.deleteContents(),a},c.prototype.breakBlockEl=function(b,c){var d;return null==c&&(c=this.range()),d=a(b),c.collapsed?(c.setStartBefore(d.get(0)),c.collapsed?d:d.before(c.extractContents())):d},c.prototype.save=function(b){var c,d,e;return null==b&&(b=this.range()),this._selectionSaved?void 0:(d=b.cloneRange(),d.collapse(!1),e=a("<span/>").addClass("simditor-caret-start"),c=a("<span/>").addClass("simditor-caret-end"),d.insertNode(c[0]),b.insertNode(e[0]),this.clear(),this._selectionSaved=!0)},c.prototype.restore=function(){var a,b,c,d,e,f,g;return this._selectionSaved?(e=this.editor.body.find(".simditor-caret-start"),a=this.editor.body.find(".simditor-caret-end"),e.length&&a.length?(f=e.parent(),g=f.contents().index(e),b=a.parent(),c=b.contents().index(a),f[0]===b[0]&&(c-=1),d=document.createRange(),d.setStart(f.get(0),g),d.setEnd(b.get(0),c),e.remove(),a.remove(),this.range(d)):(e.remove(),a.remove()),this._selectionSaved=!1,d):!1},c}(b),n=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="Formatter",c.prototype.opts={allowedTags:[],allowedAttributes:{},allowedStyles:{}},c.prototype._init=function(){return this.editor=this._module,this._allowedTags=a.merge(["br","span","a","img","b","strong","i","strike","u","font","p","ul","ol","li","blockquote","pre","code","h1","h2","h3","h4","hr"],this.opts.allowedTags),this._allowedAttributes=a.extend({img:["src","alt","width","height","data-non-image"],a:["href","target"],font:["color"],code:["class"]},this.opts.allowedAttributes),this._allowedStyles=a.extend({span:["color","font-size"],b:["color"],i:["color"],strong:["color"],strike:["color"],u:["color"],p:["margin-left","text-align"],h1:["margin-left","text-align"],h2:["margin-left","text-align"],h3:["margin-left","text-align"],h4:["margin-left","text-align"]},this.opts.allowedStyles),this.editor.body.on("click","a",function(a){return!1})},c.prototype.decorate=function(a){return null==a&&(a=this.editor.body),this.editor.trigger("decorate",[a]),a},c.prototype.undecorate=function(a){return null==a&&(a=this.editor.body.clone()),this.editor.trigger("undecorate",[a]),a},c.prototype.autolink=function(b){var c,d,e,f,g,h,i,j,k,l,m,n,o;for(null==b&&(b=this.editor.body),i=[],e=function(c){return c.contents().each(function(c,d){var f,g;return f=a(d),f.is("a")||f.closest("a, pre",b).length?void 0:!f.is("iframe")&&f.contents().length?e(f):(g=f.text())&&/https?:\/\/|www\./gi.test(g)?i.push(f):void 0})},e(b),k=/(https?:\/\/|www\.)[\w\-\.\?&=\/#%:,@\!\+]+/gi,f=0,h=i.length;h>f;f++){for(d=i[f],n=d.text(),l=[],j=null,g=0;null!==(j=k.exec(n));)m=n.substring(g,j.index),l.push(document.createTextNode(m)),g=k.lastIndex,o=/^(http(s)?:\/\/|\/)/.test(j[0])?j[0]:"http://"+j[0],c=a('<a href="'+o+'" rel="nofollow"></a>').text(j[0]),l.push(c[0]);l.push(document.createTextNode(n.substring(g))),d.replaceWith(a(l))}return b},c.prototype.format=function(b){var c,d,e,f,g,h,i,j,k,l;if(null==b&&(b=this.editor.body),b.is(":empty"))return b.append("<p>"+this.editor.util.phBr+"</p>"),b;for(k=b.contents(),e=0,g=k.length;g>e;e++)i=k[e],this.cleanNode(i,!0);for(l=b.contents(),f=0,h=l.length;h>f;f++)j=l[f],c=a(j),c.is("br")?("undefined"!=typeof d&&null!==d&&(d=null),c.remove()):this.editor.util.isBlockNode(j)?c.is("li")?d&&d.is("ul, ol")?d.append(j):(d=a("<ul/>").insertBefore(j),d.append(j)):d=null:((!d||d.is("ul, ol"))&&(d=a("<p/>").insertBefore(j)),d.append(j),this.editor.util.isEmptyNode(d)&&d.append(this.editor.util.phBr));return b},c.prototype.cleanNode=function(b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u;if(f=a(b),f.length>0){if(3===f[0].nodeType)return t=f.text().replace(/(\r\n|\n|\r)/gm,""),void(t?(u=document.createTextNode(t),f.replaceWith(u)):f.remove());if(k=f.is("iframe")?null:f.contents(),l=this.editor.util.isDecoratedNode(f),f.is(this._allowedTags.join(","))||l){if(f.is("a")&&(e=f.find("img")).length>0&&(f.replaceWith(e),f=e,k=null),f.is("td")&&(d=f.find(this.editor.util.blockNodes.join(","))).length>0&&(d.each(function(b){return function(b,c){return a(c).contents().unwrap()}}(this)),k=f.contents()),f.is("img")&&f.hasClass("uploading")&&f.remove(),!l){for(i=this._allowedAttributes[f[0].tagName.toLowerCase()],r=a.makeArray(f[0].attributes),m=0,o=r.length;o>m;m++)j=r[m],"style"!==j.name&&(null!=i&&(s=j.name,O.call(i,s)>=0)||f.removeAttr(j.name));this._cleanNodeStyles(f),f.is("span")&&0===f[0].attributes.length&&f.contents().first().unwrap()}}else 1!==f[0].nodeType||f.is(":empty")?(f.remove(),k=null):f.is("div, article, dl, header, footer, tr")?(f.append("<br/>"),k.first().unwrap()):f.is("table")?(g=a("<p/>"),f.find("tr").each(function(b,c){return g.append(a(c).text()+"<br/>")}),f.replaceWith(g),k=null):f.is("thead, tfoot")?(f.remove(),k=null):f.is("th")?(h=a("<td/>").append(f.contents()),f.replaceWith(h)):k.first().unwrap();if(c&&null!=k&&!f.is("pre"))for(n=0,p=k.length;p>n;n++)q=k[n],this.cleanNode(q,!0);return null}},c.prototype._cleanNodeStyles=function(b){var c,d,e,f,g,h,i,j,k;if(j=b.attr("style")){if(b.removeAttr("style"),c=this._allowedStyles[b[0].tagName.toLowerCase()],!(c&&c.length>0))return b;for(k={},g=j.split(";"),d=0,e=g.length;e>d;d++)i=g[d],i=a.trim(i),f=i.split(":"),(f.length=2)&&(h=f[0],O.call(c,h)>=0&&(k[a.trim(f[0])]=a.trim(f[1])));return Object.keys(k).length>0&&b.css(k),b}},c.prototype.clearHtml=function(b,c){var d,e,f;return null==c&&(c=!0),d=a("<div/>").append(b),e=d.contents(),f="",e.each(function(b){return function(d,g){var h,i;return 3===g.nodeType?f+=g.nodeValue:1===g.nodeType&&(h=a(g),i=h.is("iframe")?null:h.contents(),i&&i.length>0&&(f+=b.clearHtml(i)),c&&d<e.length-1&&h.is("br, p, div, li,tr, pre, address, artticle, aside, dl, figcaption, footer, h1, h2,h3, h4, header"))?f+="\n":void 0}}(this)),f},c.prototype.beautify=function(b){var c;return c=function(a){return!!(a.is("p")&&!a.text()&&a.children(":not(br)").length<1)},b.each(function(b,d){var e,f;return e=a(d),f=e.is(':not(img, br, col, td, hr, [class^="simditor-"]):empty'),(f||c(e))&&e.remove(),e.find(':not(img, br, col, td, hr, [class^="simditor-"]):empty').remove()})},c}(b),t=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="InputManager",c.prototype._modifierKeys=[16,17,18,91,93,224],c.prototype._arrowKeys=[37,38,39,40],c.prototype._init=function(){var b,c;return this.editor=this._module,this.throttledValueChanged=this.editor.util.throttle(function(a){return function(b){return setTimeout(function(){return a.editor.trigger("valuechanged",b)},10)}}(this),300),this.throttledSelectionChanged=this.editor.util.throttle(function(a){return function(){return a.editor.trigger("selectionchanged")}}(this),50),a(document).on("selectionchange.simditor"+this.editor.id,function(a){return function(b){var c;if(a.focused&&!a.editor.clipboard.pasting)return(c=function(){return a._selectionTimer&&(clearTimeout(a._selectionTimer),a._selectionTimer=null),a.editor.selection._selection.rangeCount>0?a.throttledSelectionChanged():a._selectionTimer=setTimeout(function(){return a._selectionTimer=null,a.focused?c():void 0},10)})()}}(this)),this.editor.on("valuechanged",function(b){return function(){var c;return b.lastCaretPosition=null,c=b.editor.body.children().filter(function(a,c){return b.editor.util.isBlockNode(c)}),b.focused&&0===c.length&&(b.editor.selection.save(),b.editor.formatter.format(),b.editor.selection.restore()),b.editor.body.find("hr, pre, .simditor-table").each(function(c,d){var e,f;return e=a(d),(e.parent().is("blockquote")||e.parent()[0]===b.editor.body[0])&&(f=!1,0===e.next().length&&(a("<p/>").append(b.editor.util.phBr).insertAfter(e),f=!0),0===e.prev().length&&(a("<p/>").append(b.editor.util.phBr).insertBefore(e),f=!0),f)?b.throttledValueChanged():void 0}),b.editor.body.find("pre:empty").append(b.editor.util.phBr),!b.editor.util.support.onselectionchange&&b.focused?b.throttledSelectionChanged():void 0}}(this)),this.editor.body.on("keydown",a.proxy(this._onKeyDown,this)).on("keypress",a.proxy(this._onKeyPress,this)).on("keyup",a.proxy(this._onKeyUp,this)).on("mouseup",a.proxy(this._onMouseUp,this)).on("focus",a.proxy(this._onFocus,this)).on("blur",a.proxy(this._onBlur,this)).on("drop",a.proxy(this._onDrop,this)).on("input",a.proxy(this._onInput,this)),this.editor.util.browser.firefox&&(this.editor.hotkeys.add("cmd+left",function(a){return function(b){return b.preventDefault(),a.editor.selection._selection.modify("move","backward","lineboundary"),!1}}(this)),this.editor.hotkeys.add("cmd+right",function(a){return function(b){return b.preventDefault(),a.editor.selection._selection.modify("move","forward","lineboundary"),!1}}(this)),b=this.editor.util.os.mac?"cmd+a":"ctrl+a",this.editor.hotkeys.add(b,function(a){return function(b){var c,d,e,f;return c=a.editor.body.children(),c.length>0?(d=c.first().get(0),e=c.last().get(0),f=document.createRange(),f.setStart(d,0),f.setEnd(e,a.editor.util.getNodeLength(e)),a.editor.selection.range(f),!1):void 0}}(this))),c=this.editor.util.os.mac?"cmd+enter":"ctrl+enter",this.editor.hotkeys.add(c,function(a){return function(b){return a.editor.el.closest("form").find("button:submit").click(),!1}}(this))},c.prototype._onFocus=function(a){return this.editor.clipboard.pasting?void 0:(this.editor.el.addClass("focus").removeClass("error"),this.focused=!0,setTimeout(function(a){return function(){var b,c;return c=a.editor.selection._selection.getRangeAt(0),c.startContainer===a.editor.body[0]&&(a.lastCaretPosition?a.editor.undoManager.caretPosition(a.lastCaretPosition):(b=a.editor.body.children().first(),c=document.createRange(),a.editor.selection.setRangeAtStartOf(b,c))),a.lastCaretPosition=null,a.editor.triggerHandler("focus"),a.editor.util.support.onselectionchange?void 0:a.throttledSelectionChanged()}}(this),0))},c.prototype._onBlur=function(a){var b;if(!this.editor.clipboard.pasting)return this.editor.el.removeClass("focus"),this.editor.sync(),this.focused=!1,this.lastCaretPosition=null!=(b=this.editor.undoManager.currentState())?b.caret:void 0,this.editor.triggerHandler("blur")},c.prototype._onMouseUp=function(a){return this.editor.util.support.onselectionchange?void 0:this.throttledSelectionChanged()},c.prototype._onKeyDown=function(a){var b,c;if(this.editor.triggerHandler(a)===!1)return!1;if(!this.editor.hotkeys.respondTo(a)){if(this.editor.keystroke.respondTo(a))return this.throttledValueChanged(),!1;if(b=a.which,!(O.call(this._modifierKeys,b)>=0||(c=a.which,O.call(this._arrowKeys,c)>=0)||this.editor.util.metaKey(a)&&86===a.which))return this.editor.util.support.oninput||this.throttledValueChanged(["typing"]),null}},c.prototype._onKeyPress=function(a){return this.editor.triggerHandler(a)===!1?!1:void 0},c.prototype._onKeyUp=function(b){var c,d;return this.editor.triggerHandler(b)===!1?!1:!this.editor.util.support.onselectionchange&&(d=b.which,O.call(this._arrowKeys,d)>=0)?void this.throttledValueChanged():void(8!==b.which&&46!==b.which||!this.editor.util.isEmptyNode(this.editor.body)||(this.editor.body.empty(),c=a("<p/>").append(this.editor.util.phBr).appendTo(this.editor.body),this.editor.selection.setRangeAtStartOf(c)))},c.prototype._onDrop=function(a){return this.editor.triggerHandler(a)===!1?!1:this.throttledValueChanged()},c.prototype._onInput=function(a){return this.throttledValueChanged(["oninput"])},c}(b),v=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="Keystroke",c.prototype._init=function(){return this.editor=this._module,this._keystrokeHandlers={},this._initKeystrokeHandlers()},c.prototype.add=function(a,b,c){return a=a.toLowerCase(),a=this.editor.hotkeys.constructor.aliases[a]||a,this._keystrokeHandlers[a]||(this._keystrokeHandlers[a]={}),this._keystrokeHandlers[a][b]=c},c.prototype.respondTo=function(b){var c,d,e,f;return(d=null!=(e=this.editor.hotkeys.constructor.keyNameMap[b.which])?e.toLowerCase():void 0)&&d in this._keystrokeHandlers&&(f="function"==typeof(c=this._keystrokeHandlers[d])["*"]?c["*"](b):void 0,f||this.editor.selection.startNodes().each(function(c){return function(e,g){var h,i;if(g.nodeType===Node.ELEMENT_NODE)return h=null!=(i=c._keystrokeHandlers[d])?i[g.tagName.toLowerCase()]:void 0,f="function"==typeof h?h(b,a(g)):void 0,f===!0||f===!1?!1:void 0}}(this)),f)?!0:void 0},c.prototype._initKeystrokeHandlers=function(){var b;return this.editor.util.browser.safari&&this.add("enter","*",function(b){return function(c){var d,e;if(c.shiftKey&&(d=b.editor.selection.blockNodes().last(),!d.is("pre")))return e=a("<br/>"),b.editor.selection.rangeAtEndOf(d)?(b.editor.selection.insertNode(e),b.editor.selection.insertNode(a("<br/>")),b.editor.selection.setRangeBefore(e)):b.editor.selection.insertNode(e),!0}}(this)),(this.editor.util.browser.webkit||this.editor.util.browser.msie)&&(b=function(b){return function(c,d){var e;if(b.editor.selection.rangeAtEndOf(d))return e=a("<p/>").append(b.editor.util.phBr).insertAfter(d),b.editor.selection.setRangeAtStartOf(e),!0}}(this),this.add("enter","h1",b),this.add("enter","h2",b),this.add("enter","h3",b),this.add("enter","h4",b),this.add("enter","h5",b),this.add("enter","h6",b)),this.add("backspace","*",function(a){return function(b){var c,d,e,f;return e=a.editor.selection.rootNodes().first(),d=e.prev(),d.is("hr")&&a.editor.selection.rangeAtStartOf(e)?(a.editor.selection.save(),d.remove(),a.editor.selection.restore(),!0):(c=a.editor.selection.blockNodes().last(),f=a.editor.util.browser.webkit,f&&a.editor.selection.rangeAtStartOf(c)?(a.editor.selection.save(),a.editor.formatter.cleanNode(c,!0),a.editor.selection.restore(),null):void 0)}}(this)),this.add("enter","li",function(b){return function(c,d){var e,f,g,h;if(e=d.clone(),e.find("ul, ol").remove(),b.editor.util.isEmptyNode(e)&&d.is(b.editor.selection.blockNodes().last())){if(f=d.parent(),d.next("li").length>0){if(!b.editor.util.isEmptyNode(d))return;f.parent("li").length>0?(g=a("<li/>").append(b.editor.util.phBr).insertAfter(f.parent("li")),h=a("<"+f[0].tagName+"/>").append(d.nextAll("li")),g.append(h)):(g=a("<p/>").append(b.editor.util.phBr).insertAfter(f),h=a("<"+f[0].tagName+"/>").append(d.nextAll("li")),g.after(h))}else f.parent("li").length>0?(g=a("<li/>").insertAfter(f.parent("li")),g.append(d.contents().length>0?d.contents():b.editor.util.phBr)):(g=a("<p/>").append(b.editor.util.phBr).insertAfter(f),d.children("ul, ol").length>0&&g.after(d.children("ul, ol")));return d.prev("li").length?d.remove():f.remove(),b.editor.selection.setRangeAtStartOf(g),!0}}}(this)),this.add("enter","pre",function(b){return function(c,d){var e,f,g;return c.preventDefault(),c.shiftKey?(e=a("<p/>").append(b.editor.util.phBr).insertAfter(d),b.editor.selection.setRangeAtStartOf(e),!0):(g=b.editor.selection.range(),f=null,g.deleteContents(),!b.editor.util.browser.msie&&b.editor.selection.rangeAtEndOf(d)?(f=document.createTextNode("\n\n"),g.insertNode(f),g.setEnd(f,1)):(f=document.createTextNode("\n"),g.insertNode(f),g.setStartAfter(f)),g.collapse(!1),b.editor.selection.range(g),!0)}}(this)),this.add("enter","blockquote",function(a){return function(b,c){var d,e;return d=a.editor.selection.blockNodes().last(),d.is("p")&&!d.next().length&&a.editor.util.isEmptyNode(d)?(c.after(d),e=document.createRange(),a.editor.selection.setRangeAtStartOf(d,e),!0):void 0}}(this)),this.add("backspace","li",function(b){return function(c,d){var e,f,g,h,i,j,k,l,m;return f=d.children("ul, ol"),i=d.prev("li"),f.length>0&&i.length>0?(m="",j=null,d.contents().each(function(b,c){if(1===c.nodeType&&/UL|OL/.test(c.nodeName))return!1;if(1!==c.nodeType||!/BR/.test(c.nodeName))return 3===c.nodeType&&c.nodeValue?m+=c.nodeValue:1===c.nodeType&&(m+=a(c).text()),j=a(c)}),k=b.editor.util.browser.firefox&&!j.next("br").length,j&&1===m.length&&k?(e=a(b.editor.util.phBr).insertAfter(j),j.remove(),b.editor.selection.setRangeBefore(e),!0):m.length>0?!1:(l=document.createRange(),h=i.children("ul, ol"),h.length>0?(g=a("<li/>").append(b.editor.util.phBr).appendTo(h),h.append(f.children("li")),d.remove(),b.editor.selection.setRangeAtEndOf(g,l)):(b.editor.selection.setRangeAtEndOf(i,l),i.append(f),d.remove(),b.editor.selection.range(l)),!0)):!1}}(this)),this.add("backspace","pre",function(b){return function(c,d){var e,f,g;if(b.editor.selection.rangeAtStartOf(d))return f=d.html().replace("\n","<br/>")||b.editor.util.phBr,e=a("<p/>").append(f).insertAfter(d),d.remove(),g=document.createRange(),b.editor.selection.setRangeAtStartOf(e,g),!0}}(this)),this.add("backspace","blockquote",function(a){return function(b,c){var d,e;if(a.editor.selection.rangeAtStartOf(c))return d=c.children().first().unwrap(),e=document.createRange(),a.editor.selection.setRangeAtStartOf(d,e),!0}}(this))},c}(b),J=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="UndoManager",c.prototype._index=-1,c.prototype._capacity=20,c.prototype._startPosition=null,c.prototype._endPosition=null,c.prototype._init=function(){var a,b;return this.editor=this._module,this._stack=[],this.editor.util.os.mac?(b="cmd+z",a="shift+cmd+z"):this.editor.util.os.win?(b="ctrl+z",a="ctrl+y"):(b="ctrl+z",a="shift+ctrl+z"),this.editor.hotkeys.add(b,function(a){return function(b){return b.preventDefault(),a.undo(),!1}}(this)),this.editor.hotkeys.add(a,function(a){return function(b){return b.preventDefault(),a.redo(),!1}}(this)),this.throttledPushState=this.editor.util.throttle(function(a){return function(){return a._pushUndoState()}}(this),2e3),this.editor.on("valuechanged",function(a){return function(b,c){return"undo"!==c&&"redo"!==c?a.throttledPushState():void 0}}(this)),this.editor.on("selectionchanged",function(a){return function(b){return a.resetCaretPosition()}}(this)),this.editor.on("focus",function(a){return function(b){return 0===a._stack.length?a._pushUndoState():void 0}}(this)),this.editor.on("blur",function(a){return function(b){return a.resetCaretPosition()}}(this))},c.prototype.resetCaretPosition=function(){return this._startPosition=null,this._endPosition=null},c.prototype.startPosition=function(){return this.editor.selection._range&&(this._startPosition||(this._startPosition=this._getPosition("start"))),this._startPosition},c.prototype.endPosition=function(){return this.editor.selection._range&&(this._endPosition||(this._endPosition=function(a){return function(){var b;return b=a.editor.selection.range(),b.collapsed?a._startPosition:a._getPosition("end")}}(this)())),this._endPosition},c.prototype._pushUndoState=function(){var a;if(this.editor.triggerHandler("pushundostate")!==!1&&(a=this.caretPosition(),a.start))return this._index+=1,this._stack.length=this._index,this._stack.push({html:this.editor.body.html(),caret:this.caretPosition()}),this._stack.length>this._capacity?(this._stack.shift(),this._index-=1):void 0},c.prototype.currentState=function(){return this._stack.length&&this._index>-1?this._stack[this._index]:null},c.prototype.undo=function(){var a;if(!(this._index<1||this._stack.length<2))return this.editor.hidePopover(),this._index-=1,a=this._stack[this._index],this.editor.body.html(a.html),this.caretPosition(a.caret),this.editor.body.find(".selected").removeClass("selected"),this.editor.sync(),this.editor.trigger("valuechanged",["undo"])},c.prototype.redo=function(){var a;if(!(this._index<0||this._stack.length<this._index+2))return this.editor.hidePopover(),this._index+=1,a=this._stack[this._index],this.editor.body.html(a.html),this.caretPosition(a.caret),this.editor.body.find(".selected").removeClass("selected"),this.editor.sync(),this.editor.trigger("valuechanged",["redo"])},c.prototype.update=function(){var a,b;return(a=this.currentState())?(b=this.editor.body.html(),a.html=b,a.caret=this.caretPosition()):void 0},c.prototype._getNodeOffset=function(b,c){var d,e,f;return d=a.isNumeric(c)?a(b):a(b).parent(),f=0,e=!1,d.contents().each(function(a,d){return b===d||c===a&&0===a?!1:(d.nodeType===Node.TEXT_NODE?!e&&d.nodeValue.length>0&&(f+=1,e=!0):(f+=1,e=!1),c-1===a?!1:null)}),f},c.prototype._getPosition=function(b){var c,d,e,f,g,h,i;if(null==b&&(b="start"),i=this.editor.selection.range(),f=i[b+"Offset"],c=this.editor.selection[b+"Nodes"](),d=c.first()[0],d.nodeType===Node.TEXT_NODE){for(h=d.previousSibling;h&&h.nodeType===Node.TEXT_NODE;)d=h,f+=this.editor.util.getNodeLength(h),h=h.previousSibling;e=c.get(),e[0]=d,c=a(e)}else f=this._getNodeOffset(d,f);return g=[f],c.each(function(a){return function(b,c){return g.unshift(a._getNodeOffset(c))}}(this)),g},c.prototype._getNodeByPosition=function(b){var c,d,e,f,g,h,i,j;for(h=this.editor.body[0],j=b.slice(0,b.length-1),e=f=0,g=j.length;g>f;e=++f){if(i=j[e],d=h.childNodes,i>d.length-1){if(e!==b.length-2||!a(h).is("pre:empty")){h=null;break}c=document.createTextNode(""),h.appendChild(c),d=h.childNodes}h=d[i]}return h},c.prototype.caretPosition=function(a){var b,c,d,e,f;if(a){if(!a.start)return;return e=this._getNodeByPosition(a.start),f=a.start[a.start.length-1],a.collapsed?(b=e,c=f):(b=this._getNodeByPosition(a.end),c=a.start[a.start.length-1]),e&&b?(d=document.createRange(),d.setStart(e,f),d.setEnd(b,c),this.editor.selection.range(d)):void("undefined"!=typeof console&&null!==console&&"function"==typeof console.warn&&console.warn("simditor: invalid caret state"))}return d=this.editor.selection.range(),a=this.editor.inputManager.focused&&null!=d?{start:this.startPosition(),end:this.endPosition(),collapsed:d.collapsed}:{}},c}(b),L=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="Util",c.prototype._init=function(){return this.editor=this._module,this.browser.msie&&this.browser.version<11?this.phBr="":void 0},c.prototype.phBr="<br/>",c.prototype.os=function(){var a;return a={},/Mac/.test(navigator.appVersion)?a.mac=!0:/Linux/.test(navigator.appVersion)?a.linux=!0:/Win/.test(navigator.appVersion)?a.win=!0:/X11/.test(navigator.appVersion)&&(a.unix=!0),/Mobi/.test(navigator.appVersion)&&(a.mobile=!0),a}(),c.prototype.browser=function(){var a,b,c,d,e,f,g,h,i,j,k;return k=navigator.userAgent,d=/(msie|trident)/i.test(k),a=/chrome|crios/i.test(k),j=/safari/i.test(k)&&!a,c=/firefox/i.test(k),b=/edge/i.test(k),d?{msie:!0,version:1*(null!=(e=k.match(/(msie |rv:)(\d+(\.\d+)?)/i))?e[2]:void 0)}:b?{edge:!0,webkit:!0,version:1*(null!=(f=k.match(/edge\/(\d+(\.\d+)?)/i))?f[1]:void 0)}:a?{webkit:!0,chrome:!0,version:1*(null!=(g=k.match(/(?:chrome|crios)\/(\d+(\.\d+)?)/i))?g[1]:void 0)}:j?{webkit:!0,safari:!0,version:1*(null!=(h=k.match(/version\/(\d+(\.\d+)?)/i))?h[1]:void 0)}:c?{mozilla:!0,firefox:!0,version:1*(null!=(i=k.match(/firefox\/(\d+(\.\d+)?)/i))?i[1]:void 0)}:{}}(),c.prototype.support=function(){return{onselectionchange:function(){var a,b;if(b=document.onselectionchange,void 0!==b)try{return document.onselectionchange=0,null===document.onselectionchange}catch(c){a=c}finally{document.onselectionchange=b}return!1}(),oninput:function(){return!/(msie|trident)/i.test(navigator.userAgent)}()}}(),c.prototype.reflow=function(b){return null==b&&(b=document),a(b)[0].offsetHeight},c.prototype.metaKey=function(a){var b;return b=/Mac/.test(navigator.userAgent),b?a.metaKey:a.ctrlKey},c.prototype.isEmptyNode=function(b){var c;return c=a(b),c.is(":empty")||!c.text()&&!c.find(":not(br, span, div)").length},c.prototype.isDecoratedNode=function(b){return a(b).is('[class^="simditor-"]')},c.prototype.blockNodes=["div","p","ul","ol","li","blockquote","hr","pre","h1","h2","h3","h4","h5","table"],c.prototype.isBlockNode=function(b){return b=a(b)[0],b&&3!==b.nodeType?new RegExp("^("+this.blockNodes.join("|")+")$").test(b.nodeName.toLowerCase()):!1},c.prototype.getNodeLength=function(b){switch(b=a(b)[0],b.nodeType){case 7:case 10:return 0;case 3:case 8:return b.length;default:return b.childNodes.length}},c.prototype.dataURLtoBlob=function(a){var b,c,d,e,f,g,h,i,j,k,l,m,n;if(h=window.Blob&&function(){var a;try{return Boolean(new Blob)}catch(b){return a=b,!1}}(),g=h&&window.Uint8Array&&function(){var a;try{return 100===new Blob([new Uint8Array(100)]).size}catch(b){return a=b,!1}}(),b=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,n=h||b,!(n&&window.atob&&window.ArrayBuffer&&window.Uint8Array))return!1;for(f=a.split(",")[0].indexOf("base64")>=0?atob(a.split(",")[1]):decodeURIComponent(a.split(",")[1]),c=new ArrayBuffer(f.length),j=new Uint8Array(c),i=k=0,m=f.length;m>=0?m>=k:k>=m;i=m>=0?++k:--k)j[i]=f.charCodeAt(i);return l=a.split(",")[0].split(":")[1].split(";")[0],h?(e=g?j:c,new Blob([e],{type:l})):(d=new b,d.append(c),d.getBlob(l))},c.prototype.throttle=function(a,b){var c,d,e,f,g,h,i;return f=0,i=0,e=c=g=null,d=function(){return i=0,f=+new Date,g=a.apply(e,c),e=null,c=null},h=function(){var a;return e=this,c=arguments,a=new Date-f,i||(a>=b?d():i=setTimeout(d,b-a)),g},h.clear=function(){return i?(clearTimeout(i),d()):void 0},h},c.prototype.formatHTML=function(b){var c,d,e,f,g,h,i,j,k;for(h=/<(\/?)(.+?)(\/?)>/g,j="",f=0,e=null,d="  ",i=function(a,b){return new Array(b+1).join(a)};null!==(g=h.exec(b));)g.isBlockNode=a.inArray(g[2],this.blockNodes)>-1,g.isStartTag="/"!==g[1]&&"/"!==g[3],g.isEndTag="/"===g[1]||"/"===g[3],c=e?e.index+e[0].length:0,(k=b.substring(c,g.index)).length>0&&a.trim(k)&&(j+=k),g.isBlockNode&&g.isEndTag&&!g.isStartTag&&(f-=1),g.isBlockNode&&g.isStartTag&&(e&&e.isBlockNode&&e.isEndTag||(j+="\n"),j+=i(d,f)),j+=g[0],g.isBlockNode&&g.isEndTag&&(j+="\n"),g.isBlockNode&&g.isStartTag&&(f+=1),e=g;return a.trim(j)},c}(b),H=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="Toolbar",c.prototype.opts={toolbar:!0,
toolbarFloat:!0,toolbarHidden:!1,toolbarFloatOffset:0},c.prototype._tpl={wrapper:'<div class="simditor-toolbar"><ul></ul></div>',separator:'<li><span class="separator"></span></li>'},c.prototype._init=function(){var b,c,d;return this.editor=this._module,this.opts.toolbar?(a.isArray(this.opts.toolbar)||(this.opts.toolbar=["bold","italic","underline","strikethrough","|","ol","ul","blockquote","code","|","link","image","|","indent","outdent"]),this._render(),this.list.on("click",function(a){return!1}),this.wrapper.on("mousedown",function(a){return function(b){return a.list.find(".menu-on").removeClass(".menu-on")}}(this)),a(document).on("mousedown.simditor"+this.editor.id,function(a){return function(b){return a.list.find(".menu-on").removeClass(".menu-on")}}(this)),!this.opts.toolbarHidden&&this.opts.toolbarFloat&&(this.wrapper.css("top",this.opts.toolbarFloatOffset),d=0,c=function(a){return function(){return a.wrapper.css("position","static"),a.wrapper.width("auto"),a.editor.util.reflow(a.wrapper),a.wrapper.width(a.wrapper.outerWidth()),a.wrapper.css("left",a.editor.util.os.mobile?a.wrapper.position().left:a.wrapper.offset().left),a.wrapper.css("position",""),d=a.wrapper.outerHeight(),a.editor.placeholderEl.css("top",d),!0}}(this),b=null,a(window).on("resize.simditor-"+this.editor.id,function(a){return b=c()}),a(window).on("scroll.simditor-"+this.editor.id,function(e){return function(f){var g,h,i;if(e.wrapper.is(":visible"))if(i=e.editor.wrapper.offset().top,g=i+e.editor.wrapper.outerHeight()-80,h=a(document).scrollTop()+e.opts.toolbarFloatOffset,i>=h||h>=g){if(e.editor.wrapper.removeClass("toolbar-floating").css("padding-top",""),e.editor.util.os.mobile)return e.wrapper.css("top",e.opts.toolbarFloatOffset)}else if(b||(b=c()),e.editor.wrapper.addClass("toolbar-floating").css("padding-top",d),e.editor.util.os.mobile)return e.wrapper.css("top",h-i+e.opts.toolbarFloatOffset)}}(this))),this.editor.on("destroy",function(a){return function(){return a.buttons.length=0}}(this)),a(document).on("mousedown.simditor-"+this.editor.id,function(a){return function(b){return a.list.find("li.menu-on").removeClass("menu-on")}}(this))):void 0},c.prototype._render=function(){var b,c,d,e;for(this.buttons=[],this.wrapper=a(this._tpl.wrapper).prependTo(this.editor.wrapper),this.list=this.wrapper.find("ul"),e=this.opts.toolbar,b=0,c=e.length;c>b;b++)if(d=e[b],"|"!==d){if(!this.constructor.buttons[d])throw new Error("simditor: invalid toolbar button "+d);this.buttons.push(new this.constructor.buttons[d]({editor:this.editor}))}else a(this._tpl.separator).appendTo(this.list);return this.opts.toolbarHidden?this.wrapper.hide():void 0},c.prototype.findButton=function(a){var b;return b=this.list.find(".toolbar-item-"+a).data("button"),null!=b?b:null},c.addButton=function(a){return this.buttons[a.prototype.name]=a},c.buttons={},c}(b),s=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="Indentation",c.prototype.opts={tabIndent:!0},c.prototype._init=function(){return this.editor=this._module,this.editor.keystroke.add("tab","*",function(a){return function(b){var c;return c=a.editor.toolbar.findButton("code"),a.opts.tabIndent||c&&c.active?a.indent(b.shiftKey):void 0}}(this))},c.prototype.indent=function(b){var c,d,e,f,g;return e=this.editor.selection.startNodes(),d=this.editor.selection.endNodes(),c=this.editor.selection.blockNodes(),f=[],c=c.each(function(b,c){var d,e,g,h,i;for(d=!0,e=g=0,h=f.length;h>g;e=++g){if(i=f[e],a.contains(c,i)){d=!1;break}if(a.contains(i,c)){f.splice(e,1,c),d=!1;break}}return d?f.push(c):void 0}),c=a(f),g=!1,c.each(function(a){return function(c,d){var e;return e=b?a.outdentBlock(d):a.indentBlock(d),e?g=e:void 0}}(this)),g},c.prototype.indentBlock=function(b){var c,d,e,f,g,h,i,j,k,l;if(c=a(b),c.length){if(c.is("pre")){if(h=this.editor.selection.containerNode(),!h.is(c)&&!h.closest("pre").is(c))return;this.indentText(this.editor.selection.range())}else if(c.is("li")){if(g=c.prev("li"),g.length<1)return;this.editor.selection.save(),l=c.parent()[0].tagName,d=g.children("ul, ol"),d.length>0?d.append(c):a("<"+l+"/>").append(c).appendTo(g),this.editor.selection.restore()}else if(c.is("p, h1, h2, h3, h4"))k=parseInt(c.css("margin-left"))||0,k=(Math.round(k/this.opts.indentWidth)+1)*this.opts.indentWidth,c.css("margin-left",k);else{if(!c.is("table")&&!c.is(".simditor-table"))return!1;if(i=this.editor.selection.containerNode().closest("td, th"),e=i.next("td, th"),e.length>0||(j=i.parent("tr"),f=j.next("tr"),f.length<1&&j.parent().is("thead")&&(f=j.parent("thead").next("tbody").find("tr:first")),e=f.find("td:first, th:first")),!(i.length>0&&e.length>0))return;this.editor.selection.setRangeAtEndOf(e)}return!0}},c.prototype.indentText=function(a){var b,c;return b=a.toString().replace(/^(?=.+)/gm,"  "),c=document.createTextNode(b||"  "),a.deleteContents(),a.insertNode(c),b?(a.selectNode(c),this.editor.selection.range(a)):this.editor.selection.setRangeAfter(c)},c.prototype.outdentBlock=function(b){var c,d,e,f,g,h,i,j,k,l;if(c=a(b),c&&c.length>0){if(c.is("pre")){if(f=this.editor.selection.containerNode(),!f.is(c)&&!f.closest("pre").is(c))return;this.outdentText(l)}else if(c.is("li"))d=c.parent(),e=d.parent("li"),this.editor.selection.save(),e.length<1?(l=document.createRange(),l.setStartBefore(d[0]),l.setEndBefore(c[0]),d.before(l.extractContents()),a("<p/>").insertBefore(d).after(c.children("ul, ol")).append(c.contents()),c.remove()):(c.next("li").length>0&&a("<"+d[0].tagName+"/>").append(c.nextAll("li")).appendTo(c),c.insertAfter(e),d.children("li").length<1&&d.remove()),this.editor.selection.restore();else if(c.is("p, h1, h2, h3, h4"))k=parseInt(c.css("margin-left"))||0,k=Math.max(Math.round(k/this.opts.indentWidth)-1,0)*this.opts.indentWidth,c.css("margin-left",0===k?"":k);else{if(!c.is("table")&&!c.is(".simditor-table"))return!1;if(i=this.editor.selection.containerNode().closest("td, th"),g=i.prev("td, th"),g.length>0||(j=i.parent("tr"),h=j.prev("tr"),h.length<1&&j.parent().is("tbody")&&(h=j.parent("tbody").prev("thead").find("tr:first")),g=h.find("td:last, th:last")),!(i.length>0&&g.length>0))return;this.editor.selection.setRangeAtEndOf(g)}return!0}},c.prototype.outdentText=function(a){},c}(b),i=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.pluginName="Clipboard",c.prototype.opts={pasteImage:!1},c.prototype._init=function(){return this.editor=this._module,this.opts.pasteImage&&"string"!=typeof this.opts.pasteImage&&(this.opts.pasteImage="inline"),this.editor.body.on("paste",function(a){return function(b){var c;if(!a.pasting&&!a._pasteBin)return a.editor.triggerHandler(b)===!1?!1:(c=a.editor.selection.deleteRangeContents(),a.editor.body.html()?c.collapsed||c.collapse(!0):(a.editor.formatter.format(),a.editor.selection.setRangeAtStartOf(a.editor.body.find("p:first"))),a._processPasteByClipboardApi(b)?!1:(a.editor.inputManager.throttledValueChanged.clear(),a.editor.inputManager.throttledSelectionChanged.clear(),a.editor.undoManager.throttledPushState.clear(),a.editor.selection.reset(),a.editor.undoManager.resetCaretPosition(),a.pasting=!0,a._getPasteContent(function(b){return a._processPasteContent(b),a._pasteInBlockEl=null,a._pastePlainText=null,a.pasting=!1})))}}(this))},c.prototype._processPasteByClipboardApi=function(a){var b,c,d,e;if(!this.editor.util.browser.edge&&a.originalEvent.clipboardData&&a.originalEvent.clipboardData.items&&a.originalEvent.clipboardData.items.length>0&&(c=a.originalEvent.clipboardData.items[0],/^image\//.test(c.type))){if(b=c.getAsFile(),null==b||!this.opts.pasteImage)return;if(b.name||(b.name="Clipboard Image.png"),this.editor.triggerHandler("pasting",[b])===!1)return;return e={},e[this.opts.pasteImage]=!0,null!=(d=this.editor.uploader)&&d.upload(b,e),!0}},c.prototype._getPasteContent=function(b){var c;return this._pasteBin=a('<div contenteditable="true" />').addClass("simditor-paste-bin").attr("tabIndex","-1").appendTo(this.editor.el),c={html:this.editor.body.html(),caret:this.editor.undoManager.caretPosition()},this._pasteBin.focus(),setTimeout(function(d){return function(){var e;return d.editor.hidePopover(),d.editor.body.html(c.html),d.editor.undoManager.caretPosition(c.caret),d.editor.body.focus(),d.editor.selection.reset(),d.editor.selection.range(),d._pasteInBlockEl=d.editor.selection.blockNodes().last(),d._pastePlainText=d._pasteInBlockEl.is("pre, table"),d._pastePlainText?e=d.editor.formatter.clearHtml(d._pasteBin.html(),!0):(e=a("<div/>").append(d._pasteBin.contents()),e.find("table colgroup").remove(),d.editor.formatter.format(e),d.editor.formatter.decorate(e),d.editor.formatter.beautify(e.children()),e=e.contents()),d._pasteBin.remove(),d._pasteBin=null,b(e)}}(this),0)},c.prototype._processPasteContent=function(b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y;if(this.editor.triggerHandler("pasting",[b])!==!1&&(c=this._pasteInBlockEl,b)){if(this._pastePlainText)if(c.is("table")){for(q=b.split("\n"),j=q.pop(),h=0,k=q.length;k>h;h++)p=q[h],this.editor.selection.insertNode(document.createTextNode(p)),this.editor.selection.insertNode(a("<br/>"));this.editor.selection.insertNode(document.createTextNode(j))}else for(b=a("<div/>").text(b),v=b.contents(),i=0,l=v.length;l>i;i++)s=v[i],this.editor.selection.insertNode(a(s)[0]);else if(c.is(this.editor.body))for(r=0,m=b.length;m>r;r++)s=b[r],this.editor.selection.insertNode(s);else{if(b.length<1)return;if(1===b.length)if(b.is("p")){if(f=b.contents(),1===f.length&&f.is("img")){if(d=f,/^data:image/.test(d.attr("src"))){if(!this.opts.pasteImage)return;return e=this.editor.util.dataURLtoBlob(d.attr("src")),e.name="Clipboard Image.png",y={},y[this.opts.pasteImage]=!0,void(null!=(w=this.editor.uploader)&&w.upload(e,y))}if(d.is('img[src^="webkit-fake-url://"]'))return}for(t=0,n=f.length;n>t;t++)s=f[t],this.editor.selection.insertNode(s)}else if(c.is("p")&&this.editor.util.isEmptyNode(c))c.replaceWith(b),this.editor.selection.setRangeAtEndOf(b);else if(b.is("ul, ol"))if(1===b.find("li").length)for(b=a("<div/>").text(b.text()),x=b.contents(),u=0,o=x.length;o>u;u++)s=x[u],this.editor.selection.insertNode(a(s)[0]);else c.is("li")?(c.parent().after(b),this.editor.selection.setRangeAtEndOf(b)):(c.after(b),this.editor.selection.setRangeAtEndOf(b));else c.after(b),this.editor.selection.setRangeAtEndOf(b);else c.is("li")&&(c=c.parent()),this.editor.selection.rangeAtStartOf(c)?g="before":this.editor.selection.rangeAtEndOf(c)?g="after":(this.editor.selection.breakBlockEl(c),g="before"),c[g](b),this.editor.selection.setRangeAtEndOf(b.last())}return this.editor.inputManager.throttledValueChanged()}},c}(b),D=function(b){function e(){return e.__super__.constructor.apply(this,arguments)}return M(e,b),e.connect(L),e.connect(t),e.connect(C),e.connect(J),e.connect(v),e.connect(n),e.connect(H),e.connect(s),e.connect(i),e.count=0,e.prototype.opts={textarea:null,placeholder:"",defaultImage:"images/image.png",params:{},upload:!1,indentWidth:40},e.prototype._init=function(){var b,f,g,h;if(this.textarea=a(this.opts.textarea),this.opts.placeholder=this.opts.placeholder||this.textarea.attr("placeholder"),!this.textarea.length)throw new Error("simditor: param textarea is required.");if(f=this.textarea.data("simditor"),null!=f&&f.destroy(),this.id=++e.count,this._render(),!c)throw new Error("simditor: simple-hotkeys is required.");if(this.hotkeys=c({el:this.body}),this.opts.upload&&d&&(h="object"==typeof this.opts.upload?this.opts.upload:{},this.uploader=d(h)),g=this.textarea.closest("form"),g.length&&(g.on("submit.simditor-"+this.id,function(a){return function(){return a.sync()}}(this)),g.on("reset.simditor-"+this.id,function(a){return function(){return a.setValue("")}}(this))),this.on("initialized",function(a){return function(){return a.opts.placeholder&&a.on("valuechanged",function(){return a._placeholder()}),a.setValue(a.textarea.val().trim()||""),a.textarea.attr("autofocus")?a.focus():void 0}}(this)),this.util.browser.mozilla){this.util.reflow();try{return document.execCommand("enableObjectResizing",!1,!1),document.execCommand("enableInlineTableEditing",!1,!1)}catch(i){b=i}}},e.prototype._tpl='<div class="simditor">\n  <div class="simditor-wrapper">\n    <div class="simditor-placeholder"></div>\n    <div class="simditor-body" contenteditable="true">\n    </div>\n  </div>\n</div>',e.prototype._render=function(){var b,c,d,e;if(this.el=a(this._tpl).insertBefore(this.textarea),this.wrapper=this.el.find(".simditor-wrapper"),this.body=this.wrapper.find(".simditor-body"),this.placeholderEl=this.wrapper.find(".simditor-placeholder").append(this.opts.placeholder),this.el.data("simditor",this),this.wrapper.append(this.textarea),this.textarea.data("simditor",this).blur(),this.body.attr("tabindex",this.textarea.attr("tabindex")),this.util.os.mac?this.el.addClass("simditor-mac"):this.util.os.linux&&this.el.addClass("simditor-linux"),this.util.os.mobile&&this.el.addClass("simditor-mobile"),this.opts.params){c=this.opts.params,d=[];for(b in c)e=c[b],d.push(a("<input/>",{type:"hidden",name:b,value:e}).insertAfter(this.textarea));return d}},e.prototype._placeholder=function(){var a;return a=this.body.children(),0===a.length||1===a.length&&this.util.isEmptyNode(a)&&parseInt(a.css("margin-left")||0)<this.opts.indentWidth?this.placeholderEl.show():this.placeholderEl.hide()},e.prototype.setValue=function(a){return this.hidePopover(),this.textarea.val(a),this.body.html(a),this.formatter.format(),this.formatter.decorate(),this.util.reflow(this.body),this.inputManager.lastCaretPosition=null,this.trigger("valuechanged")},e.prototype.getValue=function(){return this.sync()},e.prototype.sync=function(){var b,c,d,e,f,g;for(c=this.body.clone(),this.formatter.undecorate(c),this.formatter.format(c),this.formatter.autolink(c),b=c.children(),f=b.last("p"),e=b.first("p");f.is("p")&&this.util.isEmptyNode(f);)d=f,f=f.prev("p"),d.remove();for(;e.is("p")&&this.util.isEmptyNode(e);)d=e,e=f.next("p"),d.remove();return c.find("img.uploading").remove(),g=a.trim(c.html()),this.textarea.val(g),g},e.prototype.focus=function(){var b,c;return this.body.is(":visible")&&this.body.is("[contenteditable]")?this.inputManager.lastCaretPosition?(this.undoManager.caretPosition(this.inputManager.lastCaretPosition),this.inputManager.lastCaretPosition=null):(b=this.body.children().last(),b.is("p")||(b=a("<p/>").append(this.util.phBr).appendTo(this.body)),c=document.createRange(),this.selection.setRangeAtEndOf(b,c)):void this.el.find("textarea:visible").focus()},e.prototype.blur=function(){return this.body.is(":visible")&&this.body.is("[contenteditable]")?this.body.blur():this.body.find("textarea:visible").blur()},e.prototype.hidePopover=function(){return this.el.find(".simditor-popover").each(function(b,c){return c=a(c).data("popover"),c.active?c.hide():void 0})},e.prototype.destroy=function(){return this.triggerHandler("destroy"),this.textarea.closest("form").off(".simditor .simditor-"+this.id),this.selection.clear(),this.inputManager.focused=!1,this.textarea.insertBefore(this.el).hide().val("").removeData("simditor"),this.el.remove(),a(document).off(".simditor-"+this.id),a(window).off(".simditor-"+this.id),this.off()},e}(b),D.i18n={"zh-CN":{blockquote:"引用",bold:"加粗文字",code:"插入代码",color:"文字颜色",coloredText:"彩色文字",hr:"分隔线",image:"插入图片",externalImage:"外链图片",uploadImage:"上传图片",uploadFailed:"上传失败了",uploadError:"上传出错了",imageUrl:"图片地址",imageSize:"图片尺寸",imageAlt:"图片描述",restoreImageSize:"还原图片尺寸",uploading:"正在上传",indent:"向右缩进",outdent:"向左缩进",italic:"斜体文字",link:"插入链接",linkText:"链接文字",linkUrl:"链接地址",linkTarget:"打开方式",openLinkInCurrentWindow:"在新窗口中打开",openLinkInNewWindow:"在当前窗口中打开",removeLink:"移除链接",ol:"有序列表",ul:"无序列表",strikethrough:"删除线文字",table:"表格",deleteRow:"删除行",insertRowAbove:"在上面插入行",insertRowBelow:"在下面插入行",deleteColumn:"删除列",insertColumnLeft:"在左边插入列",insertColumnRight:"在右边插入列",deleteTable:"删除表格",title:"标题",normalText:"普通文本",underline:"下划线文字",alignment:"水平对齐",alignCenter:"居中",alignLeft:"居左",alignRight:"居右",selectLanguage:"选择程序语言",fontScale:"字体大小",fontScaleXLarge:"超大字体",fontScaleLarge:"大号字体",fontScaleNormal:"正常大小",fontScaleSmall:"小号字体",fontScaleXSmall:"超小字体"},"en-US":{blockquote:"Block Quote",bold:"Bold",code:"Code",color:"Text Color",coloredText:"Colored Text",hr:"Horizontal Line",image:"Insert Image",externalImage:"External Image",uploadImage:"Upload Image",uploadFailed:"Upload failed",uploadError:"Error occurs during upload",imageUrl:"Url",imageSize:"Size",imageAlt:"Alt",restoreImageSize:"Restore Origin Size",uploading:"Uploading",indent:"Indent",outdent:"Outdent",italic:"Italic",link:"Insert Link",linkText:"Text",linkUrl:"Url",linkTarget:"Target",openLinkInCurrentWindow:"Open link in current window",openLinkInNewWindow:"Open link in new window",removeLink:"Remove Link",ol:"Ordered List",ul:"Unordered List",strikethrough:"Strikethrough",table:"Table",deleteRow:"Delete Row",insertRowAbove:"Insert Row Above",insertRowBelow:"Insert Row Below",deleteColumn:"Delete Column",insertColumnLeft:"Insert Column Left",insertColumnRight:"Insert Column Right",deleteTable:"Delete Table",title:"Title",normalText:"Text",underline:"Underline",alignment:"Alignment",alignCenter:"Align Center",alignLeft:"Align Left",alignRight:"Align Right",selectLanguage:"Select Language",fontScale:"Font Size",fontScaleXLarge:"X Large Size",fontScaleLarge:"Large Size",fontScaleNormal:"Normal Size",fontScaleSmall:"Small Size",fontScaleXSmall:"X Small Size"}},h=function(b){function c(a){this.editor=a.editor,this.title=this._t(this.name),c.__super__.constructor.call(this,a)}return M(c,b),c.prototype._tpl={item:'<li><a tabindex="-1" unselectable="on" class="toolbar-item" href="javascript:;"><span></span></a></li>',menuWrapper:'<div class="toolbar-menu"></div>',menuItem:'<li><a tabindex="-1" unselectable="on" class="menu-item" href="javascript:;"><span></span></a></li>',separator:'<li><span class="separator"></span></li>'},c.prototype.name="",c.prototype.icon="",c.prototype.title="",c.prototype.text="",c.prototype.htmlTag="",c.prototype.disableTag="",c.prototype.menu=!1,c.prototype.active=!1,c.prototype.disabled=!1,c.prototype.needFocus=!0,c.prototype.shortcut=null,c.prototype._init=function(){var b,c,d,e;for(this.render(),this.el.on("mousedown",function(a){return function(b){var c,d,e;return b.preventDefault(),d=a.needFocus&&!a.editor.inputManager.focused,a.el.hasClass("disabled")||d?!1:a.menu?(a.wrapper.toggleClass("menu-on").siblings("li").removeClass("menu-on"),a.wrapper.is(".menu-on")&&(c=a.menuWrapper.offset().left+a.menuWrapper.outerWidth()+5-a.editor.wrapper.offset().left-a.editor.wrapper.outerWidth(),c>0&&a.menuWrapper.css({left:"auto",right:0}),a.trigger("menuexpand")),!1):(e=a.el.data("param"),a.command(e),!1)}}(this)),this.wrapper.on("click","a.menu-item",function(b){return function(c){var d,e,f;return c.preventDefault(),d=a(c.currentTarget),b.wrapper.removeClass("menu-on"),e=b.needFocus&&!b.editor.inputManager.focused,d.hasClass("disabled")||e?!1:(b.editor.toolbar.wrapper.removeClass("menu-on"),f=d.data("param"),b.command(f),!1)}}(this)),this.wrapper.on("mousedown","a.menu-item",function(a){return!1}),this.editor.on("blur",function(a){return function(){var b;return b=a.editor.body.is(":visible")&&a.editor.body.is("[contenteditable]"),b&&!a.editor.clipboard.pasting?(a.setActive(!1),a.setDisabled(!1)):void 0}}(this)),null!=this.shortcut&&this.editor.hotkeys.add(this.shortcut,function(a){return function(b){return a.el.mousedown(),!1}}(this)),d=this.htmlTag.split(","),b=0,c=d.length;c>b;b++)e=d[b],e=a.trim(e),e&&a.inArray(e,this.editor.formatter._allowedTags)<0&&this.editor.formatter._allowedTags.push(e);return this.editor.on("selectionchanged",function(a){return function(b){return a.editor.inputManager.focused?a._status():void 0}}(this))},c.prototype.iconClassOf=function(a){return a?"simditor-icon simditor-icon-"+a:""},c.prototype.setIcon=function(a){return this.el.find("span").removeClass().addClass(this.iconClassOf(a)).text(this.text)},c.prototype.render=function(){return this.wrapper=a(this._tpl.item).appendTo(this.editor.toolbar.list),this.el=this.wrapper.find("a.toolbar-item"),this.el.attr("title",this.title).addClass("toolbar-item-"+this.name).data("button",this),this.setIcon(this.icon),this.menu?(this.menuWrapper=a(this._tpl.menuWrapper).appendTo(this.wrapper),this.menuWrapper.addClass("toolbar-menu-"+this.name),this.renderMenu()):void 0},c.prototype.renderMenu=function(){var b,c,d,e,f,g,h,i;if(a.isArray(this.menu)){for(this.menuEl=a("<ul/>").appendTo(this.menuWrapper),g=this.menu,i=[],d=0,e=g.length;e>d;d++)f=g[d],"|"!==f?(c=a(this._tpl.menuItem).appendTo(this.menuEl),b=c.find("a.menu-item").attr({title:null!=(h=f.title)?h:f.text,"data-param":f.param}).addClass("menu-item-"+f.name),i.push(f.icon?b.find("span").addClass(this.iconClassOf(f.icon)):b.find("span").text(f.text))):a(this._tpl.separator).appendTo(this.menuEl);return i}},c.prototype.setActive=function(a){return a!==this.active?(this.active=a,this.el.toggleClass("active",this.active)):void 0},c.prototype.setDisabled=function(a){return a!==this.disabled?(this.disabled=a,this.el.toggleClass("disabled",this.disabled)):void 0},c.prototype._disableStatus=function(){var a,b,c;return c=this.editor.selection.startNodes(),b=this.editor.selection.endNodes(),a=c.filter(this.disableTag).length>0||b.filter(this.disableTag).length>0,this.setDisabled(a),this.disabled&&this.setActive(!1),this.disabled},c.prototype._activeStatus=function(){var a,b,c,d,e;return e=this.editor.selection.startNodes(),c=this.editor.selection.endNodes(),d=e.filter(this.htmlTag),b=c.filter(this.htmlTag),a=d.length>0&&b.length>0&&d.is(b),this.node=a?d:null,this.setActive(a),this.active},c.prototype._status=function(){return this._disableStatus(),this.disabled?void 0:this._activeStatus()},c.prototype.command=function(a){},c.prototype._t=function(){var a,b,d;return a=1<=arguments.length?P.call(arguments,0):[],d=c.__super__._t.apply(this,a),d||(d=(b=this.editor)._t.apply(b,a)),d},c}(b),D.Button=h,B=function(b){function c(a){this.button=a.button,this.editor=a.button.editor,c.__super__.constructor.call(this,a)}return M(c,b),c.prototype.offset={top:4,left:0},c.prototype.target=null,c.prototype.active=!1,c.prototype._init=function(){return this.el=a('<div class="simditor-popover"></div>').appendTo(this.editor.el).data("popover",this),this.render(),this.el.on("mouseenter",function(a){return function(b){return a.el.addClass("hover")}}(this)),this.el.on("mouseleave",function(a){return function(b){return a.el.removeClass("hover")}}(this))},c.prototype.render=function(){},c.prototype._initLabelWidth=function(){var b;return b=this.el.find(".settings-field"),b.length>0?(this._labelWidth=0,b.each(function(b){return function(c,d){var e,f;return e=a(d),f=e.find("label"),f.length>0?b._labelWidth=Math.max(b._labelWidth,f.width()):void 0}}(this)),b.find("label").width(this._labelWidth)):void 0},c.prototype.show=function(b,c){return null==c&&(c="bottom"),null!=b?(this.el.siblings(".simditor-popover").each(function(b,c){return c=a(c).data("popover"),c.active?c.hide():void 0}),this.active&&this.target&&this.target.removeClass("selected"),this.target=b.addClass("selected"),this.active?(this.refresh(c),this.trigger("popovershow")):(this.active=!0,this.el.css({left:-9999}).show(),this._labelWidth||this._initLabelWidth(),this.editor.util.reflow(),this.refresh(c),this.trigger("popovershow"))):void 0},c.prototype.hide=function(){return this.active?(this.target&&this.target.removeClass("selected"),this.target=null,this.active=!1,this.el.hide(),this.trigger("popoverhide")):void 0},c.prototype.refresh=function(a){var b,c,d,e,f,g;return null==a&&(a="bottom"),this.active?(b=this.editor.el.offset(),f=this.target.offset(),e=this.target.outerHeight(),"bottom"===a?g=f.top-b.top+e:"top"===a&&(g=f.top-b.top-this.el.height()),d=this.editor.wrapper.width()-this.el.outerWidth()-10,c=Math.min(f.left-b.left,d),this.el.css({top:g+this.offset.top,left:c+this.offset.left})):void 0},c.prototype.destroy=function(){return this.target=null,this.active=!1,this.editor.off(".linkpopover"),this.el.remove()},c.prototype._t=function(){var a,b,d;return a=1<=arguments.length?P.call(arguments,0):[],d=c.__super__._t.apply(this,a),d||(d=(b=this.button)._t.apply(b,a)),d},c}(b),D.Popover=B,G=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="title",c.prototype.htmlTag="h1, h2, h3, h4, h5",c.prototype.disableTag="pre, table",c.prototype._init=function(){return this.menu=[{name:"normal",text:this._t("normalText"),param:"p"},"|",{name:"h1",text:this._t("title")+" 1",param:"h1"},{name:"h2",text:this._t("title")+" 2",param:"h2"},{name:"h3",text:this._t("title")+" 3",param:"h3"},{name:"h4",text:this._t("title")+" 4",param:"h4"},{name:"h5",text:this._t("title")+" 5",param:"h5"}],c.__super__._init.call(this)},c.prototype.setActive=function(a,b){return c.__super__.setActive.call(this,a),a&&(b||(b=this.node[0].tagName.toLowerCase())),this.el.removeClass("active-p active-h1 active-h2 active-h3 active-h4 active-h5"),a?this.el.addClass("active active-"+b):void 0},c.prototype.command=function(b){var c;return c=this.editor.selection.rootNodes(),this.editor.selection.save(),c.each(function(c){return function(d,e){var f;return f=a(e),f.is("blockquote")||f.is(b)||f.is(c.disableTag)||c.editor.util.isDecoratedNode(f)?void 0:a("<"+b+"/>").append(f.contents()).replaceAll(f)}}(this)),this.editor.selection.restore(),this.editor.trigger("valuechanged")},c}(h),D.Toolbar.addButton(G),m=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="fontScale",c.prototype.icon="font",c.prototype.disableTag="pre",c.prototype.htmlTag="span",c.prototype.sizeMap={"x-large":"1.5em",large:"1.25em",small:".75em","x-small":".5em"},c.prototype._init=function(){return this.menu=[{name:"150%",text:this._t("fontScaleXLarge"),param:"5"},{name:"125%",text:this._t("fontScaleLarge"),param:"4"},{name:"100%",text:this._t("fontScaleNormal"),param:"3"},{name:"75%",text:this._t("fontScaleSmall"),param:"2"},{name:"50%",text:this._t("fontScaleXSmall"),param:"1"}],c.__super__._init.call(this)},c.prototype._activeStatus=function(){var a,b,c,d,e,f;return d=this.editor.selection.range(),f=this.editor.selection.startNodes(),c=this.editor.selection.endNodes(),e=f.filter('span[style*="font-size"]'),b=c.filter('span[style*="font-size"]'),a=f.length>0&&c.length>0&&e.is(b),this.setActive(a),this.active},c.prototype.command=function(b){var c,d,e;return e=this.editor.selection.range(),e.collapsed?void 0:(document.execCommand("styleWithCSS",!1,!0),document.execCommand("fontSize",!1,b),document.execCommand("styleWithCSS",!1,!1),this.editor.selection.reset(),this.editor.selection.range(),d=this.editor.selection.containerNode(),c=d[0].nodeType===Node.TEXT_NODE?d.closest('span[style*="font-size"]'):d.find('span[style*="font-size"]'),c.each(function(b){return function(c,d){var e,f;return e=a(d),f=d.style.fontSize,/large|x-large|small|x-small/.test(f)?e.css("fontSize",b.sizeMap[f]):"medium"===f?e.replaceWith(e.contents()):void 0}}(this)),this.editor.trigger("valuechanged"))},c}(h),D.Toolbar.addButton(m),g=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="bold",c.prototype.icon="bold",c.prototype.htmlTag="b, strong",c.prototype.disableTag="pre",c.prototype.shortcut="cmd+b",c.prototype._init=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + b )":(this.title=this.title+" ( Ctrl + b )",this.shortcut="ctrl+b"),c.__super__._init.call(this)},c.prototype._activeStatus=function(){var a;return a=document.queryCommandState("bold")===!0,this.setActive(a),this.active},c.prototype.command=function(){return document.execCommand("bold"),this.editor.util.support.oninput||this.editor.trigger("valuechanged"),a(document).trigger("selectionchange")},c}(h),D.Toolbar.addButton(g),u=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="italic",c.prototype.icon="italic",c.prototype.htmlTag="i",c.prototype.disableTag="pre",c.prototype.shortcut="cmd+i",c.prototype._init=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + i )":(this.title=this.title+" ( Ctrl + i )",this.shortcut="ctrl+i"),c.__super__._init.call(this)},c.prototype._activeStatus=function(){var a;return a=document.queryCommandState("italic")===!0,this.setActive(a),this.active},c.prototype.command=function(){return document.execCommand("italic"),this.editor.util.support.oninput||this.editor.trigger("valuechanged"),a(document).trigger("selectionchange")},c}(h),D.Toolbar.addButton(u),I=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="underline",c.prototype.icon="underline",c.prototype.htmlTag="u",c.prototype.disableTag="pre",c.prototype.shortcut="cmd+u",c.prototype.render=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + u )":(this.title=this.title+" ( Ctrl + u )",this.shortcut="ctrl+u"),c.__super__.render.call(this)},c.prototype._activeStatus=function(){var a;return a=document.queryCommandState("underline")===!0,this.setActive(a),this.active},c.prototype.command=function(){return document.execCommand("underline"),this.editor.util.support.oninput||this.editor.trigger("valuechanged"),a(document).trigger("selectionchange")},c}(h),D.Toolbar.addButton(I),l=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="color",c.prototype.icon="tint",c.prototype.disableTag="pre",c.prototype.menu=!0,c.prototype.render=function(){var a;return a=1<=arguments.length?P.call(arguments,0):[],c.__super__.render.apply(this,a)},c.prototype.renderMenu=function(){return a('<ul class="color-list">\n  <li><a href="javascript:;" class="font-color font-color-1"></a></li>\n  <li><a href="javascript:;" class="font-color font-color-2"></a></li>\n  <li><a href="javascript:;" class="font-color font-color-3"></a></li>\n  <li><a href="javascript:;" class="font-color font-color-4"></a></li>\n  <li><a href="javascript:;" class="font-color font-color-5"></a></li>\n  <li><a href="javascript:;" class="font-color font-color-6"></a></li>\n  <li><a href="javascript:;" class="font-color font-color-7"></a></li>\n  <li><a href="javascript:;" class="font-color font-color-default"></a></li>\n</ul>').appendTo(this.menuWrapper),this.menuWrapper.on("mousedown",".color-list",function(a){return!1}),this.menuWrapper.on("click",".font-color",function(b){return function(c){var d,e,f,g,h,i;if(b.wrapper.removeClass("menu-on"),d=a(c.currentTarget),d.hasClass("font-color-default")){if(e=b.editor.body.find("p, li"),!(e.length>0))return;h=window.getComputedStyle(e[0],null).getPropertyValue("color"),f=b._convertRgbToHex(h)}else h=window.getComputedStyle(d[0],null).getPropertyValue("background-color"),f=b._convertRgbToHex(h);return f?(g=b.editor.selection.range(),!d.hasClass("font-color-default")&&g.collapsed&&(i=document.createTextNode(b._t("coloredText")),g.insertNode(i),g.selectNodeContents(i),b.editor.selection.range(g)),document.execCommand("styleWithCSS",!1,!0),document.execCommand("foreColor",!1,f),document.execCommand("styleWithCSS",!1,!1),b.editor.util.support.oninput?void 0:b.editor.trigger("valuechanged")):void 0}}(this))},c.prototype._convertRgbToHex=function(a){var b,c,d;return c=/rgb\((\d+),\s?(\d+),\s?(\d+)\)/g,(b=c.exec(a))?(d=function(a,b,c){var d;return d=function(a){var b;return b=a.toString(16),1===b.length?"0"+b:b},"#"+d(a)+d(b)+d(c)})(1*b[1],1*b[2],1*b[3]):""},c}(h),D.Toolbar.addButton(l),y=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.type="",c.prototype.disableTag="pre, table",c.prototype.command=function(b){var c,d,e;return d=this.editor.selection.blockNodes(),e="ul"===this.type?"ol":"ul",this.editor.selection.save(),c=null,d.each(function(b){return function(d,f){var g;return g=a(f),g.is("blockquote, li")||g.is(b.disableTag)||b.editor.util.isDecoratedNode(g)||!a.contains(document,f)?void 0:g.is(b.type)?(g.children("li").each(function(c,d){
var e,f;return f=a(d),e=f.children("ul, ol").insertAfter(g),a("<p/>").append(a(d).html()||b.editor.util.phBr).insertBefore(g)}),g.remove()):g.is(e)?a("<"+b.type+"/>").append(g.contents()).replaceAll(g):c&&g.prev().is(c)?(a("<li/>").append(g.html()||b.editor.util.phBr).appendTo(c),g.remove()):(c=a("<"+b.type+"><li></li></"+b.type+">"),c.find("li").append(g.html()||b.editor.util.phBr),c.replaceAll(g))}}(this)),this.editor.selection.restore(),this.editor.trigger("valuechanged")},c}(h),z=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return M(b,a),b.prototype.type="ol",b.prototype.name="ol",b.prototype.icon="list-ol",b.prototype.htmlTag="ol",b.prototype.shortcut="cmd+/",b.prototype._init=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + / )":(this.title=this.title+" ( ctrl + / )",this.shortcut="ctrl+/"),b.__super__._init.call(this)},b}(y),K=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return M(b,a),b.prototype.type="ul",b.prototype.name="ul",b.prototype.icon="list-ul",b.prototype.htmlTag="ul",b.prototype.shortcut="cmd+.",b.prototype._init=function(){return this.editor.util.os.mac?this.title=this.title+" ( Cmd + . )":(this.title=this.title+" ( Ctrl + . )",this.shortcut="ctrl+."),b.__super__._init.call(this)},b}(y),D.Toolbar.addButton(z),D.Toolbar.addButton(K),f=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="blockquote",c.prototype.icon="quote-left",c.prototype.htmlTag="blockquote",c.prototype.disableTag="pre, table",c.prototype.command=function(){var b,c,d;return b=this.editor.selection.rootNodes(),b=b.filter(function(b,c){return!a(c).parent().is("blockquote")}),this.editor.selection.save(),d=[],c=function(b){return function(){return d.length>0?(a("<"+b.htmlTag+"/>").insertBefore(d[0]).append(d),d.length=0):void 0}}(this),b.each(function(b){return function(e,f){var g;return g=a(f),g.parent().is(b.editor.body)?g.is(b.htmlTag)?(c(),g.children().unwrap()):g.is(b.disableTag)||b.editor.util.isDecoratedNode(g)?c():d.push(f):void 0}}(this)),c(),this.editor.selection.restore(),this.editor.trigger("valuechanged")},c}(h),D.Toolbar.addButton(f),j=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="code",c.prototype.icon="code",c.prototype.htmlTag="pre",c.prototype.disableTag="ul, ol, table",c.prototype._init=function(){return c.__super__._init.call(this),this.editor.on("decorate",function(b){return function(c,d){return d.find("pre").each(function(c,d){return b.decorate(a(d))})}}(this)),this.editor.on("undecorate",function(b){return function(c,d){return d.find("pre").each(function(c,d){return b.undecorate(a(d))})}}(this))},c.prototype.render=function(){var a;return a=1<=arguments.length?P.call(arguments,0):[],c.__super__.render.apply(this,a),this.popover=new k({button:this})},c.prototype._checkMode=function(){var b,c;return c=this.editor.selection.range(),(b=a(c.cloneContents()).find(this.editor.util.blockNodes.join(",")))>0||c.collapsed&&0===this.editor.selection.startNodes().filter("code").length?(this.inlineMode=!1,this.htmlTag="pre"):(this.inlineMode=!0,this.htmlTag="code")},c.prototype._status=function(){return this._checkMode(),c.__super__._status.call(this),this.inlineMode?void 0:this.active?this.popover.show(this.node):this.popover.hide()},c.prototype.decorate=function(a){var b,c,d,e;return b=a.find("> code"),b.length>0&&(c=null!=(d=b.attr("class"))&&null!=(e=d.match(/lang-(\S+)/))?e[1]:void 0,b.contents().unwrap(),c)?a.attr("data-lang",c):void 0},c.prototype.undecorate=function(b){var c,d;return d=b.attr("data-lang"),c=a("<code/>"),d&&-1!==d&&c.addClass("lang-"+d),b.wrapInner(c).removeAttr("data-lang")},c.prototype.command=function(){return this.inlineMode?this._inlineCommand():this._blockCommand()},c.prototype._blockCommand=function(){var b,c,d,e;return b=this.editor.selection.rootNodes(),d=[],e=[],c=function(b){return function(){var c;if(d.length>0)return c=a("<"+b.htmlTag+"/>").insertBefore(d[0]).text(b.editor.formatter.clearHtml(d)),e.push(c[0]),d.length=0}}(this),b.each(function(b){return function(f,g){var h,i;return h=a(g),h.is(b.htmlTag)?(c(),i=a("<p/>").append(h.html().replace("\n","<br/>")).replaceAll(h),e.push(i[0])):h.is(b.disableTag)||b.editor.util.isDecoratedNode(h)||h.is("blockquote")?c():d.push(g)}}(this)),c(),this.editor.selection.setRangeAtEndOf(a(e).last()),this.editor.trigger("valuechanged")},c.prototype._inlineCommand=function(){var b,c,d;return d=this.editor.selection.range(),this.active?(d.selectNodeContents(this.node[0]),this.editor.selection.save(d),this.node.contents().unwrap(),this.editor.selection.restore()):(c=a(d.extractContents()),b=a("<"+this.htmlTag+"/>").append(c.contents()),d.insertNode(b[0]),d.selectNodeContents(b[0]),this.editor.selection.range(d)),this.editor.trigger("valuechanged")},c}(h),k=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.render=function(){var b,c,d,e,f;for(this._tpl='<div class="code-settings">\n  <div class="settings-field">\n    <select class="select-lang">\n      <option value="-1">'+this._t("selectLanguage")+"</option>\n    </select>\n  </div>\n</div>",this.langs=this.editor.opts.codeLanguages||[{name:"Bash",value:"bash"},{name:"C++",value:"c++"},{name:"C#",value:"cs"},{name:"CSS",value:"css"},{name:"Erlang",value:"erlang"},{name:"Less",value:"less"},{name:"Sass",value:"sass"},{name:"Diff",value:"diff"},{name:"CoffeeScript",value:"coffeescript"},{name:"HTML,XML",value:"html"},{name:"JSON",value:"json"},{name:"Java",value:"java"},{name:"JavaScript",value:"js"},{name:"Markdown",value:"markdown"},{name:"Objective C",value:"oc"},{name:"PHP",value:"php"},{name:"Perl",value:"parl"},{name:"Python",value:"python"},{name:"Ruby",value:"ruby"},{name:"SQL",value:"sql"}],this.el.addClass("code-popover").append(this._tpl),this.selectEl=this.el.find(".select-lang"),f=this.langs,c=0,e=f.length;e>c;c++)d=f[c],b=a("<option/>",{text:d.name,value:d.value}).appendTo(this.selectEl);return this.selectEl.on("change",function(a){return function(b){var c;return a.lang=a.selectEl.val(),c=a.target.hasClass("selected"),a.target.removeClass().removeAttr("data-lang"),-1!==a.lang&&a.target.attr("data-lang",a.lang),c&&a.target.addClass("selected"),a.editor.trigger("valuechanged")}}(this)),this.editor.on("valuechanged",function(a){return function(b){return a.active?a.refresh():void 0}}(this))},c.prototype.show=function(){var a;return a=1<=arguments.length?P.call(arguments,0):[],c.__super__.show.apply(this,a),this.lang=this.target.attr("data-lang"),this.selectEl.val(null!=this.lang?this.lang:-1)},c}(B),D.Toolbar.addButton(j),w=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="link",c.prototype.icon="link",c.prototype.htmlTag="a",c.prototype.disableTag="pre",c.prototype.render=function(){var a;return a=1<=arguments.length?P.call(arguments,0):[],c.__super__.render.apply(this,a),this.popover=new x({button:this})},c.prototype._status=function(){return c.__super__._status.call(this),this.active&&!this.editor.selection.rangeAtEndOf(this.node)?this.popover.show(this.node):this.popover.hide()},c.prototype.command=function(){var b,c,d,e,f,g;return f=this.editor.selection.range(),this.active?(g=document.createTextNode(this.node.text()),this.node.replaceWith(g),f.selectNode(g)):(b=a(f.extractContents()),e=this.editor.formatter.clearHtml(b.contents(),!1),c=a("<a/>",{href:"http://www.example.com",target:"_blank",text:e||this._t("linkText")}),this.editor.selection.blockNodes().length>0?f.insertNode(c[0]):(d=a("<p/>").append(c),f.insertNode(d[0])),f.selectNodeContents(c[0]),this.popover.one("popovershow",function(a){return function(){return e?(a.popover.urlEl.focus(),a.popover.urlEl[0].select()):(a.popover.textEl.focus(),a.popover.textEl[0].select())}}(this))),this.editor.selection.range(f),this.editor.trigger("valuechanged")},c}(h),x=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.render=function(){var b;return b='<div class="link-settings">\n  <div class="settings-field">\n    <label>'+this._t("linkText")+'</label>\n    <input class="link-text" type="text"/>\n    <a class="btn-unlink" href="javascript:;" title="'+this._t("removeLink")+'"\n      tabindex="-1">\n      <span class="simditor-icon simditor-icon-unlink"></span>\n    </a>\n  </div>\n  <div class="settings-field">\n    <label>'+this._t("linkUrl")+'</label>\n    <input class="link-url" type="text"/>\n  </div>\n  <div class="settings-field">\n    <label>'+this._t("linkTarget")+'</label>\n    <select class="link-target">\n      <option value="_blank">'+this._t("openLinkInNewWindow")+' (_blank)</option>\n      <option value="_self">'+this._t("openLinkInCurrentWindow")+" (_self)</option>\n    </select>\n  </div>\n</div>",this.el.addClass("link-popover").append(b),this.textEl=this.el.find(".link-text"),this.urlEl=this.el.find(".link-url"),this.unlinkEl=this.el.find(".btn-unlink"),this.selectTarget=this.el.find(".link-target"),this.textEl.on("keyup",function(a){return function(b){return 13!==b.which?(a.target.text(a.textEl.val()),a.editor.inputManager.throttledValueChanged()):void 0}}(this)),this.urlEl.on("keyup",function(a){return function(b){var c;if(13!==b.which)return c=a.urlEl.val(),!/https?:\/\/|^\//gi.test(c)&&c&&(c="http://"+c),a.target.attr("href",c),a.editor.inputManager.throttledValueChanged()}}(this)),a([this.urlEl[0],this.textEl[0]]).on("keydown",function(b){return function(c){var d;return 13===c.which||27===c.which||!c.shiftKey&&9===c.which&&a(c.target).hasClass("link-url")?(c.preventDefault(),d=document.createRange(),b.editor.selection.setRangeAfter(b.target,d),b.hide(),b.editor.inputManager.throttledValueChanged()):void 0}}(this)),this.unlinkEl.on("click",function(a){return function(b){var c,d;return d=document.createTextNode(a.target.text()),a.target.replaceWith(d),a.hide(),c=document.createRange(),a.editor.selection.setRangeAfter(d,c),a.editor.inputManager.throttledValueChanged()}}(this)),this.selectTarget.on("change",function(a){return function(b){return a.target.attr("target",a.selectTarget.val()),a.editor.inputManager.throttledValueChanged()}}(this))},c.prototype.show=function(){var a;return a=1<=arguments.length?P.call(arguments,0):[],c.__super__.show.apply(this,a),this.textEl.val(this.target.text()),this.urlEl.val(this.target.attr("href"))},c}(B),D.Toolbar.addButton(w),p=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="image",c.prototype.icon="picture-o",c.prototype.htmlTag="img",c.prototype.disableTag="pre, table",c.prototype.defaultImage="",c.prototype.needFocus=!1,c.prototype._init=function(){var b,d,e,f;if(this.editor.opts.imageButton)if(Array.isArray(this.editor.opts.imageButton))for(this.menu=[],f=this.editor.opts.imageButton,d=0,e=f.length;e>d;d++)b=f[d],this.menu.push({name:b+"-image",text:this._t(b+"Image")});else this.menu=!1;else null!=this.editor.uploader?this.menu=[{name:"upload-image",text:this._t("uploadImage")},{name:"external-image",text:this._t("externalImage")}]:this.menu=!1;return this.defaultImage=this.editor.opts.defaultImage,this.editor.body.on("click","img:not([data-non-image])",function(b){return function(c){var d,e;return d=a(c.currentTarget),e=document.createRange(),e.selectNode(d[0]),b.editor.selection.range(e),b.editor.util.support.onselectionchange||b.editor.trigger("selectionchanged"),!1}}(this)),this.editor.body.on("mouseup","img:not([data-non-image])",function(a){return!1}),this.editor.on("selectionchanged.image",function(b){return function(){var c,d,e;return e=b.editor.selection.range(),null!=e?(c=a(e.cloneContents()).contents(),1===c.length&&c.is("img:not([data-non-image])")?(d=a(e.startContainer).contents().eq(e.startOffset),b.popover.show(d)):b.popover.hide()):void 0}}(this)),this.editor.on("valuechanged.image",function(b){return function(){var c;return c=b.editor.wrapper.find(".simditor-image-loading"),c.length>0?c.each(function(c,d){var e,f,g;return f=a(d),e=f.data("img"),!(e&&e.parent().length>0)&&(f.remove(),e&&(g=e.data("file"),g&&(b.editor.uploader.cancel(g),b.editor.body.find("img.uploading").length<1)))?b.editor.uploader.trigger("uploadready",[g]):void 0}):void 0}}(this)),c.__super__._init.call(this)},c.prototype.render=function(){var a;return a=1<=arguments.length?P.call(arguments,0):[],c.__super__.render.apply(this,a),this.popover=new q({button:this}),"upload"===this.editor.opts.imageButton?this._initUploader(this.el):void 0},c.prototype.renderMenu=function(){return c.__super__.renderMenu.call(this),this._initUploader()},c.prototype._initUploader=function(b){var c,d,e;return null==b&&(b=this.menuEl.find(".menu-item-upload-image")),null==this.editor.uploader?void this.el.find(".btn-upload").remove():(c=null,d=function(d){return function(){return c&&c.remove(),c=a("<input/>",{type:"file",title:d._t("uploadImage"),multiple:!0,accept:"image/*"}).appendTo(b)}}(this),d(),b.on("click mousedown","input[type=file]",function(a){return a.stopPropagation()}),b.on("change","input[type=file]",function(a){return function(b){return a.editor.inputManager.focused?(a.editor.uploader.upload(c,{inline:!0}),d()):(a.editor.one("focus",function(b){return a.editor.uploader.upload(c,{inline:!0}),d()}),a.editor.focus()),a.wrapper.removeClass("menu-on")}}(this)),this.editor.uploader.on("beforeupload",function(b){return function(c,d){var e;if(d.inline)return d.img?e=a(d.img):(e=b.createImage(d.name),d.img=e),e.addClass("uploading"),e.data("file",d),b.editor.uploader.readImageFile(d.obj,function(a){var c;if(e.hasClass("uploading"))return c=a?a.src:b.defaultImage,b.loadImage(e,c,function(){return b.popover.active?(b.popover.refresh(),b.popover.srcEl.val(b._t("uploading")).prop("disabled",!0)):void 0})})}}(this)),e=a.proxy(this.editor.util.throttle(function(a,b,c,d){var e,f,g;if(b.inline&&(f=b.img.data("mask")))return e=f.data("img"),e.hasClass("uploading")&&e.parent().length>0?(g=c/d,g=(100*g).toFixed(0),g>99&&(g=99),f.find(".progress").height(100-g+"%")):void f.remove()},500),this),this.editor.uploader.on("uploadprogress",e),this.editor.uploader.on("uploadsuccess",function(b){return function(c,d,e){var f,g,h;if(d.inline&&(f=d.img,f.hasClass("uploading")&&f.parent().length>0)){if("object"!=typeof e)try{e=a.parseJSON(e)}catch(i){c=i,e={success:!1}}return e.success===!1?(h=e.msg||b._t("uploadFailed"),alert(h),g=b.defaultImage):g=e.file_path,b.loadImage(f,g,function(){var a;return f.removeData("file"),f.removeClass("uploading").removeClass("loading"),a=f.data("mask"),a&&a.remove(),f.removeData("mask"),b.editor.trigger("valuechanged"),b.editor.body.find("img.uploading").length<1?b.editor.uploader.trigger("uploadready",[d,e]):void 0}),b.popover.active?(b.popover.srcEl.prop("disabled",!1),b.popover.srcEl.val(e.file_path)):void 0}}}(this)),this.editor.uploader.on("uploaderror",function(b){return function(c,d,e){var f,g,h;if(d.inline&&"abort"!==e.statusText){if(e.responseText){try{h=a.parseJSON(e.responseText),g=h.msg}catch(i){c=i,g=b._t("uploadError")}alert(g)}if(f=d.img,f.hasClass("uploading")&&f.parent().length>0)return b.loadImage(f,b.defaultImage,function(){var a;return f.removeData("file"),f.removeClass("uploading").removeClass("loading"),a=f.data("mask"),a&&a.remove(),f.removeData("mask")}),b.popover.active&&(b.popover.srcEl.prop("disabled",!1),b.popover.srcEl.val(b.defaultImage)),b.editor.trigger("valuechanged"),b.editor.body.find("img.uploading").length<1?b.editor.uploader.trigger("uploadready",[d,h]):void 0}}}(this)))},c.prototype._status=function(){return this._disableStatus()},c.prototype.loadImage=function(b,c,d){var e,f,g;return g=function(a){return function(){var c,d;return c=b.offset(),d=a.editor.wrapper.offset(),e.css({top:c.top-d.top,left:c.left-d.left,width:b.width(),height:b.height()}).show()}}(this),b.addClass("loading"),e=b.data("mask"),e||(e=a('<div class="simditor-image-loading">\n  <div class="progress"></div>\n</div>').hide().appendTo(this.editor.wrapper),g(),b.data("mask",e),e.data("img",b)),f=new Image,f.onload=function(h){return function(){var i,j;if(b.hasClass("loading")||b.hasClass("uploading"))return j=f.width,i=f.height,b.attr({src:c,width:j,height:i,"data-image-size":j+","+i}).removeClass("loading"),b.hasClass("uploading")?(h.editor.util.reflow(h.editor.body),g()):(e.remove(),b.removeData("mask")),a.isFunction(d)?d(f):void 0}}(this),f.onerror=function(){return a.isFunction(d)&&d(!1),e.remove(),b.removeData("mask").removeClass("loading")},f.src=c},c.prototype.createImage=function(b){var c,d;return null==b&&(b="Image"),this.editor.inputManager.focused||this.editor.focus(),d=this.editor.selection.range(),d.deleteContents(),this.editor.selection.range(d),c=a("<img/>").attr("alt",b),d.insertNode(c[0]),this.editor.selection.setRangeAfter(c,d),this.editor.trigger("valuechanged"),c},c.prototype.command=function(a){var b;return b=this.createImage(),this.loadImage(b,a||this.defaultImage,function(a){return function(){return a.editor.trigger("valuechanged"),a.editor.util.reflow(b),b.click(),a.popover.one("popovershow",function(){return a.popover.srcEl.focus(),a.popover.srcEl[0].select()})}}(this))},c}(h),q=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.offset={top:6,left:-4},c.prototype.render=function(){var b;return b='<div class="link-settings">\n  <div class="settings-field">\n    <label>'+this._t("imageUrl")+'</label>\n    <input class="image-src" type="text" tabindex="1" />\n    <a class="btn-upload" href="javascript:;"\n      title="'+this._t("uploadImage")+'" tabindex="-1">\n      <span class="simditor-icon simditor-icon-upload"></span>\n    </a>\n  </div>\n  <div class=\'settings-field\'>\n    <label>'+this._t("imageAlt")+'</label>\n    <input class="image-alt" id="image-alt" type="text" tabindex="1" />\n  </div>\n  <div class="settings-field">\n    <label>'+this._t("imageSize")+'</label>\n    <input class="image-size" id="image-width" type="text" tabindex="2" />\n    <span class="times">×</span>\n    <input class="image-size" id="image-height" type="text" tabindex="3" />\n    <a class="btn-restore" href="javascript:;"\n      title="'+this._t("restoreImageSize")+'" tabindex="-1">\n      <span class="simditor-icon simditor-icon-undo"></span>\n    </a>\n  </div>\n</div>',this.el.addClass("image-popover").append(b),this.srcEl=this.el.find(".image-src"),this.widthEl=this.el.find("#image-width"),this.heightEl=this.el.find("#image-height"),this.altEl=this.el.find("#image-alt"),this.srcEl.on("keydown",function(a){return function(b){var c;if(13===b.which&&!a.target.hasClass("uploading"))return b.preventDefault(),c=document.createRange(),a.button.editor.selection.setRangeAfter(a.target,c),a.hide()}}(this)),this.srcEl.on("blur",function(a){return function(b){return a._loadImage(a.srcEl.val())}}(this)),this.el.find(".image-size").on("blur",function(b){return function(c){return b._resizeImg(a(c.currentTarget)),b.el.data("popover").refresh()}}(this)),this.el.find(".image-size").on("keyup",function(b){return function(c){var d;return d=a(c.currentTarget),13!==c.which&&27!==c.which&&9!==c.which?b._resizeImg(d,!0):void 0}}(this)),this.el.find(".image-size").on("keydown",function(b){return function(c){var d,e,f;return e=a(c.currentTarget),13===c.which||27===c.which?(c.preventDefault(),13===c.which?b._resizeImg(e):b._restoreImg(),d=b.target,b.hide(),f=document.createRange(),b.button.editor.selection.setRangeAfter(d,f)):9===c.which?b.el.data("popover").refresh():void 0}}(this)),this.altEl.on("keydown",function(a){return function(b){var c;return 13===b.which?(b.preventDefault(),c=document.createRange(),a.button.editor.selection.setRangeAfter(a.target,c),a.hide()):void 0}}(this)),this.altEl.on("keyup",function(a){return function(b){return 13!==b.which&&27!==b.which&&9!==b.which?(a.alt=a.altEl.val(),a.target.attr("alt",a.alt)):void 0}}(this)),this.el.find(".btn-restore").on("click",function(a){return function(b){return a._restoreImg(),a.el.data("popover").refresh()}}(this)),this.editor.on("valuechanged",function(a){return function(b){return a.active?a.refresh():void 0}}(this)),this._initUploader()},c.prototype._initUploader=function(){var b,c;return b=this.el.find(".btn-upload"),null==this.editor.uploader?void b.remove():(c=function(c){return function(){return c.input&&c.input.remove(),c.input=a("<input/>",{type:"file",title:c._t("uploadImage"),multiple:!0,accept:"image/*"}).appendTo(b)}}(this),c(),this.el.on("click mousedown","input[type=file]",function(a){return a.stopPropagation()}),this.el.on("change","input[type=file]",function(a){return function(b){return a.editor.uploader.upload(a.input,{inline:!0,img:a.target}),c()}}(this)))},c.prototype._resizeImg=function(b,c){var d,e,f;return null==c&&(c=!1),e=1*b.val(),this.target&&(a.isNumeric(e)||0>e)?(b.is(this.widthEl)?(f=e,d=this.height*e/this.width,this.heightEl.val(d)):(d=e,f=this.width*e/this.height,this.widthEl.val(f)),c?void 0:(this.target.attr({width:f,height:d}),this.editor.trigger("valuechanged"))):void 0},c.prototype._restoreImg=function(){var a,b;return b=(null!=(a=this.target.data("image-size"))?a.split(","):void 0)||[this.width,this.height],this.target.attr({width:1*b[0],height:1*b[1]}),this.widthEl.val(b[0]),this.heightEl.val(b[1]),this.editor.trigger("valuechanged")},c.prototype._loadImage=function(a,b){if(/^data:image/.test(a)&&!this.editor.uploader)return void(b&&b(!1));if(this.target.attr("src")!==a)return this.button.loadImage(this.target,a,function(c){return function(d){var e;if(d)return c.active&&(c.width=d.width,c.height=d.height,c.widthEl.val(c.width),c.heightEl.val(c.height)),/^data:image/.test(a)?(e=c.editor.util.dataURLtoBlob(a),e.name="Base64 Image.png",c.editor.uploader.upload(e,{inline:!0,img:c.target})):c.editor.trigger("valuechanged"),b?b(d):void 0}}(this))},c.prototype.show=function(){var a,b;return b=1<=arguments.length?P.call(arguments,0):[],c.__super__.show.apply(this,b),a=this.target,this.width=a.width(),this.height=a.height(),this.alt=a.attr("alt"),a.hasClass("uploading")?this.srcEl.val(this._t("uploading")).prop("disabled",!0):(this.srcEl.val(a.attr("src")).prop("disabled",!1),this.widthEl.val(this.width),this.heightEl.val(this.height),this.altEl.val(this.alt))},c}(B),D.Toolbar.addButton(p),r=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return M(b,a),b.prototype.name="indent",b.prototype.icon="indent",b.prototype._init=function(){return this.title=this._t(this.name)+" (Tab)",b.__super__._init.call(this)},b.prototype._status=function(){},b.prototype.command=function(){return this.editor.indentation.indent()},b}(h),D.Toolbar.addButton(r),A=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return M(b,a),b.prototype.name="outdent",b.prototype.icon="outdent",b.prototype._init=function(){return this.title=this._t(this.name)+" (Shift + Tab)",b.__super__._init.call(this)},b.prototype._status=function(){},b.prototype.command=function(){return this.editor.indentation.indent(!0)},b}(h),D.Toolbar.addButton(A),o=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="hr",c.prototype.icon="minus",c.prototype.htmlTag="hr",c.prototype._status=function(){},c.prototype.command=function(){var b,c,d,e;return e=this.editor.selection.rootNodes().first(),d=e.next(),d.length>0?this.editor.selection.save():c=a("<p/>").append(this.editor.util.phBr),b=a("<hr/>").insertAfter(e),c?(c.insertAfter(b),this.editor.selection.setRangeAtStartOf(c)):this.editor.selection.restore(),this.editor.trigger("valuechanged")},c}(h),D.Toolbar.addButton(o),F=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="table",c.prototype.icon="table",c.prototype.htmlTag="table",c.prototype.disableTag="pre, li, blockquote",c.prototype.menu=!0,c.prototype._init=function(){return c.__super__._init.call(this),a.merge(this.editor.formatter._allowedTags,["thead","th","tbody","tr","td","colgroup","col"]),a.extend(this.editor.formatter._allowedAttributes,{td:["rowspan","colspan"],col:["width"]}),a.extend(this.editor.formatter._allowedStyles,{td:["text-align"],th:["text-align"]}),this._initShortcuts(),this.editor.on("decorate",function(b){return function(c,d){return d.find("table").each(function(c,d){return b.decorate(a(d))})}}(this)),this.editor.on("undecorate",function(b){return function(c,d){return d.find("table").each(function(c,d){return b.undecorate(a(d))})}}(this)),this.editor.on("selectionchanged.table",function(a){return function(b){var c,d;return a.editor.body.find(".simditor-table td, .simditor-table th").removeClass("active"),(d=a.editor.selection.range())?(c=a.editor.selection.containerNode(),d.collapsed&&c.is(".simditor-table")&&(c=c.find(a.editor.selection.rangeAtStartOf(c)?"th:first":"td:last"),a.editor.selection.setRangeAtEndOf(c)),c.closest("td, th",a.editor.body).addClass("active")):void 0}}(this)),this.editor.on("blur.table",function(a){return function(b){return a.editor.body.find(".simditor-table td, .simditor-table th").removeClass("active")}}(this)),this.editor.keystroke.add("up","td",function(a){return function(b,c){return a._tdNav(c,"up"),!0}}(this)),this.editor.keystroke.add("up","th",function(a){return function(b,c){return a._tdNav(c,"up"),!0}}(this)),this.editor.keystroke.add("down","td",function(a){return function(b,c){return a._tdNav(c,"down"),!0}}(this)),this.editor.keystroke.add("down","th",function(a){return function(b,c){return a._tdNav(c,"down"),!0}}(this))},c.prototype._tdNav=function(a,b){var c,d,e,f,g,h,i;return null==b&&(b="up"),e="up"===b?"prev":"next",i="up"===b?["tbody","thead"]:["thead","tbody"],h=i[0],f=i[1],d=a.parent("tr"),c=this["_"+e+"Row"](d),c.length>0?(g=d.find("td, th").index(a),this.editor.selection.setRangeAtEndOf(c.find("td, th").eq(g))):!0},c.prototype._nextRow=function(a){var b;return b=a.next("tr"),b.length<1&&a.parent("thead").length>0&&(b=a.parent("thead").next("tbody").find("tr:first")),b},c.prototype._prevRow=function(a){var b;return b=a.prev("tr"),b.length<1&&a.parent("tbody").length>0&&(b=a.parent("tbody").prev("thead").find("tr")),b},c.prototype.initResize=function(b){var c,d,e;return e=b.parent(".simditor-table"),c=b.find("colgroup"),c.length<1&&(c=a("<colgroup/>").prependTo(b),b.find("thead tr th").each(function(b,d){var e;return e=a("<col/>").appendTo(c)}),this.refreshTableWidth(b)),d=a("<div />",{"class":"simditor-resize-handle",contenteditable:"false"}).appendTo(e),e.on("mousemove","td, th",function(b){var f,g,h,i,j,k;if(!e.hasClass("resizing"))return g=a(b.currentTarget),k=b.pageX-a(b.currentTarget).offset().left,5>k&&g.prev().length>0&&(g=g.prev()),g.next("td, th").length<1?void d.hide():(null!=(i=d.data("td"))?i.is(g):void 0)?void d.show():(h=g.parent().find("td, th").index(g),f=c.find("col").eq(h),(null!=(j=d.data("col"))?j.is(f):void 0)?void d.show():d.css("left",g.position().left+g.outerWidth()-5).data("td",g).data("col",f).show())}),e.on("mouseleave",function(a){return d.hide()}),e.on("mousedown",".simditor-resize-handle",function(b){var c,d,f,g,h,i,j,k,l,m,n;return c=a(b.currentTarget),f=c.data("td"),d=c.data("col"),h=f.next("td, th"),g=d.next("col"),m=b.pageX,k=1*f.outerWidth(),l=1*h.outerWidth(),j=parseFloat(c.css("left")),n=f.closest("table").width(),i=50,a(document).on("mousemove.simditor-resize-table",function(a){var b,e,f;return b=a.pageX-m,e=k+b,f=l-b,i>e?(e=i,b=i-k,f=l-b):i>f&&(f=i,b=l-i,e=k+b),d.attr("width",e/n*100+"%"),g.attr("width",f/n*100+"%"),c.css("left",j+b)}),a(document).one("mouseup.simditor-resize-table",function(b){return a(document).off(".simditor-resize-table"),e.removeClass("resizing")}),e.addClass("resizing"),!1})},c.prototype._initShortcuts=function(){return this.editor.hotkeys.add("ctrl+alt+up",function(a){return function(b){return a.editMenu.find(".menu-item[data-param=insertRowAbove]").click(),!1}}(this)),this.editor.hotkeys.add("ctrl+alt+down",function(a){return function(b){return a.editMenu.find(".menu-item[data-param=insertRowBelow]").click(),!1}}(this)),this.editor.hotkeys.add("ctrl+alt+left",function(a){return function(b){return a.editMenu.find(".menu-item[data-param=insertColLeft]").click(),!1}}(this)),this.editor.hotkeys.add("ctrl+alt+right",function(a){return function(b){return a.editMenu.find(".menu-item[data-param=insertColRight]").click(),!1}}(this))},c.prototype.decorate=function(b){var c,d,e;return b.parent(".simditor-table").length>0&&this.undecorate(b),b.wrap('<div class="simditor-table"></div>'),b.find("thead").length<1&&(e=a("<thead />"),c=b.find("tr").first(),e.append(c),this._changeCellTag(c,"th"),d=b.find("tbody"),d.length>0?d.before(e):b.prepend(e)),this.initResize(b),b.parent()},c.prototype.undecorate=function(a){return a.parent(".simditor-table").length>0?a.parent().replaceWith(a):void 0},c.prototype.renderMenu=function(){var b;return a('<div class="menu-create-table">\n</div>\n<div class="menu-edit-table">\n  <ul>\n    <li>\n      <a tabindex="-1" unselectable="on" class="menu-item"\n        href="javascript:;" data-param="deleteRow">\n        <span>'+this._t("deleteRow")+'</span>\n      </a>\n    </li>\n    <li>\n      <a tabindex="-1" unselectable="on" class="menu-item"\n        href="javascript:;" data-param="insertRowAbove">\n        <span>'+this._t("insertRowAbove")+' ( Ctrl + Alt + ↑ )</span>\n      </a>\n    </li>\n    <li>\n      <a tabindex="-1" unselectable="on" class="menu-item"\n        href="javascript:;" data-param="insertRowBelow">\n        <span>'+this._t("insertRowBelow")+' ( Ctrl + Alt + ↓ )</span>\n      </a>\n    </li>\n    <li><span class="separator"></span></li>\n    <li>\n      <a tabindex="-1" unselectable="on" class="menu-item"\n        href="javascript:;" data-param="deleteCol">\n        <span>'+this._t("deleteColumn")+'</span>\n      </a>\n    </li>\n    <li>\n      <a tabindex="-1" unselectable="on" class="menu-item"\n        href="javascript:;" data-param="insertColLeft">\n        <span>'+this._t("insertColumnLeft")+' ( Ctrl + Alt + ← )</span>\n      </a>\n    </li>\n    <li>\n      <a tabindex="-1" unselectable="on" class="menu-item"\n        href="javascript:;" data-param="insertColRight">\n        <span>'+this._t("insertColumnRight")+' ( Ctrl + Alt + → )</span>\n      </a>\n    </li>\n    <li><span class="separator"></span></li>\n    <li>\n      <a tabindex="-1" unselectable="on" class="menu-item"\n        href="javascript:;" data-param="deleteTable">\n        <span>'+this._t("deleteTable")+"</span>\n      </a>\n    </li>\n  </ul>\n</div>").appendTo(this.menuWrapper),this.createMenu=this.menuWrapper.find(".menu-create-table"),this.editMenu=this.menuWrapper.find(".menu-edit-table"),b=this.createTable(6,6).appendTo(this.createMenu),this.createMenu.on("mouseenter","td, th",function(c){return function(d){var e,f,g,h;return c.createMenu.find("td, th").removeClass("selected"),e=a(d.currentTarget),f=e.parent(),h=f.find("td, th").index(e)+1,g=f.prevAll("tr").addBack(),f.parent().is("tbody")&&(g=g.add(b.find("thead tr"))),g.find("td:lt("+h+"), th:lt("+h+")").addClass("selected")}}(this)),this.createMenu.on("mouseleave",function(b){return a(b.currentTarget).find("td, th").removeClass("selected")}),this.createMenu.on("mousedown","td, th",function(c){return function(d){var e,f,g,h,i;return c.wrapper.removeClass("menu-on"),c.editor.inputManager.focused?(f=a(d.currentTarget),g=f.parent(),h=g.find("td").index(f)+1,i=g.prevAll("tr").length+1,g.parent().is("tbody")&&(i+=1),b=c.createTable(i,h,!0),e=c.editor.selection.blockNodes().last(),c.editor.util.isEmptyNode(e)?e.replaceWith(b):e.after(b),c.decorate(b),c.editor.selection.setRangeAtStartOf(b.find("th:first")),c.editor.trigger("valuechanged"),!1):void 0}}(this))},c.prototype.createTable=function(b,c,d){var e,f,g,h,i,j,k,l,m,n,o;for(e=a("<table/>"),h=a("<thead/>").appendTo(e),f=a("<tbody/>").appendTo(e),m=k=0,n=b;n>=0?n>k:k>n;m=n>=0?++k:--k)for(i=a("<tr/>"),
i.appendTo(0===m?h:f),j=l=0,o=c;o>=0?o>l:l>o;j=o>=0?++l:--l)g=a(0===m?"<th/>":"<td/>").appendTo(i),d&&g.append(this.editor.util.phBr);return e},c.prototype.refreshTableWidth=function(b){var c,d;return d=b.width(),c=b.find("col"),b.find("thead tr th").each(function(b,e){var f;return f=c.eq(b),f.attr("width",a(e).outerWidth()/d*100+"%")})},c.prototype.setActive=function(a){return c.__super__.setActive.call(this,a),a?(this.createMenu.hide(),this.editMenu.show()):(this.createMenu.show(),this.editMenu.hide())},c.prototype._changeCellTag=function(b,c){return b.find("td, th").each(function(b,d){var e;return e=a(d),e.replaceWith("<"+c+">"+e.html()+"</"+c+">")})},c.prototype.deleteRow=function(a){var b,c,d;return c=a.parent("tr"),c.closest("table").find("tr").length<1?this.deleteTable(a):(b=this._nextRow(c),b.length>0||(b=this._prevRow(c)),d=c.find("td, th").index(a),c.parent().is("thead")&&(b.appendTo(c.parent()),this._changeCellTag(b,"th")),c.remove(),this.editor.selection.setRangeAtEndOf(b.find("td, th").eq(d)))},c.prototype.insertRow=function(b,c){var d,e,f,g,h,i,j,k,l;for(null==c&&(c="after"),f=b.parent("tr"),e=f.closest("table"),h=0,e.find("tr").each(function(b,c){return h=Math.max(h,a(c).find("td").length)}),j=f.find("td, th").index(b),d=a("<tr/>"),g="td","after"===c&&f.parent().is("thead")?f.parent().next("tbody").prepend(d):"before"===c&&f.parent().is("thead")?(f.before(d),f.parent().next("tbody").prepend(f),this._changeCellTag(f,"td"),g="th"):f[c](d),i=k=1,l=h;l>=1?l>=k:k>=l;i=l>=1?++k:--k)a("<"+g+"/>").append(this.editor.util.phBr).appendTo(d);return this.editor.selection.setRangeAtStartOf(d.find("td, th").eq(j))},c.prototype.deleteCol=function(b){var c,d,e,f,g,h;return e=b.parent("tr"),h=e.closest("table").find("tr").length<2,g=b.siblings("td, th").length<1,h&&g?this.deleteTable(b):(f=e.find("td, th").index(b),c=b.next("td, th"),c.length>0||(c=e.prev("td, th")),d=e.closest("table"),d.find("col").eq(f).remove(),d.find("tr").each(function(b,c){return a(c).find("td, th").eq(f).remove()}),this.refreshTableWidth(d),this.editor.selection.setRangeAtEndOf(c))},c.prototype.insertCol=function(b,c){var d,e,f,g,h,i,j,k;return null==c&&(c="after"),h=b.parent("tr"),i=h.find("td, th").index(b),g=b.closest("table"),d=g.find("col").eq(i),g.find("tr").each(function(b){return function(d,e){var f,g;return g=a(e).parent().is("thead")?"th":"td",f=a("<"+g+"/>").append(b.editor.util.phBr),a(e).find("td, th").eq(i)[c](f)}}(this)),e=a("<col/>"),d[c](e),j=g.width(),k=Math.max(parseFloat(d.attr("width"))/2,50/j*100),d.attr("width",k+"%"),e.attr("width",k+"%"),this.refreshTableWidth(g),f="after"===c?b.next("td, th"):b.prev("td, th"),this.editor.selection.setRangeAtStartOf(f)},c.prototype.deleteTable=function(a){var b,c;return c=a.closest(".simditor-table"),b=c.next("p"),c.remove(),b.length>0?this.editor.selection.setRangeAtStartOf(b):void 0},c.prototype.command=function(a){var b;if(b=this.editor.selection.containerNode().closest("td, th"),b.length>0){if("deleteRow"===a)this.deleteRow(b);else if("insertRowAbove"===a)this.insertRow(b,"before");else if("insertRowBelow"===a)this.insertRow(b);else if("deleteCol"===a)this.deleteCol(b);else if("insertColLeft"===a)this.insertCol(b,"before");else if("insertColRight"===a)this.insertCol(b);else{if("deleteTable"!==a)return;this.deleteTable(b)}return this.editor.trigger("valuechanged")}},c}(h),D.Toolbar.addButton(F),E=function(b){function c(){return c.__super__.constructor.apply(this,arguments)}return M(c,b),c.prototype.name="strikethrough",c.prototype.icon="strikethrough",c.prototype.htmlTag="strike",c.prototype.disableTag="pre",c.prototype._activeStatus=function(){var a;return a=document.queryCommandState("strikethrough")===!0,this.setActive(a),this.active},c.prototype.command=function(){return document.execCommand("strikethrough"),this.editor.util.support.oninput||this.editor.trigger("valuechanged"),a(document).trigger("selectionchange")},c}(h),D.Toolbar.addButton(E),e=function(a){function b(){return b.__super__.constructor.apply(this,arguments)}return M(b,a),b.prototype.name="alignment",b.prototype.icon="align-left",b.prototype.htmlTag="p, h1, h2, h3, h4, td, th",b.prototype._init=function(){return this.menu=[{name:"left",text:this._t("alignLeft"),icon:"align-left",param:"left"},{name:"center",text:this._t("alignCenter"),icon:"align-center",param:"center"},{name:"right",text:this._t("alignRight"),icon:"align-right",param:"right"}],b.__super__._init.call(this)},b.prototype.setActive=function(a,c){return null==c&&(c="left"),"left"!==c&&"center"!==c&&"right"!==c&&(c="left"),"left"===c?b.__super__.setActive.call(this,!1):b.__super__.setActive.call(this,a),this.el.removeClass("align-left align-center align-right"),a&&this.el.addClass("align-"+c),this.setIcon("align-"+c),this.menuEl.find(".menu-item").show().end().find(".menu-item-"+c).hide()},b.prototype._status=function(){return this.nodes=this.editor.selection.nodes().filter(this.htmlTag),this.nodes.length<1?(this.setDisabled(!0),this.setActive(!1)):(this.setDisabled(!1),this.setActive(!0,this.nodes.first().css("text-align")))},b.prototype.command=function(a){if("left"!==a&&"center"!==a&&"right"!==a)throw new Error("simditor alignment button: invalid align "+a);return this.nodes.css({"text-align":"left"===a?"":a}),this.editor.trigger("valuechanged"),this.editor.inputManager.throttledSelectionChanged()},b}(h),D.Toolbar.addButton(e),D});