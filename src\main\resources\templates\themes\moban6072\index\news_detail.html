<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" th:content="${news.remark}">
    <meta name="keywords" th:content="${configs.get('seo_keywords').value}">
    <meta name="author" content="企业门户网站">

    <title th:text="${news.title} + ' - ' + ${configs.get('website_title').value} ?: '企业门户网站'">新闻详情 - 企业门户网站</title>

    <!--== Google Fonts ==-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="/static/moban6072/css/css2.css" rel="stylesheet">

    <!--== Bootstrap CSS ==-->
    <link href="/static/moban6072/css/bootstrap.min.css" rel="stylesheet">
    <!--== Icofont CSS ==-->
    <link href="/static/moban6072/css/icofont.css" rel="stylesheet">
    <!--== Swiper CSS ==-->
    <link href="/static/moban6072/css/swiper.min.css" rel="stylesheet">
    <!--== Main Style CSS ==-->
    <link href="/static/moban6072/css/style.css" rel="stylesheet">

</head>

<body>

<!--wrapper start-->
<div class="wrapper page-blog-details-wrapper">

  <!--== Start Header Wrapper ==-->
  <header class="header-wrapper">
    <div class="header-area header-default header-transparent sticky-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-4 col-sm-6 col-lg-2">
            <div class="header-logo-area">
              <a href="/">
                <img class="logo-main" th:src="'/UploadFilePath/config/' + ${configs.get('website_logo').value}" 
                     th:alt="${configs.get('website_company').value}" width="161" height="48">
                <img class="logo-light" th:src="'/UploadFilePath/config/' + ${configs.get('website_logo').value}" 
                     th:alt="${configs.get('website_company').value}" width="161" height="48">
              </a>
            </div>
          </div>
          <div class="col-lg-7 d-none d-lg-block">
            <div class="header-navigation-area">
              <ul class="main-menu nav position-relative">
                <li><a href="/">首页</a></li>
                <li class="has-submenu"><a href="/About/about.html">关于我们</a>
                  <ul class="submenu-nav">
                    <li><a href="/About/about.html">公司简介</a></li>
                    <li><a href="/About/scope.html">经营范围</a></li>
                    <li><a href="/About/civilization.html">企业文化</a></li>
                    <li><a href="/About/setup.html">人才体系</a></li>
                    <li><a href="/About/partner.html">合作伙伴</a></li>
                  </ul>
                </li>

                <li class="has-submenu active"><a href="/News/company.html">新闻中心</a>
                  <ul class="submenu-nav">
                    <li><a href="/News/company.html">公司新闻</a></li>
                    <li><a href="/News/industry.html">行业资讯</a></li>
                  </ul>
                </li>
                <li><a href="/Contact/contact.html">联系我们</a></li>
              </ul>
            </div>
          </div>
          <div class="col-8 col-sm-6 col-lg-3">
            <div class="header-action-area">
              <div class="header-action-btn">
                <a class="btn-theme" href="/Contact/contact.html"><span>获取报价</span></a>
              </div>
              <button class="btn-menu d-lg-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasWithBothOptions" aria-controls="offcanvasWithBothOptions">
                <span class="icon-line"></span>
                <span class="icon-line"></span>
                <span class="icon-line"></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!--== End Header Wrapper ==-->
  
  <main class="main-content">
    <!--== Start Page Title Area ==-->
    <section class="page-title-area bg-img" th:data-bg-img="'/UploadFilePath/config/' + ${configs.get('website_banner1').value}">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-12 m-auto">
            <div class="page-title-content text-center">
              <h2 class="title">新闻详情</h2>
              <div class="bread-crumbs">
                <a href="/"> 首页 </a>
                <span class="breadcrumb-sep"> // </span>
                <a th:href="'/News/' + ${news.catalog == '公司新闻' ? 'company' : 'industry'} + '.html'" 
                   th:text="${news.catalog}">新闻中心</a>
                <span class="breadcrumb-sep"> // </span>
                <span class="active">详情</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-overlay3"></div>
    </section>
    <!--== End Page Title Area ==-->
    
    <!--== Start Blog Area Wrapper ==-->
    <section class="blog-details-area">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="post-details-content">
              <div class="post-details-body">
                <div class="content-top">
                  <div class="meta">
                    <span class="author">
                      <span th:text="${configs.get('website_company').value}">公司</span>
                    </span>
                    <span class="dots"></span>
                    <span class="post-date" th:text="${#dates.format(news.createTime, 'dd MMMM, yyyy')}">发布日期</span>
                    <span class="dots"></span>
                    <span class="post-time" th:text="${news.catalog}">分类</span>
                  </div>
                  <h4 class="title" th:text="${news.title}">新闻标题</h4>
                  <div class="widget-tags" th:if="${news.catalog}">
                    <ul>
                      <li><a th:href="'/News/' + ${news.catalog == '公司新闻' ? 'company' : 'industry'} + '.html'" 
                             th:text="${news.catalog}">新闻分类</a></li>
                    </ul>
                  </div>
                </div>
                <div class="thumb" th:if="${news.picture != null and news.picture != ''}">
                  <img class="w-100" th:src="'/UploadFilePath/news/' + ${news.picture}" 
                       th:alt="${news.title}" width="1170" height="550">
                </div>
                <div class="content plr-100">
                  <div class="news-summary" th:if="${news.remark != null and news.remark != ''}">
                    <h4 class="title">摘要</h4>
                    <p th:text="${news.remark}">新闻摘要</p>
                  </div>
                  
                  <div class="news-content">
                    <div th:utext="${news.content}">
                      <p>新闻详细内容</p>
                    </div>
                  </div>
                  
                  <div class="news-meta">
                    <p class="text-muted">
                      <small>
                        发布时间：<span th:text="${#dates.format(news.createTime, 'yyyy年MM月dd日 HH:mm')}">发布时间</span>
                        <span th:if="${news.updateTime != null and news.updateTime != news.createTime}">
                          | 更新时间：<span th:text="${#dates.format(news.updateTime, 'yyyy年MM月dd日 HH:mm')}">更新时间</span>
                        </span>
                      </small>
                    </p>
                  </div>
                  
                  <div class="widget-social-icons">
                    <span>分享这篇文章：</span>
                    <div class="social-icons">
                      <a class="facebook" href="#/" onclick="shareToFacebook()"><i class="icofont-facebook"></i></a>
                      <a class="twitter" href="#/" onclick="shareToTwitter()"><i class="icofont-twitter"></i></a>
                      <a class="linkedin" href="#/" onclick="shareToLinkedIn()"><i class="icofont-linkedin"></i></a>
                      <a class="wechat" href="#/" onclick="shareToWechat()"><i class="icofont-wechat"></i></a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 相关新闻 -->
      <div class="related-posts-area" th:if="${not #lists.isEmpty(pageInfo.list)}">
        <div class="container">
          <div class="row">
            <div class="col-lg-12">
              <div class="related-posts">
                <h2 class="inner-title">相关新闻</h2>
                <div class="row">
                  <div class="col-md-4" th:each="relatedNews, iterStat : ${pageInfo.list}" th:if="${iterStat.index < 3 and relatedNews.id != news.id}">
                    <!--== Start Blog Post Item ==-->
                    <div class="post-item2">
                      <div class="thumb">
                        <a th:href="'/News/newsDetail/id/' + ${relatedNews.id}">
                          <img th:src="'/UploadFilePath/news/' + ${relatedNews.picture}" 
                               th:alt="${relatedNews.title}" width="350" height="270">
                        </a>
                      </div>
                      <div class="content">
                        <div class="author" th:text="${relatedNews.catalog}">分类</div>
                        <h4 class="title">
                          <a th:href="'/News/newsDetail/id/' + ${relatedNews.id}" th:text="${relatedNews.title}">相关新闻标题</a>
                        </h4>
                        <div class="meta">
                          <span class="post-date" th:text="${#dates.format(relatedNews.createTime, 'dd MMMM, yyyy')}">发布日期</span>
                          <span class="dots"></span>
                          <span class="post-time">阅读</span>
                        </div>
                      </div>
                    </div>
                    <!--== End Blog Post Item ==-->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!--== End Blog Area Wrapper ==-->

    <!--== Start Divider Area Wrapper ==-->
    <section class="divider-area divider-default-area">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-sm-8 col-md-7">
            <div class="content">
              <h2 class="title">关注我们获取<br> 最新资讯动态</h2>
            </div>
          </div>
          <div class="col-sm-4 col-md-5">
            <div class="divider-btn">
              <a class="btn-theme btn-white" th:href="'/News/' + ${news.catalog == '公司新闻' ? 'company' : 'industry'} + '.html'">
                返回列表 <i class="icofont-rounded-double-right"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="shape-group">
        <div class="shape-style4">
          <img src="/static/moban6072/picture/42.webp" alt="装饰图片" width="560" height="250">
        </div>
      </div>
    </section>
    <!--== End Divider Area Wrapper ==-->
  </main>

  <!--== Start Footer Area Wrapper ==-->
  <footer class="footer-area default-style">
    <div class="footer-main">
      <div class="container">
        <div class="row">
          <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="widget-item">
              <h4 class="widget-title">最新资讯</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-1">最新资讯</h4>
              <div id="dividerId-1" class="collapse widget-collapse-body">
                <div class="widget-blog-wrap">
                  <!-- 显示相关新闻 -->
                  <div class="blog-post-item" th:each="relatedNews, iterStat : ${pageInfo.list}" th:if="${iterStat.index < 2 and relatedNews.id != news.id}">
                    <div class="content">
                      <h4 class="title">
                        <i class="icon icofont-minus"></i>
                        <a th:href="'/News/newsDetail/id/' + ${relatedNews.id}" th:text="${relatedNews.title}">新闻标题</a>
                      </h4>
                      <div class="meta-date">
                        <a th:href="'/News/' + ${relatedNews.catalog == '公司新闻' ? 'company' : 'industry'} + '.html'">
                          <i class="icofont-calendar"></i>
                          <span th:text="${#dates.format(relatedNews.createTime, 'yyyy/MM/dd')}">日期</span>
                        </a>
                      </div>
                    </div>
                  </div>
                  <!-- 如果没有相关新闻，显示默认内容 -->
                  <div class="blog-post-item" th:if="${#lists.isEmpty(pageInfo.list)}">
                    <div class="content">
                      <h4 class="title"><i class="icon icofont-minus"></i> <a href="/News/company.html">欢迎关注我们的最新动态</a></h4>
                      <div class="meta-date"><a href="/News/company.html"><i class="icofont-calendar"></i> 最新</a></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 offset-lg-0 col-xl-3 offset-xl-1">
            <div class="widget-item">
              <h4 class="widget-title">主要服务</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-2">主要服务</h4>
              <div id="dividerId-2" class="collapse widget-collapse-body">
                <nav class="widget-menu-wrap">
                  <ul class="nav-menu nav">
                    <li><a href="/About/scope.html"><i class="icofont-minus"></i>经营范围</a></li>
                    <li><a href="/About/setup.html"><i class="icofont-minus"></i>人才体系</a></li>
                    <li><a href="/About/civilization.html"><i class="icofont-minus"></i>企业文化</a></li>
                    <li><a href="/Contact/contact.html"><i class="icofont-minus"></i>联系我们</a></li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 col-xl-3">
            <div class="widget-item ml-40 ml-lg-20 md-ml-0">
              <h4 class="widget-title">重要链接</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-3">重要链接</h4>
              <div id="dividerId-3" class="collapse widget-collapse-body">
                <nav class="widget-menu-wrap">
                  <ul class="nav-menu nav">
                    <li><a href="/About/about.html"><i class="icofont-minus"></i>关于我们</a></li>
                    <li><a href="/Contact/contact.html"><i class="icofont-minus"></i>客户支持</a></li>
                    <li><a href="/News/company.html"><i class="icofont-minus"></i>新闻中心</a></li>
                    <li><a href="/Contact/contact.html"><i class="icofont-minus"></i>联系我们</a></li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
          <div class="col-md-6 col-lg-2 col-xl-2">
            <div class="widget-item ml-35 lg-ml-0">
              <h4 class="widget-title">联系信息</h4>
              <h4 class="widget-title widget-collapsed-title collapsed" data-bs-toggle="collapse" data-bs-target="#dividerId-4">联系信息</h4>
              <div id="dividerId-4" class="collapse widget-collapse-body">
                <div class="contact-info">
                  <p><i class="icofont-phone"></i> <span th:text="${configs.get('website_phone').value}">联系电话</span></p>
                  <p><i class="icofont-email"></i> <span th:text="${configs.get('website_email').value}">邮箱地址</span></p>
                  <p><i class="icofont-location-pin"></i> <span th:text="${configs.get('website_address').value}">公司地址</span></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-shape bg-img" data-bg-img="/static/moban6072/assets/img/photos/footer1.webp"></div>
    </div>
    <div class="footer-bottom">
      <div class="container">
        <div class="footer-bottom-content">
          <div class="row">
            <div class="col-md-12">
              <div class="widget-copyright">
                <p>
                  Copyright &copy; 2025 <span th:text="${configs.get('website_company').value}">公司名称</span> 版权所有
                  <span th:if="${configs.get('website_record').value != null and configs.get('website_record').value != ''}"
                        th:text="'| ' + ${configs.get('website_record').value}"></span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <!--== End Footer Area Wrapper ==-->

  <!--== Scroll Top Button ==-->
  <div class="scroll-to-top"><span class="icofont-rounded-double-left icofont-rotate-90"></span></div>

  <!--== Start Side Menu ==-->
  <aside class="off-canvas-area offcanvas offcanvas-end" data-bs-scroll="true" tabindex="-1" id="offcanvasWithBothOptions">
    <div class="offcanvas-header">
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Start Mobile Menu Wrapper -->
      <div class="res-mobile-menu">
        <nav id="offcanvasNav" class="offcanvas-menu">
          <ul>
            <li><a href="/">首页</a></li>
            <li class="has-submenu"><a href="/About/about.html">关于我们</a>
              <ul>
                <li><a href="/About/about.html">公司简介</a></li>
                <li><a href="/About/scope.html">经营范围</a></li>
                <li><a href="/About/civilization.html">企业文化</a></li>
                <li><a href="/About/setup.html">人才体系</a></li>
              </ul>
            </li>

            <li class="has-submenu"><a href="/News/company.html">新闻中心</a>
              <ul>
                <li><a href="/News/company.html">公司新闻</a></li>
                <li><a href="/News/industry.html">行业资讯</a></li>
              </ul>
            </li>
            <li><a href="/Contact/contact.html">联系我们</a></li>
          </ul>
        </nav>
      </div>
      <!-- End Mobile Menu Wrapper -->
    </div>
  </aside>
  <!--== End Side Menu ==-->
</div>

<!--=======================Javascript============================-->

<!--=== Modernizr Min Js ===-->
<script src="/static/moban6072/js/modernizr.js"></script>
<!--=== jQuery Min Js ===-->
<script src="/static/moban6072/js/jquery-main.js"></script>
<!--=== jQuery Migration Min Js ===-->
<script src="/static/moban6072/js/jquery-migrate.js"></script>
<!--=== Popper Min Js ===-->
<script src="/static/moban6072/js/popper.min.js"></script>
<!--=== Bootstrap Min Js ===-->
<script src="/static/moban6072/js/bootstrap.min.js"></script>
<!--=== jquery Swiper Min Js ===-->
<script src="/static/moban6072/js/swiper.min.js"></script>
<!--=== jquery Countdown Js ===-->
<script src="/static/moban6072/js/jquery.countdown.min.js"></script>
<!--=== Isotope Min Js ===-->
<script src="/static/moban6072/js/isotope.pkgd.min.js"></script>

<!--=== Custom Js ===-->
<script src="/static/moban6072/js/custom.js"></script>

<!-- 社交分享功能 -->
<script>
function shareToFacebook() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank', 'width=600,height=400');
}

function shareToTwitter() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
}

function shareToLinkedIn() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank', 'width=600,height=400');
}

function shareToWechat() {
    // 微信分享通常需要微信SDK，这里提供一个简单的提示
    alert('请复制链接分享到微信：' + window.location.href);
}
</script>

</body>

</html>
