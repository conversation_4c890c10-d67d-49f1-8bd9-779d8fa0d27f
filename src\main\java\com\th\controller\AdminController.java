package com.th.controller;

import com.th.common.Constant;
import com.th.common.NumberHelper;
import com.th.pojo.*;
import com.th.service.AdminService;
import com.th.service.TemplateService;
import com.th.service.MgrService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "Admin")
public class AdminController {
    @Value("${system.username}")
    private String systemUsername;

    @Value("${system.password}")
    private String systemPassword;

    @Value("${system.upload-file-path}")
    private String UploadFilePath;

    @Autowired
    private AdminService adminService;

    @Autowired
    private MgrService mgrService;

    @Autowired
    private TemplateService templateService;

    /**
     * 获取系统配置信息
     *
     * @return
     */
    public HashMap<String, Config> config(HttpSession session) {
        Integer companyId = getCompanyId(session);
        ArrayList<Config> configArrayList = adminService.getConfigs(companyId);
        // List转Map，基于JDK1.8的Stream API和函数式接口实现，同时还可去重
        HashMap<String, Config> configHashMap = (HashMap<String, Config>) configArrayList.stream().collect(Collectors.toMap(Config::getKey, Function.identity()));
        for (String configAttr : Constant.CONFIG_ATTRS) {
            if (!configHashMap.containsKey(configAttr)) {
                Config config = new Config();
                config.setCompanyId(companyId);
                config.setKey(configAttr);
                config.setValue("");
                configHashMap.put(configAttr, config);
            }
        }
        return configHashMap;
    }

    /**
     * 跳转登录页面
     *
     * @param model
     * @return
     */
    @GetMapping(value = "login")
    public String login(HttpSession session, Model model) {
        model.addAttribute("configs", config(session));
        return "admin/login.html";
    }

    /**
     * 登录
     *
     * @param request
     * @param session
     * @param model
     * @return
     */
    @PostMapping(value = "loginAction")
    public String loginAction(HttpServletRequest request, HttpSession session, Model model) {
        model.addAttribute("configs", config(session));
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        if (username == null || "".equals(username.trim())
                || password == null || "".equals(password.trim()) ) {
            model.addAttribute("msg","请录入账号或密码!");
            return "admin/login.html";
        }

        // 检查是否为超级管理员
        if (systemUsername.equals(username) && systemPassword.equals(password)) {
            session.setAttribute("admin", "super_admin");
            session.setAttribute("companyId", 0); // 超级管理员使用特殊的companyId
            UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
            Browser browser = userAgent.getBrowser();
            OperatingSystem os = userAgent.getOperatingSystem();
            Timestamp ts = new Timestamp(new Date().getTime());
            try {
                adminService.recordLog(request.getRemoteAddr(), os.toString(), browser.toString(), "super_admin", ts, 0);
            } catch (Exception e) {
                // Todo
            } finally {
                return "redirect:index.html";
            }
        }

        // 检查公司管理员
        Integer companyId = getCompanyId(session);
        Company company = mgrService.selectCompanyByUsername(username, companyId);

        if (company != null && password.equals(company.getPassword())){
            session.setAttribute("admin","admin");
            UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
            Browser browser = userAgent.getBrowser();
            OperatingSystem os = userAgent.getOperatingSystem();
            Timestamp ts = new Timestamp(new Date().getTime());
            try {
                adminService.recordLog(request.getRemoteAddr(), os.toString(), browser.toString(), "admin", ts, companyId);
            } catch (Exception e) {
                // Todo
            } finally {
                return "redirect:index.html";
            }
        } else{
            model.addAttribute("msg","账号或密码错误!");
            return "admin/login.html";
        }
    }

    /**
     * 注销
     *
     * @param session
     * @return
     */
    @GetMapping(value = "loginOut")
    public String loginOut(HttpSession session){
        session.removeAttribute("admin");
        return "redirect:login";
    }

    /**
     * 系统主页
     *
     * @param session
     * @param model
     * @return
     */
    @GetMapping(value = "index")
    public String index(HttpServletRequest request, HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/index.html";
            } else if (admin.equals("super_admin")) {
                // 超级管理员使用专门的首页
                model.addAttribute("totalCompanies", mgrService.getAllCompanies().size());
                model.addAttribute("totalTemplates", templateService.getAllAvailableTemplates().size());
                return "admin/super_admin_index.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 系统配置
     *
     * @param model
     * @return
     */
    @GetMapping(value = "config")
    public String config(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/config.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 系统设置
     *
     * @param model
     * @return
     */
    @GetMapping(value = "setting")
    public String setting(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/setting.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 公司简介
     *
     * @param model
     * @return
     */
    @GetMapping(value = "about")
    public String about(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/about.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 轮播图
     *
     * @param model
     * @return
     */
    @GetMapping(value = "slider")
    public String slider(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/slider.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 经营范围
     *
     * @param model
     * @return
     */
    @GetMapping(value = "scope")
    public String scope(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/scope.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 企业文化
     *
     * @param model
     * @return
     */
    @GetMapping(value = "civilization")
    public String civilization(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/civilization.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 人才体系
     *
     * @param model
     * @return
     */
    @GetMapping(value = "setup")
    public String setup(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/setup.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 人才招聘
     *
     * @param model
     * @return
     */
    @GetMapping(value = "talent")
    public String talent(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/talent.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * key - value 形式保存到数据库
     *
     * @param requestMap
     * @param session
     * @return
     */
    @ResponseBody
    @PostMapping(value = "configAction")
    public HashMap<String ,Object> configAction(@RequestBody HashMap<String ,Object> requestMap, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        HashMap<String ,Object> responseMap = new HashMap<>();
        if (null != admin) {
            if (admin.equals("admin")) {
                Integer companyId = getCompanyId(session);
                // 保存配置
                saveOrUpdateConfig(requestMap, companyId);
                responseMap.put("status", true);
            } else {
                responseMap.put("status", false);
            }
        } else {
            responseMap.put("status", false);
        }
        return responseMap;
    }

    /**
     * 增加或者更新配置
     * @param dataMap
     * @param companyId
     */
    private void saveOrUpdateConfig(Map<String, Object> dataMap, Integer companyId) {
        for (String key : Constant.CONFIG_ATTRS) {
            String value = dataMap.get(key) == null ? "" : String.valueOf(dataMap.get(key));
            if (!dataMap.containsKey(key)) {
                continue;
            }
            Config config = adminService.getConfig(key, companyId);
            if (config == null) {
                adminService.saveConfig(key, value, companyId);
            } else {
                adminService.updateConfig(key, value, companyId);
            }
        }
    }

    /**
     * 登录日志
     *
     * @param request
     * @param session
     * @param model
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping(value = "log")
    public String log(HttpServletRequest request, HttpSession session, Model model,
                       @RequestParam(defaultValue = "1") int pageNo,
                       @RequestParam(defaultValue = "10") int pageSize) {
        String admin = (String) session.getAttribute("admin");
        String search = request.getParameter("search");
        if (null != admin) {
            if (admin.equals("admin")) {
                PageHelper.startPage(pageNo,pageSize);
                PageInfo<Log> pageInfo = new PageInfo<>(adminService.getLog(search, getCompanyId(session)));
                model.addAttribute(pageInfo);
                model.addAttribute("configs", config(session));
                return "admin/log.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 新闻资讯
     *
     * @param request
     * @param session
     * @param model
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping(value = "news")
    public String news(HttpServletRequest request, HttpSession session, Model model,
                       @RequestParam(defaultValue = "1") int pageNo,
                       @RequestParam(defaultValue = "10") int pageSize) {
        String admin = (String) session.getAttribute("admin");
        String search = request.getParameter("search");
        if (null != admin) {
            if (admin.equals("admin")) {
                PageHelper.startPage(pageNo,pageSize);
                PageInfo<News> pageInfo = new PageInfo<>(adminService.getNews(search, getCompanyId(session)));
                model.addAttribute(pageInfo);
                model.addAttribute("configs", config(session));
                return "admin/news.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 添加新闻资讯
     *
     * @param session
     * @return
     */
    @GetMapping(value = "addNews")
    public String addNews(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/news_add.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 添加新闻资讯方法
     *
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "addNewsAction")
    public String addNewsAction(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String title = request.getParameter("title");
        String remark = request.getParameter("remark");
        String catalog = request.getParameter("catalog");
        String content = request.getParameter("content");
        String picture = request.getParameter("pic");
        Timestamp ts = new Timestamp(new Date().getTime());
        News news = new News();
        news.setTitle(title);
        news.setContent(content);
        news.setRemark(remark);
        news.setCatalog(catalog);
        news.setPicture(picture);
        news.setCreateTime(ts);
        news.setUpdateTime(ts);
        news.setCompanyId(getCompanyId(session));
        if (null != admin) {
            if (admin.equals("admin")) {
                int status = adminService.addNewsAction(news);
                return "redirect:news.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 删除新闻资讯方法
     *
     * @param request
     * @param session
     * @return
     */
    @GetMapping(value = "deleteNews")
    public String deleteNews(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                Integer status = adminService.deleteNews(id);
                return "redirect:news.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改新闻资讯
     *
     * @param session
     * @param request
     * @param model
     * @return
     */
    @GetMapping(value = "updateNews")
    public String updateNews(HttpSession session, HttpServletRequest request, Model model) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                News news = adminService.selectNewsById(id);
                model.addAttribute("news", news);
                model.addAttribute("configs", config(session));
                return "admin/news_update.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改新闻资讯方法
     *
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "updateNewsAction")
    public String updateNewsAction(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        String remark = request.getParameter("remark");
        String catalog = request.getParameter("catalog");
        String title = request.getParameter("title");
        String picture = request.getParameter("pic");
        String content = request.getParameter("content");
        Timestamp ts = new Timestamp(new Date().getTime());
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                News news = new News();
                news.setId(id);
                news.setContent(content);
                news.setTitle(title);
                news.setPicture(picture);
                news.setRemark(remark);
                news.setCatalog(catalog);
                news.setUpdateTime(ts);
                news.setCompanyId(getCompanyId(session));
                adminService.updateNewsAction(news);
                return "redirect:news.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 渠道
     *
     * @param request
     * @param session
     * @param pageNo
     * @param pageSize
     * @param model
     * @return
     */
    @GetMapping(value = "project")
    public String project(HttpServletRequest request, HttpSession session, Model model,
                          @RequestParam(defaultValue = "1") int pageNo,
                          @RequestParam(defaultValue = "10") int pageSize) {
        String admin = (String) session.getAttribute("admin");
        String search = request.getParameter("search");
        if (null != admin) {
            if (admin.equals("admin")) {
                PageHelper.startPage(pageNo, pageSize);
                PageInfo<Project> pageInfo = new PageInfo<>(adminService.getProject(search, getCompanyId(session)));
                model.addAttribute(pageInfo);
                model.addAttribute("configs", config(session));
                return "admin/project.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 添加渠道
     *
     * @param session
     * @return
     */
    @GetMapping(value = "addProject")
    public String addProject(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/project_add.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 添加渠道方法
     *
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "addProjectAction")
    public String addProjectAction(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String title = request.getParameter("title");
        String remark = request.getParameter("remark");
        String picture = request.getParameter("pic");
        String content = request.getParameter("content");
        Timestamp ts = new Timestamp(new Date().getTime());
        Project project = new Project();
        project.setTitle(title);
        project.setContent(content);
        project.setRemark(remark);
        project.setPicture(picture);
        project.setCreateTime(ts);
        project.setUpdateTime(ts);
        project.setCompanyId(getCompanyId(session));
        if (null != admin) {
            if (admin.equals("admin")) {
                adminService.addProjectAction(project);
                return "redirect:project.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 删除渠道方法
     *
     * @param request
     * @param session
     * @return
     */
    @GetMapping(value = "deleteProject")
    public String deleteProject(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                int status = adminService.deleteProject(id);
                return "redirect:project.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改渠道
     *
     * @param session
     * @param request
     * @param model
     * @return
     */
    @GetMapping(value = "updateProject")
    public String updateProject(HttpSession session, HttpServletRequest request, Model model) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                Project project = adminService.selectProjectById(id);
                model.addAttribute("project", project);
                model.addAttribute("configs", config(session));
                return "admin/project_update.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改渠道方法
     *
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "updateProjectAction")
    public String updateProjectAction(HttpServletRequest request,HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String picture = request.getParameter("pic");
        String title = request.getParameter("title");
        String remark = request.getParameter("remark");
        String content = request.getParameter("content");
        String idStr = request.getParameter("id");
        Timestamp ts = new Timestamp(new Date().getTime());
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                Project project = new Project();
                project.setId(id);
                project.setRemark(remark);
                project.setContent(content);
                project.setTitle(title);
                project.setPicture(picture);
                project.setUpdateTime(ts);
                project.setCompanyId(getCompanyId(session));
                adminService.updateProjectAction(project);
                return "redirect:project.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 合作伙伴
     *
     * @param request
     * @param session
     * @param pageNo
     * @param pageSize
     * @param model
     * @return
     */
    @GetMapping(value = "partner")
    public String partner(HttpServletRequest request, HttpSession session, Model model,
                          @RequestParam(defaultValue = "1") int pageNo,
                          @RequestParam(defaultValue = "10") int pageSize) {
        String admin = (String) session.getAttribute("admin");
        String search = request.getParameter("search");
        if (null != admin) {
            if (admin.equals("admin")) {
                PageHelper.startPage(pageNo, pageSize);
                PageInfo<Partner> pageInfo = new PageInfo<>(adminService.getPartner(search, getCompanyId(session)));
                model.addAttribute(pageInfo);
                model.addAttribute("configs", config(session));
                return "admin/partner.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 添加合作伙伴
     *
     * @param session
     * @return
     */
    @GetMapping(value = "addPartner")
    public String addPartner(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/partner_add.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 添加合作伙伴方法
     *
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "addPartnerAction")
    public String addPartnerAction(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String company = request.getParameter("company");
        String picture = request.getParameter("pic");
        String url = request.getParameter("url");
        Timestamp ts = new Timestamp(new Date().getTime());
        Partner partner = new Partner();
        partner.setCompany(company);
        partner.setUrl(url);
        partner.setPicture(picture);
        partner.setCreateTime(ts);
        partner.setUpdateTime(ts);
        partner.setCompanyId(getCompanyId(session));
        if (null != admin) {
            if (admin.equals("admin")) {
                adminService.addPartnerAction(partner);
                return "redirect:partner.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 删除合作伙伴方法
     *
     * @param request
     * @param session
     * @return
     */
    @GetMapping(value = "deletePartner")
    public String deletePartner(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                int status = adminService.deletePartner(id);
                return "redirect:partner.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改合作伙伴
     *
     * @param session
     * @param request
     * @param model
     * @return
     */
    @GetMapping(value = "updatePartner")
    public String updatePartner(HttpSession session, HttpServletRequest request, Model model) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                Partner partner = adminService.selectPartnerById(id);
                model.addAttribute("partner", partner);
                model.addAttribute("configs", config(session));
                return "admin/partner_update.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改合作伙伴方法
     *
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "updatePartnerAction")
    public String updatePartnerAction(HttpServletRequest request,HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String picture = request.getParameter("pic");
        String url = request.getParameter("url");
        String company = request.getParameter("company");
        String idStr = request.getParameter("id");
        Timestamp ts = new Timestamp(new Date().getTime());
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                Partner partner = new Partner();
                partner.setId(id);
                partner.setUrl(url);
                partner.setCompany(company);
                partner.setPicture(picture);
                partner.setUpdateTime(ts);
                partner.setCompanyId(getCompanyId(session));
                adminService.updatePartnerAction(partner);
                return "redirect:partner.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 研发产品
     *
     * @param request
     * @param session
     * @param pageNo
     * @param pageSize
     * @param model
     * @return
     */
    @GetMapping(value = "product")
    public String product(HttpServletRequest request, HttpSession session, Model model,
                          @RequestParam(defaultValue = "1") int pageNo,
                          @RequestParam(defaultValue = "10") int pageSize) {
        String admin = (String) session.getAttribute("admin");
        String search = request.getParameter("search");
        if (null != admin) {
            if (admin.equals("admin")) {
                PageHelper.startPage(pageNo, pageSize);
                PageInfo<Product> pageInfo = new PageInfo<>(adminService.getProduct(search, getCompanyId(session)));
                model.addAttribute(pageInfo);
                model.addAttribute("configs", config(session));
                return "admin/product.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 添加研发产品
     *
     * @param session
     * @return
     */
    @GetMapping(value = "addProduct")
    public String addProduct(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/product_add.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 添加研发产品方法
     *
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "addProductAction")
    public String addProductAction(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String title = request.getParameter("title");
        String remark = request.getParameter("remark");
        String isOpenA = request.getParameter("isOpenA");
        String android = request.getParameter("android");
        String isOpenI = request.getParameter("isOpenI");
        String ios = request.getParameter("ios");
        String picture = request.getParameter("pic");
        Timestamp ts = new Timestamp(new Date().getTime());
        Product product = new Product();
        product.setTitle(title);
        product.setRemark(remark);
        product.setIsOpenA(isOpenA);
        product.setAndroid(android);
        product.setIsOpenI(isOpenI);
        product.setIos(ios);
        product.setPicture(picture);
        product.setCreateTime(ts);
        product.setUpdateTime(ts);
        product.setCompanyId(getCompanyId(session));
        if (null != admin) {
            if (admin.equals("admin")) {
                adminService.addProductAction(product);
                return "redirect:product.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 删除研发产品方法
     *
     * @param request
     * @param session
     * @return
     */
    @GetMapping(value = "deleteProduct")
    public String deleteProduct(HttpServletRequest request, HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                int status = adminService.deleteProduct(id);
                return "redirect:product.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改研发产品
     *
     * @param session
     * @param request
     * @param model
     * @return
     */
    @GetMapping(value = "updateProduct")
    public String updateProduct(HttpSession session, HttpServletRequest request, Model model) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                Product product = adminService.selectProductById(id);
                model.addAttribute("product", product);
                model.addAttribute("configs", config(session));
                return "admin/product_update.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改研发产品方法
     *
     * @param request
     * @param session
     * @return
     */
    @PostMapping(value = "updateProductAction")
    public String updateProductAction(HttpServletRequest request,HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        String idStr = request.getParameter("id");
        String title = request.getParameter("title");
        String remark = request.getParameter("remark");
        String isOpenA = request.getParameter("isOpenA");
        String android = request.getParameter("android");
        String isOpenI = request.getParameter("isOpenI");
        String ios = request.getParameter("ios");
        String picture = request.getParameter("pic");
        Timestamp ts = new Timestamp(new Date().getTime());
        Boolean b = NumberHelper.isInteger(idStr);
        if (null != admin && b) {
            if (admin.equals("admin")) {
                Integer id = Integer.parseInt(idStr);
                Product product = new Product();
                product.setId(id);
                product.setTitle(title);
                product.setRemark(remark);
                product.setIsOpenA(isOpenA);
                product.setAndroid(android);
                product.setIsOpenI(isOpenI);
                product.setIos(ios);
                product.setPicture(picture);
                product.setUpdateTime(ts);
                product.setCompanyId(getCompanyId(session));
                adminService.updateProductAction(product);
                return "redirect:product.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    //----------------------------------------------------------------------------------------------------------------//

    /**
     * 单文件上传（图片，视频，音频等...）
     *
     * @param request
     * @param session
     * @param file
     * @return
     */
    @ResponseBody
    @RequestMapping("uploadFileAction")
    public HashMap<String, Object> uploadFileAction(HttpServletRequest request, HttpSession session,
                                                    @RequestParam(name = "fileType") String fileType,
                                                    @RequestParam(name = "oneFile") MultipartFile file) {
        HashMap<String, Object> responseMap = new HashMap<>();
        String admin = (String) session.getAttribute("admin");
        if (null != admin) {
            if (admin.equals("admin")) {
                System.out.println("fileType => " + fileType);
                String newsFilePath = UploadFilePath;
                if (fileType.equals("news")) {
                    newsFilePath += "news";
                } else if (fileType.equals("partner")) {
                    newsFilePath += "partner";
                } else if (fileType.equals("project")) {
                    newsFilePath += "project";
                } else if (fileType.equals("product")) {
                    newsFilePath += "product";
                } else if (fileType.equals("vx")) {
                    newsFilePath += "vx";
                } else if (fileType.equals("logo")) {
                    newsFilePath += "logo";
                } else if (fileType.equals("banner")) {
                    newsFilePath += "banner";
                } else {
                    newsFilePath = "";
                }

                System.out.println("newsFilePath => " + newsFilePath);

                if (file != null && newsFilePath != "") {
                    try {
                        String uuidStr = UUID.randomUUID().toString();
                        String uuid = uuidStr.substring(0, 8) + uuidStr.substring(9, 13) + uuidStr.substring(14, 18) + uuidStr.substring(19, 23) + uuidStr.substring(24);

                        // 从文件名称的后缀名.结束（如：hello.pdf => 把hello去掉，留下.pdf）
                        String OldFileName = file.getOriginalFilename();// 原来文件名，如：hello.pdf
                        int beginIndex = OldFileName.lastIndexOf(".");// 从后匹配"."
                        String newFileName = uuid + OldFileName.substring(beginIndex);// 新文件名，如uuid.pdf

                        File path = new File(ResourceUtils.getURL("classpath:static").getPath().replace("%20", " ").replace('/', '\\'));
                        path = new File("");
                        String destFileName = newsFilePath + File.separator + newFileName;// 存储路径 + 新文件名

                        // 复制文件到指定目录
                        File destFile = new File(destFileName);
                        destFile.getParentFile().mkdirs();
                        file.transferTo(destFile);

                        responseMap.put("newFileName", newFileName);// 返回新文件名
                        responseMap.put("status", "0");// 文件上传成功
                    } catch (FileNotFoundException e) {
                        responseMap.put("status", "-2");// 文件上传异常
                    } catch (IOException e) {
                        e.printStackTrace();
                        responseMap.put("status", "-3");// 其他异常
                    }
                } else {
                    responseMap.put("status", "-1");// 不符合文件上传条件
                }
            } else {
                responseMap.put("status", "-5");// 不是管理员
            }
        } else {
            responseMap.put("status", "-4");// 管理员未登录
        }
        return responseMap;
    }

    /**
     * 系统配置
     *
     * @param model
     * @return
     */
    @GetMapping(value = "password")
    public String password(HttpSession session, Model model) {
        String admin = (String) session.getAttribute("admin");
        // 双重校验
        if (null != admin) {
            if (admin.equals("admin")) {
                model.addAttribute("configs", config(session));
                return "admin/password.html";
            } else {
                return "redirect:login";
            }
        } else {
            return "redirect:login";
        }
    }

    /**
     * 修改密码
     *
     * @param session
     * @param requestMap
     * @param model
     * @return
     */
    @ResponseBody
    @PostMapping(value = "updatePassword")
    public HashMap<String ,Object> updatePassword(HttpSession session, @RequestBody HashMap<String ,Object> requestMap, Model model) {
        HashMap<String ,Object> responseMap = new HashMap<>();
        String admin = (String) session.getAttribute("admin");
        if (null != admin) {
            if (admin.equals("admin")) {
                Integer companyId = getCompanyId(session);
                Company company = mgrService.selectCompanyById(companyId);
                String oldPassword = (String)requestMap.get("oldPassword");
                if (!oldPassword.equals(company.getPassword())) {
                    responseMap.put("status", false);
                    responseMap.put("msg", "原密码不正确");
                    return responseMap;
                }
                String password = (String)requestMap.get("password");
                mgrService.updatePassword(companyId, password);
                responseMap.put("status", true);
            } else {
                responseMap.put("status", false);
            }
        } else {
            responseMap.put("status", false);
        }
        return responseMap;
    }

    /**
     * 获取公司ID
     * @param session
     * @return
     */
    private Integer getCompanyId(HttpSession session) {
        return (Integer)session.getAttribute(Constant.COMPANY_ID);
    }

    /**
     * 检查是否为超级管理员
     * @param session
     * @return
     */
    private boolean isSuperAdmin(HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        return "super_admin".equals(admin);
    }

    /**
     * 检查是否为管理员（包括超级管理员和公司管理员）
     * @param session
     * @return
     */
    private boolean isAdmin(HttpSession session) {
        String admin = (String) session.getAttribute("admin");
        return "admin".equals(admin) || "super_admin".equals(admin);
    }

    // ==================== 超级管理员模板管理功能 ====================

    /**
     * 模板管理页面（仅超级管理员可访问）
     */
    @GetMapping(value = "templateManagement")
    public String templateManagement(HttpSession session, Model model,
                                   @RequestParam(defaultValue = "1") int pageNo,
                                   @RequestParam(defaultValue = "10") int pageSize) {
        if (!isSuperAdmin(session)) {
            return "redirect:login";
        }

        // 获取所有公司列表
        PageHelper.startPage(pageNo, pageSize);
        PageInfo<Company> pageInfo = new PageInfo<>(mgrService.getAllCompanies());

        // 获取所有可用模板
        List<Template> templates = templateService.getAllAvailableTemplates();

        model.addAttribute("pageInfo", pageInfo);
        model.addAttribute("templates", templates);
        model.addAttribute("currentPage", "templateManagement");

        return "admin/template_management.html";
    }

    /**
     * 设置公司模板（仅超级管理员可访问）
     */
    @ResponseBody
    @PostMapping(value = "setCompanyTemplate")
    public HashMap<String, Object> setCompanyTemplate(@RequestBody HashMap<String, Object> requestMap,
                                                     HttpSession session) {
        HashMap<String, Object> responseMap = new HashMap<>();

        if (!isSuperAdmin(session)) {
            responseMap.put("status", false);
            responseMap.put("msg", "权限不足");
            return responseMap;
        }

        try {
            Integer companyId = Integer.parseInt(String.valueOf(requestMap.get("companyId")));
            String templateCode = String.valueOf(requestMap.get("templateCode"));

            boolean success = templateService.setCompanyTemplate(companyId, templateCode);

            if (success) {
                responseMap.put("status", true);
                responseMap.put("msg", "模板设置成功");
            } else {
                responseMap.put("status", false);
                responseMap.put("msg", "模板设置失败");
            }
        } catch (Exception e) {
            responseMap.put("status", false);
            responseMap.put("msg", "参数错误：" + e.getMessage());
        }

        return responseMap;
    }

    /**
     * 批量设置公司模板（仅超级管理员可访问）
     */
    @ResponseBody
    @PostMapping(value = "batchSetTemplate")
    public HashMap<String, Object> batchSetTemplate(@RequestBody HashMap<String, Object> requestMap,
                                                   HttpSession session) {
        HashMap<String, Object> responseMap = new HashMap<>();

        if (!isSuperAdmin(session)) {
            responseMap.put("status", false);
            responseMap.put("msg", "权限不足");
            return responseMap;
        }

        try {
            String templateCode = String.valueOf(requestMap.get("templateCode"));
            @SuppressWarnings("unchecked")
            List<Integer> companyIds = (List<Integer>) requestMap.get("companyIds");

            int successCount = 0;
            for (Integer companyId : companyIds) {
                if (templateService.setCompanyTemplate(companyId, templateCode)) {
                    successCount++;
                }
            }

            responseMap.put("status", true);
            responseMap.put("msg", String.format("成功设置 %d/%d 个公司的模板", successCount, companyIds.size()));
        } catch (Exception e) {
            responseMap.put("status", false);
            responseMap.put("msg", "批量设置失败：" + e.getMessage());
        }

        return responseMap;
    }
}
