package com.th.service;

import com.th.common.Constant;
import com.th.mapper.AdminMapper;
import com.th.pojo.Config;
import com.th.pojo.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 模板管理服务类
 */
@Service
public class TemplateService {

    @Autowired
    private AdminMapper adminMapper;

    /**
     * 获取所有可用模板
     * @return 模板列表
     */
    public List<Template> getAllAvailableTemplates() {
        List<Template> templates = new ArrayList<>();
        
        // 基于常量中定义的模板创建模板对象
        for (int i = 0; i < Constant.AVAILABLE_TEMPLATES.length; i++) {
            String templateCode = Constant.AVAILABLE_TEMPLATES[i];
            Template template = new Template();
            template.setId(i + 1);
            template.setTemplateCode(templateCode);
            template.setTemplatePath(Constant.TEMPLATE_PREFIX + templateCode);
            template.setIsActive(true);
            template.setSortOrder(i + 1);
            template.setCreateTime(new Timestamp(new Date().getTime()));
            template.setUpdateTime(new Timestamp(new Date().getTime()));
            
            // 设置模板名称和描述
            switch (templateCode) {
                case "default":
                    template.setTemplateName("默认模板");
                    template.setTemplateDescription("经典企业门户模板，稳重大气");
                    template.setIsPremium(false);
                    break;
                case "modern":
                    template.setTemplateName("现代风格");
                    template.setTemplateDescription("现代简约设计，适合科技企业");
                    template.setIsPremium(true);
                    break;
                case "business":
                    template.setTemplateName("商务风格");
                    template.setTemplateDescription("专业商务设计，适合传统企业");
                    template.setIsPremium(true);
                    break;
                case "creative":
                    template.setTemplateName("创意风格");
                    template.setTemplateDescription("创意设计，适合创新型企业");
                    template.setIsPremium(true);
                    break;
                default:
                    template.setTemplateName(templateCode);
                    template.setTemplateDescription("自定义模板");
                    template.setIsPremium(false);
            }
            
            templates.add(template);
        }
        
        return templates;
    }

    /**
     * 根据模板代码获取模板信息
     * @param templateCode 模板代码
     * @return 模板信息
     */
    public Template getTemplateByCode(String templateCode) {
        List<Template> templates = getAllAvailableTemplates();
        return templates.stream()
                .filter(template -> template.getTemplateCode().equals(templateCode))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取公司当前使用的模板
     * @param companyId 公司ID
     * @return 模板代码
     */
    public String getCurrentTemplate(Integer companyId) {
        Config config = adminMapper.getConfig("website_template", companyId);
        if (config != null && config.getValue() != null && !config.getValue().isEmpty()) {
            return config.getValue();
        }
        return Constant.DEFAULT_TEMPLATE;
    }

    /**
     * 设置公司使用的模板
     * @param companyId 公司ID
     * @param templateCode 模板代码
     * @return 是否成功
     */
    public boolean setCompanyTemplate(Integer companyId, String templateCode) {
        // 验证模板是否存在
        if (!Arrays.asList(Constant.AVAILABLE_TEMPLATES).contains(templateCode)) {
            return false;
        }

        Config config = adminMapper.getConfig("website_template", companyId);
        if (config == null) {
            return adminMapper.saveConfig("website_template", templateCode, companyId);
        } else {
            return adminMapper.updateConfig("website_template", templateCode, companyId);
        }
    }

    /**
     * 构建模板路径
     * @param companyId 公司ID
     * @param viewName 视图名称
     * @return 完整的模板路径
     */
    public String buildTemplatePath(Integer companyId, String viewName) {
        String templateCode = getCurrentTemplate(companyId);
        return Constant.TEMPLATE_PREFIX + templateCode + "/" + viewName;
    }

    /**
     * 验证模板是否存在
     * @param templateCode 模板代码
     * @return 是否存在
     */
    public boolean isTemplateExists(String templateCode) {
        return Arrays.asList(Constant.AVAILABLE_TEMPLATES).contains(templateCode);
    }

    /**
     * 获取公司当前使用的模板信息
     * @param companyId 公司ID
     * @return 模板信息
     */
    public Template getCurrentTemplateInfo(Integer companyId) {
        String templateCode = getCurrentTemplate(companyId);
        return getTemplateByCode(templateCode);
    }
}
