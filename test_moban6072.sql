-- 测试新模板 moban6072 的SQL脚本
-- 请根据您的实际公司ID修改下面的companyId值

-- 查看当前所有公司
SELECT id, companyName, website FROM company;

-- 为公司ID=1设置新模板（请根据实际情况修改公司ID）
INSERT INTO config (`key`, `value`, companyId) VALUES ('website_template', 'moban6072', 1)
ON DUPLICATE KEY UPDATE `value` = 'moban6072';

-- 查看当前模板配置
SELECT * FROM config WHERE `key` = 'website_template';

-- 如果需要切换回默认模板，执行以下语句：
-- UPDATE config SET `value` = 'default' WHERE `key` = 'website_template' AND companyId = 1;

-- 如果需要测试其他模板，可以使用以下语句：
-- UPDATE config SET `value` = 'modern' WHERE `key` = 'website_template' AND companyId = 1;
-- UPDATE config SET `value` = 'business' WHERE `key` = 'website_template' AND companyId = 1;
-- UPDATE config SET `value` = 'creative' WHERE `key` = 'website_template' AND companyId = 1;
