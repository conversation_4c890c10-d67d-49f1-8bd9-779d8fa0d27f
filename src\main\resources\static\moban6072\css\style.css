
/*===================================================================
[Table Of Content]

    * Typography CSS
    * Common CSS
    * Main Navigation CSS
    * Header CSS
    * Home Slider CSS
    * About CSS
    * Service CSS
    * Project CSS
    * Team CSS
    * Form CSS
    * Blog CSS
    * Contact CSS
    * Footer CSS

=====================================================================*/
/*
-----------------------------------------------------------------------
  Typography CSS
-----------------------------------------------------------------------
*/
body {
  color: #656565;
  font-size: 1rem;
  font-family: "DM Sans", sans-serif;
  font-weight: 400;
  line-height: 1.75;
  margin: 0;
  overflow-x: hidden;
  -ms-word-wrap: break-word;
  word-wrap: break-word;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  color: #05103b;
  font-family: "DM Sans", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 10px;
  margin-top: 0;
}

a {
  color: #ff5e14;
  text-decoration: none;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
}
a:hover, a:active, a:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #c73f00;
  outline: none;
  text-decoration: none;
}
a img {
  border: none;
}

.btn:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

p {
  color: #777;
  margin-bottom: 30px;
}
p:last-child {
  margin-bottom: 0;
}

:active,
:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none !important;
}

::-moz-selection {
  background: #ff5e14;
  color: #fff;
  text-shadow: none;
}

::selection {
  background: #ff5e14;
  color: #fff;
  text-shadow: none;
}

::-moz-selection {
  background: #ff5e14;
  /* Firefox */
  color: #fff;
  text-shadow: none;
}

::-webkit-selection {
  background: #ff5e14;
  /* Safari */
  color: #fff;
  text-shadow: none;
}

img {
  height: auto;
  max-width: 100%;
}

iframe {
  border: none !important;
}

textarea:focus, textarea:active, input:focus, input:active {
  outline: none;
}

ul {
  margin: 0;
  padding: 0;
}
ul li {
  list-style: none;
}

pre,
ul,
ol,
dl,
dd,
blockquote,
address,
table,
fieldset {
  margin-bottom: 30px;
}

table p {
  margin-bottom: 0;
}

/*
-----------------------------------------------------------------------
  Common CSS
-----------------------------------------------------------------------
*/
.container {
  padding-left: 15px;
  padding-right: 15px;
}
@media only screen and (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}
@media only screen and (max-width: 1199px) {
  .container {
    max-width: 100%;
  }
}
.container.max-width-1700 {
  max-width: 1730px;
}

.row {
  margin-right: -15px;
  margin-left: -15px;
}
.row > [class*=col-] {
  padding-right: 15px;
  padding-left: 15px;
}

.section-title {
  margin-bottom: 68px;
}
@media only screen and (max-width: 1199px) {
  .section-title {
    margin-bottom: 58px;
  }
}
@media only screen and (max-width: 767px) {
  .section-title {
    margin-bottom: 48px;
  }
}
.section-title .subtitle {
  color: #ff5e14;
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 27px;
  letter-spacing: 1.7px;
  text-transform: uppercase;
}
@media only screen and (max-width: 1199px) {
  .section-title .subtitle {
    margin-bottom: 15px;
  }
}
.section-title .title {
  font-size: 50px;
  font-weight: 700;
}
@media only screen and (max-width: 1199px) {
  .section-title .title {
    font-size: 36px;
  }
}
@media only screen and (max-width: 767px) {
  .section-title .title {
    font-size: 30px;
  }
}
.section-title p {
  color: #656565;
}

.color-theme {
  color: #ff5e14 !important;
}

.bg-img {
  background: no-repeat center center;
  background-size: cover;
}
.bg-img.bg-img-top {
  background-position: top center;
}

.bg-img-cover {
  background-size: cover;
}

.site-wrapper-reveal {
  background-color: #FFFFFF;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.niceselect {
  float: inherit;
}
.niceselect .list {
  border-radius: 0;
  width: 100%;
}

.swiper-container {
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.no-border {
  border: none !important;
}

.z-index-1 {
  z-index: 1;
}

.z-index-2 {
  z-index: 2;
}

.content-items-center {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.justify-items-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.form-message.alert {
  margin-top: 10px;
}
.form-message.alert .btn-close {
  background-image: none;
}

.row-gutter-0 {
  margin: 0;
}
.row-gutter-0 [class*=col-] {
  margin: 0;
  padding: 0;
}

@media only screen and (min-width: 1200px) {
  .row-gutter-10 {
    margin-right: -5px;
    margin-left: -5px;
  }
  .row-gutter-10 [class*=col] {
    padding-left: 5px;
    padding-right: 5px;
  }
}

@media only screen and (min-width: 1200px) {
  .row-gutter-80 {
    margin-right: -40px;
    margin-left: -40px;
  }
  .row-gutter-80 [class*=col] {
    padding-left: 40px;
    padding-right: 40px;
  }
}

@media only screen and (min-width: 1200px) {
  .row-gutter-60 {
    margin-right: -30px;
    margin-left: -30px;
  }
  .row-gutter-60 [class*=col] {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media only screen and (min-width: 1200px) {
  .row-gutter-70 {
    margin-right: -35px;
    margin-left: -35px;
  }
  .row-gutter-70 [class*=col] {
    padding-left: 35px;
    padding-right: 35px;
  }
}

@media only screen and (min-width: 1200px) {
  .row-gutter-20 {
    margin-right: -10px;
    margin-left: -10px;
  }
  .row-gutter-20 [class*=col] {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media only screen and (min-width: 1200px) {
  .row-gutter-29 {
    margin-right: -14.5px;
    margin-left: -14.5px;
  }
  .row-gutter-29 [class*=col] {
    padding-left: 14.5px;
    padding-right: 14.5px;
  }
}

.wrapper {
  overflow-x: hidden;
}

/*-------- Button Style Css --------*/
.btn-theme {
  background-color: #ff5e14;
  border-color: #ff5e14;
  border-radius: 2px;
  color: #fff;
  display: inline-block;
  font-size: 17px;
  font-weight: 500;
  line-height: 1;
  padding: 21.5px 43.5px;
  position: relative;
  text-transform: capitalize;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
}
.btn-theme .icon {
  font-size: 23px;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 42px;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
}
.btn-theme .icon.icon-img {
  max-width: none;
  width: auto;
}
.btn-theme:hover, .btn-theme:focus {
  background-color: #05103b;
  border-color: #05103b;
  color: #fff;
}
.btn-theme:hover .icon, .btn-theme:focus .icon {
  right: 40px;
}
.btn-theme.btn-theme-color2 {
  background-color: #05103b;
  border-color: #05103b;
}
.btn-theme.btn-theme-color2:hover, .btn-theme.btn-theme-color2:focus {
  background-color: #ff5e14;
  border-color: #ff5e14;
}
.btn-theme.btn-black {
  background-color: #333;
  border-color: #333;
}
.btn-theme.btn-black:hover {
  background-color: #ff5e14;
  border-color: #ff5e14;
  color: #fff;
}
.btn-theme.btn-black.btn-border {
  background-color: transparent;
  border-color: #333;
  color: #333;
}
.btn-theme.btn-black.btn-border:hover {
  background-color: #333;
  border-color: #333;
  color: #fff;
}
.btn-theme.btn-white {
  background-color: #fff;
  border-color: #fff;
  color: #000;
}
.btn-theme.btn-white:hover {
  background-color: #000;
  border-color: #000;
  color: #fff;
}
.btn-theme.btn-white.btn-border {
  background-color: transparent;
  border-color: #fff;
  color: #fff;
}
.btn-theme.btn-white.btn-border:hover {
  background-color: #fff;
  border-color: #fff;
  color: #ff5e14;
}
.btn-theme.btn-gray {
  background-color: #f4f4f4;
  border-color: #f4f4f4;
  color: #000;
}
.btn-theme.btn-gray:hover {
  background-color: #ff5e14;
  border-color: #ff5e14;
  color: #fff;
}

.btn-link {
  color: #000;
  display: inline-block;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.9371;
  text-decoration: none;
  text-transform: uppercase;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
}
.btn-link:hover {
  color: #ff5e14;
}

/*-------- Page Title Style Css --------*/
.page-title-area {
  position: relative;
}
.page-title-area .page-title-content {
  padding: 222px 0 120px;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 991px) {
  .page-title-area .page-title-content {
    padding: 182px 0 80px;
  }
}
@media only screen and (max-width: 767px) {
  .page-title-area .page-title-content {
    padding: 165px 0 70px;
  }
}
@media only screen and (max-width: 575px) {
  .page-title-area .page-title-content {
    padding: 140px 0 55px;
  }
}
.page-title-area .page-title-content .title {
  color: #fff;
  font-size: 60px;
  margin-bottom: 5px;
}
@media only screen and (max-width: 991px) {
  .page-title-area .page-title-content .title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .page-title-area .page-title-content .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 575px) {
  .page-title-area .page-title-content .title {
    font-size: 24px;
  }
}
.page-title-area .page-title-content .bread-crumbs {
  margin-right: 3px;
}
.page-title-area .page-title-content .bread-crumbs a {
  color: #fff;
  font-size: 18px;
}
@media only screen and (max-width: 575px) {
  .page-title-area .page-title-content .bread-crumbs a {
    font-size: 15px;
  }
}
.page-title-area .page-title-content .bread-crumbs span {
  color: #fff;
  font-size: 18px;
}
@media only screen and (max-width: 575px) {
  .page-title-area .page-title-content .bread-crumbs span {
    font-size: 15px;
  }
}
.page-title-area .page-title-content .bread-crumbs .breadcrumb-sep {
  margin: 0 10px;
}
.page-title-area .page-title-content .bread-crumbs .active {
  color: #ff5e14;
}
.page-title-area .bg-overlay3 {
  background-color: #05103b;
  height: 100%;
  left: 0;
  opacity: 0.8;
  position: absolute;
  top: 0;
  width: 100%;
}

/*-------- Pagination Style Css --------*/
.pagination-area .page-numbers {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  margin-top: 10px;
  margin-bottom: 0;
}
.pagination-area .page-numbers li .page-number {
  background-color: #f5f5f5;
  border-radius: 2px;
  color: #05103b;
  display: inline-block;
  font-weight: 700;
  font-size: 16px;
  width: 58px;
  height: 58px;
  line-height: 58px;
  margin-right: 18px;
  text-align: center;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 991px) {
  .pagination-area .page-numbers li .page-number {
    font-size: 15px;
    height: 35px;
    line-height: 35px;
    margin-right: 12px;
    width: 35px;
  }
}
.pagination-area .page-numbers li .page-number:hover {
  background-color: #ff5e14;
  border-color: #ff5e14;
  color: #fff;
}
.pagination-area .page-numbers li .page-number:hover i {
  color: #fff;
}
.pagination-area .page-numbers li .page-number.active {
  background-color: #ff5e14;
  border-color: #ff5e14;
  color: #fff;
}
.pagination-area .page-numbers li .page-number i {
  color: #05103b;
  font-size: 20px;
  margin: 0;
  position: relative;
  top: 2px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 991px) {
  .pagination-area .page-numbers li .page-number i {
    font-size: 17px;
  }
}

/*-------- Error Style Css --------*/
.page-not-found-area {
  padding: 185px 0 135px;
}
@media only screen and (max-width: 1399px) {
  .page-not-found-area {
    padding: 123px 0 72px;
  }
}
@media only screen and (max-width: 991px) {
  .page-not-found-area {
    padding: 100px 0 80px;
  }
}
@media only screen and (max-width: 575px) {
  .page-not-found-area {
    padding: 70px 0 60px;
  }
}
.page-not-found-area .error-content {
  margin-bottom: 68px;
  padding-left: 70px;
  margin-top: 17px;
}
@media only screen and (max-width: 1399px) {
  .page-not-found-area .error-content {
    padding-left: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .page-not-found-area .error-content {
    padding-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  .page-not-found-area .error-content {
    text-align: center;
  }
}
.page-not-found-area .error-content h1 {
  color: #05103b;
  font-size: 192px;
  line-height: 1;
  margin-bottom: 8px;
}
@media only screen and (max-width: 767px) {
  .page-not-found-area .error-content h1 {
    font-size: 36px;
  }
}
.page-not-found-area .error-content h1 span {
  color: #ff5e14;
}
.page-not-found-area .error-content h4 {
  color: #05103b;
  font-size: 36px;
  margin-bottom: 21px;
}
@media only screen and (max-width: 1199px) {
  .page-not-found-area .error-content h4 {
    font-size: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .page-not-found-area .error-content h4 {
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .page-not-found-area .error-content h4 {
    font-size: 20px;
  }
}
.page-not-found-area .error-content p {
  color: #656565;
  font-size: 20px;
  line-height: 1.6;
  margin-bottom: 48px;
}
@media only screen and (max-width: 1399px) {
  .page-not-found-area .error-content p {
    margin-bottom: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .page-not-found-area .error-content p {
    font-size: 16px;
  }
}
.page-not-found-area .error-content img {
  margin-bottom: 51px;
}
@media only screen and (max-width: 1399px) {
  .page-not-found-area .error-content img {
    margin-bottom: 32px;
    width: 57%;
  }
}
.page-not-found-area .btn-theme {
  border-radius: 2px;
  font-weight: 700;
  font-size: 17px;
  padding: 21.5px 25.16px;
  text-transform: capitalize;
}
@media only screen and (max-width: 991px) {
  .page-not-found-area .btn-theme {
    font-size: 15px;
    padding: 17px 24px;
  }
}
@media only screen and (max-width: 575px) {
  .page-not-found-area .btn-theme {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    font-size: 13px;
    padding: 17px 19px;
  }
}
.page-not-found-area .btn-theme i {
  font-size: 19px;
  margin-right: 15px;
}
@media only screen and (max-width: 991px) {
  .page-not-found-area .btn-theme i {
    font-size: 15px;
  }
}
.page-not-found-area .error-thumb {
  margin-left: -99px;
  margin-right: -44px;
}
@media only screen and (max-width: 1399px) {
  .page-not-found-area .error-thumb {
    margin-left: 0;
    margin-right: 0;
    margin-top: 20px;
  }
}
.page-not-found-area .error-thumb img {
  border-radius: 2px;
}

/*-------- Coming Soon Style Css --------*/
.coming-soon-area {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #ff5e14;
  height: 100vh;
  margin-top: 0;
}
.coming-soon-area .container-fluid {
  padding: 0;
}
.coming-soon-area .coming-soon-content h1 {
  font-size: 42px;
  font-weight: 400;
  margin: 0;
}
.coming-soon-area .coming-soon-content h1 span {
  font-weight: 400;
  font-size: 160px;
  line-height: 1;
  color: #ff5e14;
}
@media only screen and (max-width: 767px) {
  .coming-soon-area .coming-soon-content h1 span {
    font-size: 100px;
  }
}
.coming-soon-area .coming-soon-content p {
  color: #444;
  font-weight: 400;
  line-height: 1.7;
  margin: 25px 0 18px;
}

.coming-soon-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100vh;
  min-height: 740px;
}
@media only screen and (max-width: 1199px) {
  .coming-soon-wrap {
    display: block;
  }
}
.coming-soon-wrap .coming-soon-content {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 4px solid #fff;
  min-height: 562.2px;
  min-width: 800px;
  margin-left: -90px;
  padding: 80px 60px 51px;
  text-align: center;
}
@media (max-width: 1599px) {
  .coming-soon-wrap .coming-soon-content {
    min-width: auto;
    padding: 80px 100px 51px;
  }
}
@media only screen and (max-width: 1199px) {
  .coming-soon-wrap .coming-soon-content {
    margin-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  .coming-soon-wrap .coming-soon-content {
    min-height: 360px;
    padding: 70px;
  }
}
@media only screen and (max-width: 575px) {
  .coming-soon-wrap .coming-soon-content {
    border: none;
  }
}
.coming-soon-wrap .coming-soon-content .title {
  color: #fff;
  font-size: 40px;
  font-weight: 700;
  letter-spacing: -0.8px;
  line-height: 1.3;
  text-align: center;
  position: relative;
  width: 300px;
  max-width: 300px;
  margin: 0 auto 50px;
}
@media only screen and (max-width: 767px) {
  .coming-soon-wrap .coming-soon-content .title {
    font-size: 30px;
  }
}
.coming-soon-wrap .coming-soon-content .title:after {
  top: 24px;
  content: "";
  position: absolute;
  width: 75px;
  border-top: 2px solid #fff;
  left: 120%;
}
@media only screen and (max-width: 767px) {
  .coming-soon-wrap .coming-soon-content .title:after {
    display: none;
  }
}
.coming-soon-wrap .coming-soon-content .title:before {
  bottom: 24px;
  content: "";
  position: absolute;
  width: 75px;
  border-top: 2px solid #fff;
  right: 120%;
}
@media only screen and (max-width: 767px) {
  .coming-soon-wrap .coming-soon-content .title:before {
    display: none;
  }
}
.coming-soon-wrap .coming-soon-content .countdown-content {
  margin-bottom: 48px;
}
@media only screen and (max-width: 575px) {
  .coming-soon-wrap .coming-soon-content .countdown-content {
    margin-bottom: 35px;
  }
}
.coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transform: none;
          transform: none;
  top: 0;
  left: 0;
  margin-bottom: 0;
  bottom: 0;
}
.coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer li {
  background-color: transparent;
  display: inline-block;
  margin: 0;
  padding: 0;
  text-align: center;
}
.coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer li + li {
  margin-left: 38px;
}
@media only screen and (max-width: 479.98px) {
  .coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer li + li {
    margin-left: 25px;
  }
}
.coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer li span {
  color: #fff;
  font-size: 60px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}
@media only screen and (max-width: 767px) {
  .coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer li span {
    font-size: 36px;
  }
}
@media only screen and (max-width: 479.98px) {
  .coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer li span {
    font-size: 24px;
  }
}
.coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer li p {
  color: #fff;
  display: block;
  font-size: 20px;
}
@media only screen and (max-width: 479.98px) {
  .coming-soon-wrap .coming-soon-content .countdown-content .countdown-timer li p {
    font-size: 16px;
  }
}
.coming-soon-wrap .coming-soon-content .btn-theme {
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  padding: 25px 46px;
  text-transform: uppercase;
  margin-bottom: 30px;
}
@media only screen and (max-width: 575px) {
  .coming-soon-wrap .coming-soon-content .btn-theme {
    font-size: 13px;
    padding: 11px 17px;
  }
}
.coming-soon-wrap .coming-soon-content .btn-theme:hover {
  background-color: #f4f4f4;
  color: #000;
}
.coming-soon-wrap .coming-soon-content .coming-social-icons a {
  color: #fff;
  font-size: 24px;
}
.coming-soon-wrap .coming-soon-content .coming-social-icons a + a {
  margin-left: 30px;
}
.coming-soon-wrap .coming-soon-content .coming-social-icons a:hover {
  color: #ff5e14;
}
.coming-soon-wrap .column-left {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100vh;
  min-height: 600px;
  width: 49.558%;
}
@media only screen and (max-width: 1199px) {
  .coming-soon-wrap .column-left {
    display: none;
  }
}
.coming-soon-wrap .column-right {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-position: center right;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100vh;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: 50.442%;
}
@media (max-width: 1599px) {
  .coming-soon-wrap .column-right {
    padding-right: 50px;
  }
}
@media only screen and (max-width: 1199px) {
  .coming-soon-wrap .column-right {
    height: 100%;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding-right: 0;
    width: 100%;
  }
}

/*-------- Custom Margin Padding Style Css --------*/
.mt-10 {
  margin-top: 10px;
}

.mt-12 {
  margin-top: 12px !important;
}

.mt-15 {
  margin-top: 15px;
}

.mt-18 {
  margin-top: 18px !important;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px !important;
}

.mt-35 {
  margin-top: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px !important;
}

.mt-55 {
  margin-top: 55px;
}

.mt-60 {
  margin-top: 60px !important;
}

.mt-65 {
  margin-top: 65px;
}

.mt-80 {
  margin-top: 80px !important;
}

.mt-70 {
  margin-top: 70px !important;
}

.mt-90 {
  margin-top: 90px !important;
}

.mt-100 {
  margin-top: 100px !important;
}

.mt-120 {
  margin-top: 120px !important;
}

@media only screen and (max-width: 1199px) {
  .mt-lg-50 {
    margin-top: 50px !important;
  }

  .mt-lg-70 {
    margin-top: 70px !important;
  }
}
@media only screen and (max-width: 991px) {
  .md-mt-0 {
    margin-top: 0 !important;
  }

  .mt-md-30 {
    margin-top: 30px !important;
  }

  .mt-md-40 {
    margin-top: 40px !important;
  }

  .mt-md-50 {
    margin-top: 50px !important;
  }

  .mt-md-60 {
    margin-top: 60px !important;
  }

  .mt-md-70 {
    margin-top: 70px !important;
  }
}
@media only screen and (max-width: 767px) {
  .sm-mt-0 {
    margin-top: 0 !important;
  }

  .mt-sm-25 {
    margin-top: 25px !important;
  }

  .mt-sm-30 {
    margin-top: 30px !important;
  }

  .mt-sm-50 {
    margin-top: 50px !important;
  }

  .mt-sm-60 {
    margin-top: 60px !important;
  }
}
@media only screen and (max-width: 575px) {
  .mt-xs-0 {
    margin-top: 0px;
  }

  .mt-xs-25 {
    margin-top: 25px !important;
  }

  .mt-xs-30 {
    margin-top: 30px !important;
  }
}
.mr-0 {
  margin-right: 0px !important;
}

.mr-10 {
  margin-right: 10px;
}

.mr-15 {
  margin-right: 15px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-25 {
  margin-right: 25px;
}

.mr-30 {
  margin-right: 30px;
}

.mr-50 {
  margin-right: 50px;
}

.mr-80 {
  margin-right: 80px;
}

.mr--100 {
  margin-right: -100px;
}

@media only screen and (max-width: 1199px) {
  .lg-mr-0 {
    margin-right: 0 !important;
  }
}
@media only screen and (max-width: 991px) {
  .md-mr-0 {
    margin-right: 0 !important;
  }
}
@media only screen and (max-width: 575px) {
  .mr-xs-0 {
    margin-right: 0;
  }

  .mr-xs-15 {
    margin-right: 15;
  }
}
.mb-10 {
  margin-bottom: 10px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mb-25 {
  margin-bottom: 25px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mb-35 {
  margin-bottom: 35px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.mb-45 {
  margin-bottom: 45px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mb-55 {
  margin-bottom: 55px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.mb-65 {
  margin-bottom: 65px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.mb-85 {
  margin-bottom: 85px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.mb-95 {
  margin-bottom: 95px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.mb-115 {
  margin-bottom: 115px !important;
}

.mb-120 {
  margin-bottom: 120px !important;
}

.mb-150 {
  margin-bottom: 150px !important;
}

.mb-438 {
  margin-bottom: 438px !important;
}

.mb-470 {
  margin-bottom: 470px !important;
}

.mb-471 {
  margin-bottom: 471px !important;
}

@media only screen and (max-width: 1199px) {
  .lg-mb-0, .mb-lg-0 {
    margin-bottom: 0 !important;
  }

  .mb-lg-30 {
    margin-bottom: 30px !important;
  }

  .mb-lg-40 {
    margin-bottom: 40px !important;
  }

  .mb-lg-50 {
    margin-bottom: 50px !important;
  }

  .mb-lg-70 {
    margin-bottom: 70px !important;
  }

  .mb-lg-80 {
    margin-bottom: 80px !important;
  }

  .mb-lg-90 {
    margin-bottom: 90px !important;
  }
}
@media only screen and (max-width: 991px) {
  .md-mb-0, .md-mb-0 {
    margin-bottom: 0px !important;
  }

  .mb-md-0 {
    margin-bottom: 0px !important;
  }

  .mb-md-10 {
    margin-bottom: 10px !important;
  }

  .mb-md-30 {
    margin-bottom: 30px !important;
  }

  .mb-md-40 {
    margin-bottom: 40px !important;
  }

  .mb-md-50 {
    margin-bottom: 50px !important;
  }

  .mb-md-60 {
    margin-bottom: 60px !important;
  }

  .mb-md-70 {
    margin-bottom: 70px !important;
  }

  .mb-md-80 {
    margin-bottom: 80px !important;
  }

  .mb-md-90 {
    margin-bottom: 90px !important;
  }
}
@media only screen and (max-width: 767px) {
  .mb-sm-0, .sm-mb-0 {
    margin-bottom: 0px !important;
  }

  .mb-sm-10 {
    margin-bottom: 10px !important;
  }

  .mb-sm-20 {
    margin-bottom: 20px !important;
  }

  .mb-sm-30 {
    margin-bottom: 30px !important;
  }

  .mb-sm-40 {
    margin-bottom: 40px !important;
  }

  .mb-sm-50 {
    margin-bottom: 50px !important;
  }

  .mb-sm-60 {
    margin-bottom: 60px !important;
  }

  .mb-sm-70 {
    margin-bottom: 70px !important;
  }
}
@media only screen and (max-width: 575px) {
  .mb-xs-0 {
    margin-bottom: 0 !important;
  }

  .mb-xs-30 {
    margin-bottom: 30px !important;
  }

  .mb-xs-50 {
    margin-bottom: 50px !important;
  }
}
@media only screen and (max-width: 479.98px) {
  .mb-xxs-0 {
    margin-bottom: 0 !important;
  }

  .mb-xxs-30 {
    margin-bottom: 30px !important;
  }
}
.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-25 {
  margin-left: 25px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-35 {
  margin-left: 35px !important;
}

.ml-40 {
  margin-left: 40px;
}

.ml-50 {
  margin-left: 50px !important;
}

@media only screen and (max-width: 1199px) {
  .ml-lg-0 {
    margin-left: 0 !important;
  }

  .lg-ml-0 {
    margin-left: 0 !important;
  }

  .ml-lg-20 {
    margin-left: 20px !important;
  }
}
@media only screen and (max-width: 991px) {
  .ml-md-0 {
    margin-left: 0 !important;
  }

  .md-ml-0 {
    margin-left: 0 !important;
  }
}
@media only screen and (max-width: 479.98px) {
  .ml-xxs-5 {
    margin-left: 5px !important;
  }
}
.pb-5 {
  padding-bottom: 5px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.pb-15 {
  padding-bottom: 15px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.pb-25 {
  padding-bottom: 25px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.pb-35 {
  padding-bottom: 35px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.pb-50 {
  padding-bottom: 50px !important;
}

.pb-55 {
  padding-bottom: 55px !important;
}

.pb-60 {
  padding-bottom: 60px !important;
}

.pb-70 {
  padding-bottom: 70px !important;
}

.pb-75 {
  padding-bottom: 75px !important;
}

.pb-80 {
  padding-bottom: 80px !important;
}

.pb-85 {
  padding-bottom: 85px !important;
}

.pb-90 {
  padding-bottom: 90px !important;
}

.pb-95 {
  padding-bottom: 95px !important;
}

.pb-100 {
  padding-bottom: 100px !important;
}

.pb-110 {
  padding-bottom: 110px !important;
}

.pb-115 {
  padding-bottom: 115px !important;
}

.pb-120 {
  padding-bottom: 120px !important;
}

.pb-125 {
  padding-bottom: 125px !important;
}

.pb-130 {
  padding-bottom: 130px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pb-170 {
  padding-bottom: 170px;
}

@media only screen and (max-width: 1199px) {
  .pb-lg-10 {
    padding-bottom: 10px !important;
  }

  .pb-lg-30 {
    padding-bottom: 30px !important;
  }

  .pb-lg-70 {
    padding-bottom: 70px !important;
  }

  .pb-lg-90 {
    padding-bottom: 90px !important;
  }
}
@media only screen and (max-width: 991px) {
  .md-pb-0 {
    padding-bottom: 0 !important;
  }

  .pb-md-10 {
    padding-bottom: 10px !important;
  }

  .pb-md-20 {
    padding-bottom: 20px !important;
  }

  .pb-md-30 {
    padding-bottom: 30px !important;
  }

  .pb-md-40 {
    padding-bottom: 40px !important;
  }

  .pb-md-50 {
    padding-bottom: 50px !important;
  }

  .pb-md-60 {
    padding-bottom: 60px !important;
  }

  .pb-md-70 {
    padding-bottom: 70px !important;
  }

  .pb-md-80 {
    padding-bottom: 80px !important;
  }

  .pb-md-90 {
    padding-bottom: 90px !important;
  }

  .pb-md-100 {
    padding-bottom: 100px !important;
  }

  .pb-md-120 {
    padding-bottom: 120px !important;
  }

  .pb-md-170 {
    padding-bottom: 170px !important;
  }
}
@media only screen and (max-width: 767px) {
  .sm-pb-0 {
    padding-bottom: 0 !important;
  }

  .pb-sm-20 {
    padding-bottom: 20px !important;
  }

  .pb-sm-30 {
    padding-bottom: 30px !important;
  }

  .pb-sm-50 {
    padding-bottom: 50px !important;
  }

  .pb-sm-60 {
    padding-bottom: 60px !important;
  }

  .pb-sm-70 {
    padding-bottom: 70px !important;
  }

  .pb-sm-80 {
    padding-bottom: 80px !important;
  }

  .pb-sm-100 {
    padding-bottom: 100px !important;
  }

  .pb-sm-170 {
    padding-bottom: 170px !important;
  }
}
@media only screen and (max-width: 575px) {
  .pb-xs-80 {
    padding-bottom: 80px !important;
  }
}
.pl-0 {
  padding-left: 0;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-33 {
  padding-left: 30px;
}

.pl-40 {
  padding-left: 40px;
}

.pl-50 {
  padding-left: 50px;
}

.pl-60 {
  padding-left: 60px;
}

.pl-70 {
  padding-left: 70px;
}

.pl-100 {
  padding-left: 100px;
}

.pl-130 {
  padding-left: 130px !important;
}

@media only screen and (max-width: 1199px) {
  .lg-pl-0 {
    padding-left: 0 !important;
  }

  .pl-lg-15 {
    padding-left: 15px !important;
  }
}
@media only screen and (max-width: 991px) {
  .md-pl-0 {
    padding-left: 0 !important;
  }

  .md-pl-15 {
    padding-left: 15px !important;
  }

  .pl-md-30 {
    padding-left: 30px !important;
  }

  .pl-md-50 {
    padding-left: 50px !important;
  }
}
@media only screen and (max-width: 767px) {
  .sm-pl-0 {
    padding-left: 0 !important;
  }

  .sm-pl-15, .pl-sm-15 {
    padding-left: 15px !important;
  }
}
@media only screen and (max-width: 575px) {
  .xs-pl-15 {
    padding-left: 15px !important;
  }
}
@media only screen and (max-width: 479.98px) {
  .pl-xxs-0 {
    padding-left: 0 !important;
  }
}
.plr-100 {
  padding: 0 100px !important;
}
@media only screen and (max-width: 1199px) {
  .plr-100 {
    padding: 0 !important;
  }
}

.pt-10 {
  padding-top: 10px !important;
}

.pt-15 {
  padding-top: 15px !important;
}

.pt-20 {
  padding-top: 20px !important;
}

.pt-30 {
  padding-top: 30px !important;
}

.pt-35 {
  padding-top: 35px !important;
}

.pt-40 {
  padding-top: 40px !important;
}

.pt-50 {
  padding-top: 50px !important;
}

.pt-55 {
  padding-top: 55px !important;
}

.pt-60 {
  padding-top: 60px !important;
}

.pt-70 {
  padding-top: 70px !important;
}

.pt-80 {
  padding-top: 80px !important;
}

.pt-90 {
  padding-top: 90px !important;
}

.pt-95 {
  padding-top: 95px !important;
}

.pt-100 {
  padding-top: 100px !important;
}

.pt-110 {
  padding-top: 110px !important;
}

.pt-120 {
  padding-top: 120px !important;
}

.pt-125 {
  padding-top: 125px !important;
}

.pt-130 {
  padding-top: 130px !important;
}

.pt-140 {
  padding-top: 140px !important;
}

.pt-150 {
  padding-top: 150px !important;
}

.pt-160 {
  padding-top: 160px !important;
}

.pt-165 {
  padding-top: 165px;
}

.pt-170 {
  padding-top: 170px;
}

@media only screen and (max-width: 1199px) {
  .lg-pt-0 {
    padding-top: 0 !important;
  }

  .pt-lg-40 {
    padding-top: 40px !important;
  }
}
@media only screen and (max-width: 991px) {
  .pt-md-10 {
    padding-top: 10px !important;
  }

  .pt-md-15 {
    padding-top: 15px !important;
  }

  .pt-md-30 {
    padding-top: 30px !important;
  }

  .pt-md-40 {
    padding-top: 40px !important;
  }

  .pt-md-50 {
    padding-top: 50px !important;
  }

  .pt-md-60 {
    padding-top: 60px !important;
  }

  .pt-md-65 {
    padding-top: 65px !important;
  }

  .pt-md-70 {
    padding-top: 70px !important;
  }

  .pt-md-80 {
    padding-top: 80px !important;
  }

  .pt-md-90 {
    padding-top: 90px !important;
  }

  .pt-md-100 {
    padding-top: 100px !important;
  }

  .pt-md-120 {
    padding-top: 120px !important;
  }
}
@media only screen and (max-width: 767px) {
  .pt-sm-10 {
    padding-top: 10px !important;
  }

  .pt-sm-30 {
    padding-top: 30px !important;
  }

  .pt-sm-50 {
    padding-top: 50px !important;
  }

  .pt-sm-60 {
    padding-top: 60px !important;
  }

  .pt-sm-70 {
    padding-top: 70px !important;
  }

  .pt-sm-80 {
    padding-top: 80px !important;
  }

  .pt-sm-90 {
    padding-top: 90px !important;
  }

  .pt-sm-100 {
    padding-top: 100px !important;
  }
}
@media only screen and (max-width: 575px) {
  .pt-xs-30 {
    padding-top: 30px !important;
  }

  .pt-xs-40 {
    padding-top: 40px !important;
  }
}
.pr-0 {
  padding-right: 0 !important;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-40 {
  padding-right: 40px;
}

.pr-50 {
  padding-right: 50px;
}

.pr-60 {
  padding-right: 60px;
}

.pr-70 {
  padding-right: 70px;
}

.pr-80 {
  padding-right: 80px;
}

.pr-90 {
  padding-right: 90px;
}

.pr-100 {
  padding-right: 100px;
}

@media only screen and (max-width: 1199px) {
  .lg-pr-0 {
    padding-right: 0 !important;
  }

  .pr-lg-15 {
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .sm-pr-0 {
    padding-right: 0 !important;
  }

  .sm-pr-15, .pr-sm-15 {
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 991px) {
  .md-pr-15 {
    padding-right: 15px;
  }

  .md-pl-15 {
    padding-left: 15px;
  }
}
.d-contents {
  display: contents;
}

.ml--2 {
  margin-left: -2px !important;
}

.ml-33 {
  margin-left: 33px;
}

.pt-38 {
  padding-top: 38px;
}

.pt-37 {
  padding-top: 37px;
}

/*-------- Scroll To Top Style Css --------*/
.scroll-to-top {
  background-color: #262626;
  border: none;
  border-radius: 50%;
  bottom: 53px;
  color: #fff;
  cursor: pointer;
  display: none;
  font-size: 22px;
  height: 50px;
  line-height: 50px;
  position: fixed;
  right: 50px;
  text-align: center;
  width: 50px;
  z-index: 100;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
}
@media only screen and (max-width: 575px) {
  .scroll-to-top {
    right: 30px;
    bottom: 15px;
  }
}
@media only screen and (max-width: 479.98px) {
  .scroll-to-top {
    bottom: 70px;
  }
}
.scroll-to-top:hover {
  background-color: #ff5e14;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #fff;
}

/*
-----------------------------------------------------------------------
	Main Navigation CSS
-----------------------------------------------------------------------
*/
.main-menu {
  -ms-flex-line-pack: center;
      align-content: center;
  font-family: "DM Sans", sans-serif;
}
.main-menu > li {
  margin-right: 14px;
  padding: 3px 0;
}
.main-menu > li:last-child {
  margin-right: 0;
}
.main-menu > li > a {
  color: #fcdcea;
  display: block;
  font-size: 12.5px;
  font-weight: 700;
  line-height: 22px;
  padding: 7px 25px;
  position: relative;
  text-transform: uppercase;
}
@media only screen and (max-width: 1399px) {
  .main-menu > li > a {
    margin: 0 18px;
  }
}
.main-menu > li:hover > a, .main-menu > li.active > a {
  color: #ff5e14;
}
.main-menu.menu-capitalize > li > a {
  font-size: 16px;
  text-transform: capitalize;
}

.has-submenu {
  padding-right: 10px;
  position: relative;
}
.has-submenu > a {
  position: relative;
}
.has-submenu > a:after {
  content: "";
  color: #fcdcea;
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  font-family: "FontAwesome";
  position: absolute;
  right: 11px;
  top: 7px;
}
.has-submenu:hover > .submenu-nav {
  opacity: 1;
  visibility: visible;
  margin: 0;
  margin-top: 39px;
}
@media only screen and (max-width: 1199px) {
  .has-submenu:hover > .submenu-nav {
    margin-top: 32px;
  }
}
.has-submenu:hover .menu-content .menu-content-inner h4, .has-submenu:hover .menu-content .menu-content-inner .btn-brand {
  -webkit-transform: none !important;
          transform: none !important;
}
.has-submenu .submenu-nav {
  -webkit-box-shadow: 0 1px 24px 0 rgba(0, 0, 0, 0.09);
          box-shadow: 0 1px 24px 0 rgba(0, 0, 0, 0.09);
  background-color: #333;
  color: #fff;
  display: block;
  margin-top: 80px;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  padding: 20px 0;
  transition: all 0.4s ease-in-out;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  width: 250px;
  z-index: 999;
}
.has-submenu .submenu-nav:before {
  content: "";
  position: absolute;
  height: 43px;
  width: 100%;
  left: 0;
  bottom: 100%;
}
@media only screen and (max-width: 1199px) {
  .has-submenu .submenu-nav:before {
    height: 37px;
  }
}
.has-submenu .submenu-nav > li {
  line-height: 1.8;
  position: relative;
}
.has-submenu .submenu-nav > li a {
  color: #fff;
  display: block;
  font-size: 14px;
  line-height: 1.8;
  padding: 7px 31px;
  position: relative;
}
.has-submenu .submenu-nav > li a:hover {
  color: #fff;
  text-decoration: underline;
}
.has-submenu .submenu-nav > li:hover > a {
  color: #fff;
  text-decoration: underline;
}
.has-submenu .submenu-nav > li:hover:after {
  color: #ff5e14 !important;
}
.has-submenu .submenu-nav > li.has-submenu {
  position: relative;
}
.has-submenu .submenu-nav > li.has-submenu a:before {
  display: block;
}
.has-submenu .submenu-nav > li.has-submenu:hover > .submenu-nav {
  -webkit-transform: none;
          transform: none;
  opacity: 1;
  visibility: visible;
  pointer-events: visible;
}
.has-submenu .submenu-nav > li.has-submenu:after {
  content: "";
  color: #656565;
  font-size: 15px;
  line-height: 1.2;
  font-family: "FontAwesome";
  position: absolute;
  right: 25px;
  top: 12px;
}
.has-submenu .submenu-nav > li.has-submenu .submenu-nav {
  left: 100%;
  top: 0;
  margin-top: -15px;
}
.has-submenu .submenu-nav-mega {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 25px 14px;
  left: 24%;
  max-width: 700px;
  width: 100%;
}
@media only screen and (max-width: 1199px) {
  .has-submenu .submenu-nav-mega {
    left: 0;
    width: 644px;
  }
}
.has-submenu .submenu-nav-mega .mega-menu-item {
  text-align: left;
  padding-right: 30px;
  padding-left: 0;
  float: left;
  display: block;
  padding-top: 0;
}
.has-submenu .submenu-nav-mega .mega-menu-item.banner-menu-content-wrap {
  width: 40%;
}
.has-submenu .submenu-nav-mega .mega-menu-item:last-child {
  border-right: 0;
}
.has-submenu .submenu-nav-mega .mega-menu-item ul li {
  position: relative;
  padding: 0;
}
.has-submenu .submenu-nav-mega .mega-menu-item ul li a:hover {
  color: #fff;
}
.has-submenu .submenu-nav-mega .mega-menu-item ul li.feature a {
  position: relative;
}
.has-submenu .submenu-nav-mega .mega-menu-item ul li.feature a:before {
  content: "";
  position: absolute;
  font-family: "FontAwesome";
  color: #ff5e14;
  right: -20px;
  top: 1px;
  font-size: 12px;
}
.has-submenu .submenu-nav-mega .mega-menu-item ul li.new a {
  position: relative;
}
.has-submenu .submenu-nav-mega .mega-menu-item ul li.new a:after {
  background-color: #ff5e14;
  content: "New";
  color: #fff;
  font-size: 13px;
  font-weight: 600;
  border-radius: 2px;
  position: absolute;
  top: 0;
  right: -38px;
  line-height: 1;
  padding: 2px 3px;
}
.has-submenu .submenu-nav-mega .mega-menu-item ul li:hover .menu-thumb {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  opacity: 1;
  visibility: visible;
}
.has-submenu .submenu-nav-mega .mega-menu-item ul li.active a {
  color: #fff;
}
.has-submenu .submenu-nav-mega .mega-menu-item.menu-content {
  padding: 0 !important;
  margin: -1px;
}
.has-submenu .submenu-nav-mega .mega-menu-item.menu-content .menu-content-inner {
  background-size: cover;
  background-position: center center;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  padding: 15px;
}
.has-submenu .submenu-nav-mega .mega-menu-item.menu-content .menu-content-inner h4, .has-submenu .submenu-nav-mega .mega-menu-item.menu-content .menu-content-inner .btn-brand {
  color: #fff;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  -webkit-transition-delay: 0.3s;
          transition-delay: 0.3s;
}
.has-submenu .submenu-nav-mega .mega-menu-item.menu-content .menu-content-inner h4 {
  -webkit-transform: translateY(-80%);
          transform: translateY(-80%);
}
.has-submenu .submenu-nav-mega .mega-menu-item.menu-content .menu-content-inner h4 span {
  color: #ff5e14;
}
.has-submenu .submenu-nav-mega .mega-menu-item.menu-content .menu-content-inner .btn-brand {
  margin-top: 30px;
  font-weight: 700;
  -webkit-transform: translateY(80%);
          transform: translateY(80%);
}
.has-submenu.full-width {
  position: static;
}
.has-submenu.colunm-two {
  position: relative;
}
.has-submenu.colunm-two .submenu-nav-mega {
  left: 100px !important;
  width: 550px;
}
.has-submenu.colunm-two .submenu-nav-mega .mega-menu-item {
  padding: 0 !important;
  -ms-flex-preferred-size: 50%;
      flex-basis: 50%;
}

/*
-----------------------------------------------------------------------
  Header CSS
-----------------------------------------------------------------------
*/
.header-area.header-default {
  padding: 20px 0;
  position: relative;
  z-index: 999;
}
@media only screen and (max-width: 575px) {
  .header-area.header-default {
    padding: 17px 0;
  }
}
.header-area.header-default .header-logo-area {
  max-width: 175px;
}
@media only screen and (max-width: 1199px) {
  .header-area.header-default .header-logo-area {
    max-width: 125px;
  }
}
@media only screen and (max-width: 479.98px) {
  .header-area.header-default .header-logo-area {
    margin-right: -15px;
  }
}
.header-area.header-default .header-navigation-area {
  padding-left: 28px;
}
.header-area.header-default .header-navigation-area .main-menu {
  margin-top: -1px;
  left: 0;
}
.header-area.header-default .header-navigation-area .main-menu > li {
  margin: 0;
  padding: 0;
}
.header-area.header-default .header-navigation-area .main-menu > li > a {
  color: #fff;
  font-weight: 500;
  font-size: 17px;
  padding: 0;
  margin: 0 25.63px;
  text-transform: capitalize;
}
@media only screen and (max-width: 1399px) {
  .header-area.header-default .header-navigation-area .main-menu > li > a {
    margin: 0 18px;
  }
}
@media only screen and (max-width: 1199px) {
  .header-area.header-default .header-navigation-area .main-menu > li > a {
    font-size: 15px;
    margin: 0 17px;
  }
}
.header-area.header-default .header-navigation-area .main-menu > li > a:hover {
  color: #ff5e14;
}
.header-area.header-default .header-navigation-area .main-menu > li.active > a {
  color: #ff5e14;
}
.header-area.header-default .header-navigation-area .main-menu > li.active > a:after {
  color: #ff5e14;
}
.header-area.header-default .header-navigation-area .main-menu .has-submenu > a:after {
  color: #8a8a8a;
  content: "";
  display: none;
  font-family: "FontAwesome";
  font-weight: 600;
  font-size: 14px;
  position: absolute;
  right: -20px;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.header-area.header-default .header-navigation-area .main-menu .has-submenu .submenu-nav > li a.srmenu-title {
  display: none;
}
.header-area.header-default .header-navigation-area .main-menu .has-submenu .submenu-nav > li.active > a {
  color: #ff5e14;
  text-decoration: underline;
}
.header-area.header-default .header-navigation-area .main-menu .has-submenu:hover > a {
  color: #ff5e14;
}
.header-area.header-default .header-navigation-area .main-menu .has-submenu:hover > a:after {
  color: #ff5e14;
}
.header-area.sticky-header.sticky {
  animation: 300ms ease-in-out 0s normal none 1 running fadeInDown;
  -webkit-animation: 300ms ease-in-out 0s normal none 1 running fadeInDown;
  background-color: #fff;
  -webkit-box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.06);
          box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.06);
  padding: 17px 0;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 999;
}
@media only screen and (max-width: 575px) {
  .header-area.sticky-header.sticky {
    padding: 15px 0;
  }
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu > li > a {
  color: #000;
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu > li > a:after {
  display: none;
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu > li > a:hover {
  color: #ff5e14;
  text-decoration: none;
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu > li > a:hover:after {
  color: #ff5e14;
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu .has-submenu .submenu-nav {
  margin-top: 36px;
}
@media only screen and (max-width: 1199px) {
  .header-area.sticky-header.sticky .header-navigation-area .main-menu .has-submenu .submenu-nav {
    margin-top: 29px;
  }
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu .has-submenu .submenu-nav:before {
  height: 40px;
}
@media only screen and (max-width: 1199px) {
  .header-area.sticky-header.sticky .header-navigation-area .main-menu .has-submenu .submenu-nav:before {
    height: 34px;
  }
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu .has-submenu.active > a {
  color: #ff5e14;
  text-decoration: none;
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu .has-submenu:hover > a {
  color: #ff5e14;
  text-decoration: none;
}
.header-area.sticky-header.sticky .header-navigation-area .main-menu .has-submenu:hover > a:after {
  color: #ff5e14;
}
.header-area.sticky-header.sticky .header-action-area .btn-menu {
  color: #414141;
}
.header-area.sticky-header.sticky .header-action-area .btn-menu:hover {
  background-color: #05103b;
  border-color: #05103b;
  color: #fff;
}
.header-area.sticky-header.sticky .header-action-area .btn-menu:hover span {
  background-color: #fff;
}
.header-area.sticky-header.sticky .header-action-area .header-action-btn .btn-theme:hover, .header-area.sticky-header.sticky .header-action-area .header-action-btn .btn-theme:active, .header-area.sticky-header.sticky .header-action-area .header-action-btn .btn-theme:focus {
  background-color: #05103b;
  border-color: #05103b;
  color: #fff;
}
.header-area.sticky-header.sticky .header-action-area .header-action-lang:after {
  color: #000;
}
.header-area.sticky-header.sticky .header-action-area .header-action-lang .info-dropdown {
  color: #000;
}
.header-area.sticky-header.sticky .header-action-area .header-lang-dropdown .btn-lang {
  color: #000;
}
.header-area.sticky-header.sticky .header-action-area .header-lang-dropdown .btn-lang:after {
  color: #000;
}
.header-area.sticky-header.sticky.header-default .header-logo-area {
  display: block;
}
.header-area.sticky-header.sticky.header-default .header-logo-area .logo-light {
  display: none;
}
.header-area.sticky-header.sticky.header-default .header-logo-area .logo-main {
  display: block;
}
.header-area.sticky-header.sticky.header-default .header-action-area {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 0;
}
.header-area.header-transparent {
  background-color: rgba(0, 0, 0, 0.45);
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 9;
}
.header-area .btn-menu {
  background-color: #ff5e14;
  border: 1px solid #ff5e14;
  border-radius: 2px;
  color: #fff;
  height: 46px;
  width: 52px;
  font-size: 9px;
  line-height: 1;
  margin-left: 15px;
  padding-bottom: 2px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 479.98px) {
  .header-area .btn-menu {
    height: 38px;
    margin-left: 8px;
    width: 44px;
  }
}
@media only screen and (max-width: 359.98px) {
  .header-area .btn-menu {
    height: 38px;
    margin-left: 5px;
    width: 39px;
  }
}
.header-area .btn-menu span {
  background-color: #fff;
  display: inline-block;
  height: 2.5px;
  line-height: 1;
  margin-bottom: 1px;
  width: 29px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 359.98px) {
  .header-area .btn-menu span {
    width: 22px;
  }
}
.header-area .btn-menu:hover {
  background-color: #fff;
  border-color: #fff;
  color: #ff5e14;
}
.header-area .btn-menu:hover span {
  background-color: #ff5e14;
}

.header-action-area {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  line-height: 1;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.header-action-area .header-lang-dropdown {
  line-height: 1;
}
.header-action-area .header-lang-dropdown .btn-lang {
  background-color: transparent;
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-size: 17px;
  font-weight: 500;
  margin-right: 0;
  min-width: 50px;
  padding-left: 5px;
  padding-right: 24px;
  position: relative;
  text-transform: uppercase;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 1199px) {
  .header-action-area .header-lang-dropdown .btn-lang {
    font-size: 15px;
    padding-right: 12px;
  }
}
.header-action-area .header-lang-dropdown .btn-lang:hover, .header-action-area .header-lang-dropdown .btn-lang:active, .header-action-area .header-lang-dropdown .btn-lang:focus {
  background-color: transparent;
  color: #fff;
}
.header-action-area .header-lang-dropdown .btn-lang.dropdown-toggle:after {
  border: none;
  content: "";
  color: #fff;
  font-size: 17px;
  font-weight: 500;
  line-height: 1;
  font-family: "IcoFont";
  position: absolute;
  right: 1px;
  top: 1px;
  z-index: -1;
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 1199px) {
  .header-action-area .header-lang-dropdown .btn-lang.dropdown-toggle:after {
    top: -1px;
  }
}
.header-action-area .header-lang-dropdown .dropdown-menu {
  background-color: #fff;
  border-radius: 3px;
  border: none;
  -webkit-box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.3);
          box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.3);
  margin: 0;
  min-width: 60px;
  max-width: 60px;
  overflow: hidden;
  padding: 0;
  left: -6px !important;
  top: 11px !important;
}
@media only screen and (max-width: 1199px) {
  .header-action-area .header-lang-dropdown .dropdown-menu {
    left: 1px !important;
  }
}
.header-action-area .header-lang-dropdown .dropdown-menu .dropdown-item {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  font-size: 16px;
  padding: 6px 10px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 1199px) {
  .header-action-area .header-lang-dropdown .dropdown-menu .dropdown-item {
    font-size: 15px;
  }
}
.header-action-area .header-lang-dropdown .dropdown-menu .dropdown-item.active, .header-action-area .header-lang-dropdown .dropdown-menu .dropdown-item:active, .header-action-area .header-lang-dropdown .dropdown-menu .dropdown-item:hover {
  color: #ff5e14;
  background-color: transparent;
}
.header-action-area .header-action-lang {
  cursor: pointer;
  line-height: 1;
  margin-left: 24px;
  position: relative;
}
@media only screen and (max-width: 767px) {
  .header-action-area .header-action-lang {
    margin-left: 0;
  }
}
.header-action-area .header-action-lang:after {
  content: "";
  color: #fff;
  font-size: 17px;
  font-weight: 500;
  line-height: 1;
  font-family: "IcoFont";
  position: absolute;
  right: 1px;
  top: calc(50% - 8px);
  z-index: -1;
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
}
@media only screen and (max-width: 1199px) {
  .header-action-area .header-action-lang:after {
    font-size: 15px;
  }
}
.header-action-area .header-action-lang .info-dropdown {
  appearance: none;
  background-color: transparent;
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-size: 17px;
  font-weight: 500;
  min-width: 46px;
  padding-right: 5px;
  text-transform: uppercase;
  -webkit-appearance: none;
  -moz-appearance: none;
}
@media only screen and (max-width: 1199px) {
  .header-action-area .header-action-lang .info-dropdown {
    font-size: 15px;
    min-width: 42px;
  }
}
.header-action-area .header-action-lang .info-dropdown option {
  color: #333;
  cursor: pointer;
  font-size: 15px;
}
.header-action-area .header-action-btn {
  margin-left: 24px;
  position: relative;
}
@media only screen and (max-width: 1199px) {
  .header-action-area .header-action-btn {
    margin-left: 18px;
  }
}
@media only screen and (max-width: 479.98px) {
  .header-action-area .header-action-btn {
    margin-left: 12px;
  }
}
@media only screen and (max-width: 359.98px) {
  .header-action-area .header-action-btn {
    margin-left: 3px;
  }
}
.header-action-area .header-action-btn .btn-theme {
  border: none;
  border-radius: 2px;
  line-height: 1;
  font-size: 17px;
  padding: 21.5px 35.14px;
  text-transform: capitalize;
}
@media only screen and (max-width: 1199px) {
  .header-action-area .header-action-btn .btn-theme {
    font-size: 15px;
    padding: 15.5px 22px;
  }
}
@media only screen and (max-width: 767px) {
  .header-action-area .header-action-btn .btn-theme {
    padding: 15.5px 17px;
  }
}
@media only screen and (max-width: 479.98px) {
  .header-action-area .header-action-btn .btn-theme {
    font-size: 13px;
    padding: 12.5px 9px;
  }
}
.header-action-area .header-action-btn .btn-theme:hover, .header-action-area .header-action-btn .btn-theme:active, .header-action-area .header-action-btn .btn-theme:focus {
  background-color: #fff;
  border-color: #fff;
  color: #ff5e14;
}

.header-logo-area {
  max-width: 120px;
  position: relative;
}
.header-logo-area .logo-main {
  display: none;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}

.off-canvas-area {
  padding: 0 50px;
}
@media only screen and (max-width: 575px) {
  .off-canvas-area {
    padding: 0 30px;
  }
}
@media only screen and (max-width: 479.98px) {
  .off-canvas-area.offcanvas-end {
    width: 310px;
  }
}
.off-canvas-area .offcanvas-header {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  padding: 50px 0 30px;
}
@media only screen and (max-width: 575px) {
  .off-canvas-area .offcanvas-header {
    padding: 30px 0 15px;
  }
}
.off-canvas-area .offcanvas-header .btn-close {
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #0A1039;
  font-size: 16px;
  margin: 0;
  opacity: 1;
  padding: 0;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 575px) {
  .off-canvas-area .offcanvas-header .btn-close {
    font-size: 14px;
  }
}
.off-canvas-area .offcanvas-header .btn-close:hover {
  color: #ff5e14;
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
}
.off-canvas-area .offcanvas-body {
  padding: 0 0 20px;
}

.offcanvas-menu {
  position: relative;
  z-index: 1;
}

.offcanvas-menu::before {
  position: absolute;
  z-index: -1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  content: "";
  background-color: #fff;
}

.offcanvas-menu ul ul {
  display: none;
}

.offcanvas-menu > ul > li > ul li {
  border-bottom: 0px;
}
.offcanvas-menu > ul > li > ul li a {
  padding: 3px 15px;
  font-weight: 400;
  font-size: 14px;
  color: #656565;
}
.offcanvas-menu > ul > li > ul li:first-child a {
  padding-top: 0;
}

.offcanvas-menu li.active > ul {
  display: block;
}

.offcanvas-menu li a {
  display: block;
  text-transform: capitalize;
  color: #05103b;
  padding: 10px 0px;
  position: relative;
  font-size: 17px;
}
@media only screen and (max-width: 479.98px) {
  .offcanvas-menu li a {
    font-size: 15px;
  }
}
.offcanvas-menu li a:hover {
  color: #ff5e14;
}

.offcanvas-menu a:not(:only-child):after {
  content: "";
  font-family: "IcoFont";
  position: absolute;
  right: 2px;
  top: 13px;
}

.offcanvas-menu .active > a:not(:only-child):after {
  content: "";
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/*
-----------------------------------------------------------------------
    Home Slider CSS
-----------------------------------------------------------------------
*/
.home-slider-area.slider-default {
  position: relative;
}
.home-slider-area.slider-default .home-slider-content .swiper-container .swiper-wrapper .swiper-slide-active .slider-content-area .content .inner-content .wrap-one, .home-slider-area.slider-default .home-slider-content .swiper-container .swiper-wrapper .swiper-slide-active .slider-content-area .content .inner-content .wrap-two, .home-slider-area.slider-default .home-slider-content .swiper-container .swiper-wrapper .swiper-slide-active .slider-content-area .content .inner-content .wrap-three {
  opacity: 1;
  transform: translate(0px, 0px);
  -webkit-transform: translate(0px, 0px);
  -moz-transform: translate(0px, 0px);
  -ms-transform: translate(0px, 0px);
  -o-transform: translate(0px, 0px);
}
.home-slider-area.slider-default .home-slider-content .swiper-container .swiper-wrapper .swiper-slide-active .slider-content-area .thumb .bg-thumb {
  -webkit-animation-delay: 0.15s;
  animation-delay: 0.15s;
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}
.home-slider-area.slider-default .home-slider-content .swiper-container .swiper-wrapper .swiper-slide-active .slider-content-area .bg-logo {
  -webkit-animation-delay: 0.7s;
  animation-delay: 0.7s;
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}
.home-slider-area.slider-default .home-slider-item {
  overflow: hidden;
  position: relative;
  padding-bottom: 100px;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .home-slider-area.slider-default .home-slider-item {
    padding-bottom: 0;
  }
}
.home-slider-area.slider-default .home-slider-item .bg-logo {
  position: absolute;
  pointer-events: none;
  top: calc(50% + 29px);
  left: calc(50% - 40px);
  width: 396px;
  height: 399px;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  z-index: 1;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}
@media only screen and (max-width: 1399px) {
  .home-slider-area.slider-default .home-slider-item .bg-logo {
    height: 300px;
    left: calc(50% - 70px);
    width: 300px;
  }
}
@media only screen and (max-width: 1199px) {
  .home-slider-area.slider-default .home-slider-item .bg-logo {
    height: 250px;
    left: calc(50% - 100px);
    width: 250px;
  }
}
@media only screen and (max-width: 991px) {
  .home-slider-area.slider-default .home-slider-item .bg-logo {
    height: 200px;
    left: calc(50% - 30px);
    width: 200px;
  }
}
@media only screen and (max-width: 767px) {
  .home-slider-area.slider-default .home-slider-item .bg-logo {
    display: none;
  }
}
.home-slider-area.slider-default .home-slider-item .bg-overlay {
  background-color: #05103b;
  content: "";
  height: 100%;
  left: 0;
  opacity: 0.5;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
}
.home-slider-area.slider-default .slider-content-area {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  height: 840px;
  z-index: 1;
}
@media only screen and (max-width: 1199px) {
  .home-slider-area.slider-default .slider-content-area {
    height: 720px;
  }
}
@media only screen and (max-width: 991px) {
  .home-slider-area.slider-default .slider-content-area {
    height: 680px;
  }
}
@media only screen and (max-width: 767px) {
  .home-slider-area.slider-default .slider-content-area {
    height: auto;
    padding-bottom: 15px;
  }
}
.home-slider-area.slider-default .slider-content-area .content {
  margin-top: 53px;
}
.home-slider-area.slider-default .slider-content-area .content .inner-content {
  margin-left: -1px;
  position: relative;
}
@media only screen and (max-width: 767px) {
  .home-slider-area.slider-default .slider-content-area .content .inner-content {
    margin: 0 auto;
    max-width: 540px;
    padding: 120px 0 100px;
    text-align: center;
  }
}
.home-slider-area.slider-default .slider-content-area .content .inner-content h2 {
  color: #fff;
  font-size: 70px;
  font-weight: 700;
  line-height: 75px;
  margin-bottom: 19px;
}
@media only screen and (max-width: 1199px) {
  .home-slider-area.slider-default .slider-content-area .content .inner-content h2 {
    font-size: 48px;
    line-height: 1.2;
  }
}
@media only screen and (max-width: 767px) {
  .home-slider-area.slider-default .slider-content-area .content .inner-content h2 {
    font-size: 36px;
  }
}
.home-slider-area.slider-default .slider-content-area .content .inner-content p {
  color: #fff;
  font-size: 20px;
  line-height: 1.75;
  margin-bottom: 32px;
}
@media only screen and (max-width: 1199px) {
  .home-slider-area.slider-default .slider-content-area .content .inner-content p {
    font-size: 17px;
  }
}
@media only screen and (max-width: 767px) {
  .home-slider-area.slider-default .slider-content-area .content .inner-content p {
    font-size: 15px;
  }
}
.home-slider-area.slider-default .slider-content-area .content .inner-content .btn-theme {
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -ms-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
}
@media only screen and (max-width: 1199px) {
  .home-slider-area.slider-default .slider-content-area .content .inner-content .btn-theme {
    padding: 17px 23px;
  }
}
.home-slider-area.slider-default .slider-content-area .content .inner-content .btn-theme:hover {
  background-color: #fff;
  border-color: #fff;
  color: #ff5e14;
}
.home-slider-area.slider-default .slider-content-area .content .inner-content .wrap-one, .home-slider-area.slider-default .slider-content-area .content .inner-content .wrap-two, .home-slider-area.slider-default .slider-content-area .content .inner-content .wrap-three {
  opacity: 0;
  transition: all 0.6s ease-out;
  -webkit-transition: all 0.6s ease-out;
  -moz-transition: all 0.6s ease-out;
  -ms-transition: all 0.6s ease-out;
  -o-transition: all 0.6s ease-out;
}
.home-slider-area.slider-default .slider-content-area .content .inner-content .wrap-one {
  transform: translate(0px, 35px);
  -webkit-transform: translate(0px, 35px);
  -moz-transform: translate(0px, 35px);
  -ms-transform: translate(0px, 35px);
  -o-transform: translate(0px, 35px);
  -webkit-transition-delay: 0.5s;
          transition-delay: 0.5s;
}
.home-slider-area.slider-default .slider-content-area .content .inner-content .wrap-two {
  transform: translate(0px, 30px);
  -webkit-transform: translate(0px, 30px);
  -moz-transform: translate(0px, 30px);
  -ms-transform: translate(0px, 30px);
  -o-transform: translate(0px, 30px);
  -webkit-transition-delay: 1s;
          transition-delay: 1s;
}
.home-slider-area.slider-default .slider-content-area .content .inner-content .wrap-three {
  transform: translate(0px, 25px);
  -webkit-transform: translate(0px, 25px);
  -moz-transform: translate(0px, 25px);
  -ms-transform: translate(0px, 25px);
  -o-transform: translate(0px, 25px);
  -webkit-transition-delay: 1.5s;
          transition-delay: 1.5s;
}
.home-slider-area.slider-default .slider-content-area .thumb .bg-thumb {
  height: 100%;
  position: absolute;
  right: 0;
  width: calc(50% - 102px);
  top: 100px;
  z-index: 1;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}
@media only screen and (max-width: 1199px) {
  .home-slider-area.slider-default .slider-content-area .thumb .bg-thumb {
    top: 85px;
    width: calc(55% - 102px);
  }
}
@media only screen and (max-width: 767px) {
  .home-slider-area.slider-default .slider-content-area .thumb .bg-thumb {
    border-radius: 2px;
    height: 600px;
    position: relative;
    top: auto;
    width: 100%;
  }
}
@media only screen and (max-width: 479.98px) {
  .home-slider-area.slider-default .slider-content-area .thumb .bg-thumb {
    height: 480px;
  }
}

@-webkit-keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
/*
-----------------------------------------------------------------------
	About CSS
-----------------------------------------------------------------------
*/
.about-area.about-default-area {
  padding: 140px 0 136px;
}
@media only screen and (max-width: 1199px) {
  .about-area.about-default-area {
    padding: 85px 0 100px;
  }
}
@media only screen and (max-width: 767px) {
  .about-area.about-default-area {
    padding: 77px 0 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area.about-default-area .container {
    max-width: 960px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area.about-default-area .container {
    max-width: 720px;
  }
}
@media only screen and (max-width: 767px) {
  .about-area.about-default-area .container {
    max-width: none;
  }
}
.about-area.about-style1-area {
  padding: 160px 0 143px;
}
@media only screen and (max-width: 1199px) {
  .about-area.about-style1-area {
    padding: 120px 0 100px;
  }
}
@media only screen and (max-width: 767px) {
  .about-area.about-style1-area {
    padding: 95px 0 10px;
  }
}
@media only screen and (max-width: 1199px) {
  .about-area.about-style1-area .container {
    max-width: 960px;
  }
}
@media only screen and (max-width: 991px) {
  .about-area.about-style1-area .container {
    max-width: 720px;
  }
}
@media only screen and (max-width: 767px) {
  .about-area.about-style1-area .container {
    max-width: none;
  }
}

.about-content {
  margin-top: 30px;
}
@media only screen and (max-width: 1199px) {
  .about-content {
    margin-top: 0;
  }
}
@media only screen and (max-width: 991px) {
  .about-content {
    margin-top: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .about-content {
    margin-top: 0;
  }
}
.about-content .section-title {
  margin-bottom: 0;
}
@media only screen and (max-width: 767px) {
  .about-content .section-title {
    margin-bottom: 70px;
  }
}
.about-content .section-title .subtitle {
  margin-bottom: 20px;
}
@media only screen and (max-width: 1199px) {
  .about-content .section-title .subtitle {
    margin-bottom: 12px;
  }
}
.about-content .section-title .title {
  font-size: 35px;
  line-height: 1.2;
  margin-bottom: 21px;
  margin-right: -10px;
}
@media only screen and (max-width: 1199px) {
  .about-content .section-title .title {
    font-size: 24px;
  }
}
@media only screen and (max-width: 991px) {
  .about-content .section-title .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .about-content .section-title .title {
    font-size: 24px;
  }
}
@media only screen and (max-width: 479.98px) {
  .about-content .section-title .title {
    font-size: 24px;
  }
}
.about-content .section-title p {
  margin-bottom: 13px;
}
@media only screen and (max-width: 1199px) {
  .about-content .section-title p {
    font-size: 14px;
  }
}
@media only screen and (max-width: 991px) {
  .about-content .section-title p {
    font-size: 15px;
  }
}
.about-content .section-title .btn-theme {
  padding: 21.5px 33.15px;
  margin-top: 21px;
}
@media only screen and (max-width: 1199px) {
  .about-content .section-title .btn-theme {
    font-size: 15px;
    margin-top: 8px;
    padding: 17px 23px;
  }
}
@media only screen and (max-width: 991px) {
  .about-content .section-title .btn-theme {
    margin-top: 20px;
  }
}

.about-thumb {
  margin-left: 43px;
  margin-right: -3px;
}
@media only screen and (max-width: 1199px) {
  .about-thumb {
    margin-left: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .about-thumb {
    margin-left: 0;
    margin-right: 0;
  }
}
.about-thumb img {
  width: 100%;
}

.about-content-style1 .about-thumb {
  margin-left: 20px;
  margin-right: 50px;
  position: relative;
}
@media only screen and (max-width: 991px) {
  .about-content-style1 .about-thumb {
    margin: 0;
  }
}
@media only screen and (max-width: 767px) {
  .about-content-style1 .about-thumb {
    margin-bottom: 40px;
    margin-left: 15px;
  }
}
.about-content-style1 .about-thumb img {
  border-radius: 2px;
  clip-path: polygon(0% 0%, 100% 12%, 100% 0%, 100% 100%, 0% 88%);
  -webkit-clip-path: polygon(0% 0%, 100% 12%, 100% 0%, 100% 100%, 0% 88%);
}
@media only screen and (max-width: 767px) {
  .about-content-style1 .about-thumb img {
    clip-path: none;
    -webkit-clip-path: none;
  }
}
.about-content-style1 .about-thumb .about-thumb-shape {
  background-color: #ff5e14;
  border-radius: 2px;
  content: "";
  height: 100%;
  left: -20px;
  position: absolute;
  top: -20px;
  width: 100%;
  z-index: -1;
  clip-path: polygon(0% 0%, 100% 12%, 100% 0%, 100% 100%, 0% 88%);
  -webkit-clip-path: polygon(0% 0%, 100% 12%, 100% 0%, 100% 100%, 0% 88%);
}
@media only screen and (max-width: 767px) {
  .about-content-style1 .about-thumb .about-thumb-shape {
    left: -15px;
    top: -15px;
    clip-path: none;
    -webkit-clip-path: none;
  }
}
.about-content-style1 .about-content {
  margin-top: 32px;
}
.about-content-style1 .about-content .section-title .subtitle {
  font-size: 18px;
  margin-bottom: 19px;
}
.about-content-style1 .about-content .section-title .title {
  font-size: 45px;
  line-height: 1.0667;
  margin-bottom: 23px;
}
@media only screen and (max-width: 1199px) {
  .about-content-style1 .about-content .section-title .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .about-content-style1 .about-content .section-title .title {
    font-size: 30px;
  }
}
.about-content-style1 .about-content .section-title p {
  margin-bottom: 17px;
}
.about-content-style1 .about-content .section-title .btn-theme {
  margin-top: 17px;
}

/*
-----------------------------------------------------------------------
    Team CSS
-----------------------------------------------------------------------
*/
.team-area.team-default-area {
  padding: 137px 0 140px;
}
@media only screen and (max-width: 1199px) {
  .team-area.team-default-area {
    padding: 97px 0 100px;
  }
}
@media only screen and (max-width: 767px) {
  .team-area.team-default-area {
    padding: 77px 0 80px;
  }
}
.team-area.team-default-area .section-title {
  margin-bottom: 79px;
}
@media only screen and (max-width: 1199px) {
  .team-area.team-default-area .section-title {
    margin-bottom: 59px;
  }
}
@media only screen and (max-width: 767px) {
  .team-area.team-default-area .section-title {
    margin-bottom: 49px;
  }
}
.team-area.team-style1-area {
  padding: 137px 0;
}
@media only screen and (max-width: 1199px) {
  .team-area.team-style1-area {
    padding: 97px 0;
  }
}
@media only screen and (max-width: 767px) {
  .team-area.team-style1-area {
    padding: 77px 0;
  }
}

.team-member {
  background-color: #f5f5f5;
  border-radius: 2px;
  margin-right: 39px;
  padding: 0 0 40px;
}
@media only screen and (max-width: 1199px) {
  .team-member {
    padding: 0 0 30px;
  }
}
@media only screen and (max-width: 991px) {
  .team-member {
    margin-right: 17px;
    padding-bottom: 15px;
  }
}
@media only screen and (max-width: 575px) {
  .team-member {
    margin-right: 18px;
  }
}
.team-member .thumb {
  margin-bottom: 30px;
  padding: 35px 35px 0;
  text-align: center;
}
@media only screen and (max-width: 1199px) {
  .team-member .thumb {
    margin-bottom: 25px;
    padding: 25px 25px 0;
  }
}
@media only screen and (max-width: 991px) {
  .team-member .thumb {
    margin-bottom: 15px;
    padding: 15px 15px 0;
  }
}
.team-member .thumb img {
  border-radius: 2px;
  width: 100%;
}
.team-member .content {
  background-color: #05103b;
  border-radius: 2px;
  left: 35px;
  overflow: hidden;
  position: relative;
  width: calc(100% + 3px);
  clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 84% 100%, 0% 100%);
  -webkit-clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 84% 100%, 0% 100%);
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 1199px) {
  .team-member .content {
    left: 25px;
  }
}
@media only screen and (max-width: 991px) {
  .team-member .content {
    left: 15px;
  }
}
.team-member .content:before {
  background-color: #fff;
  border-radius: 2px;
  content: "";
  height: 100%;
  position: absolute;
  right: 4px;
  top: 0;
  width: 100%;
  clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 84% 100%, 0% 100%);
  -webkit-clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 84% 100%, 0% 100%);
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.team-member .content:after {
  background-color: #05103b;
  border-radius: 2px;
  content: "";
  height: 100%;
  position: absolute;
  right: 8px;
  top: 0;
  width: 100%;
  clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 84% 100%, 0% 100%);
  -webkit-clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 84% 100%, 0% 100%);
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.team-member .content .member-info {
  padding: 17px 23px 11px;
  position: relative;
  z-index: 3;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 1199px) {
  .team-member .content .member-info {
    padding: 17px 15px 11px;
  }
}
@media only screen and (max-width: 991px) {
  .team-member .content .member-info {
    padding: 15px 12px 9px;
  }
}
.team-member .content .member-info .name {
  color: #fff;
  font-size: 23px;
  margin-bottom: 7px;
}
@media only screen and (max-width: 1199px) {
  .team-member .content .member-info .name {
    font-size: 18px;
  }
}
@media only screen and (max-width: 991px) {
  .team-member .content .member-info .name {
    font-size: 15px;
    margin-bottom: 5px;
  }
}
.team-member .content .member-info .designation {
  color: #ff5e14;
  font-weight: 400;
  font-size: 15px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 1199px) {
  .team-member .content .member-info .designation {
    font-size: 13px;
  }
}
@media only screen and (max-width: 991px) {
  .team-member .content .member-info .designation {
    font-size: 12px;
  }
}
.team-member:hover .content {
  background-color: #ff5e14;
}
.team-member:hover .content:before {
  background-color: #fff;
}
.team-member:hover .content:after {
  background-color: #ff5e14;
}
.team-member:hover .member-info .designation {
  color: #fff;
}

/*
-----------------------------------------------------------------------
  Testimonial CSS
-----------------------------------------------------------------------
*/
.testimonial-area.testimonial-default-area {
  padding: 137px 0 130px;
}
@media only screen and (max-width: 1199px) {
  .testimonial-area.testimonial-default-area {
    padding: 97px 0 90px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-area.testimonial-default-area {
    padding: 77px 0 70px;
  }
}
.testimonial-area.testimonial-default-area .section-title {
  margin-bottom: 69px;
}
@media only screen and (max-width: 1199px) {
  .testimonial-area.testimonial-default-area .section-title {
    margin-bottom: 59px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-area.testimonial-default-area .section-title {
    margin-bottom: 43px;
  }
}
.testimonial-area.testimonial-style1-area {
  padding: 0 0 130px;
}
@media only screen and (max-width: 1199px) {
  .testimonial-area.testimonial-style1-area {
    padding: 0 0 90px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-area.testimonial-style1-area {
    padding: 0 0 70px;
  }
}

.testimonial-item {
  background-color: #f5f5f5;
  border-radius: 2px;
  padding: 60px 55px 56px 54px;
}
@media only screen and (max-width: 991px) {
  .testimonial-item {
    padding: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-item {
    padding: 20px 15px;
  }
}
@media only screen and (max-width: 575px) {
  .testimonial-item {
    text-align: center;
    padding: 30px 15px;
  }
}
.testimonial-item .content {
  position: relative;
  margin-bottom: 32px;
}
@media only screen and (max-width: 767px) {
  .testimonial-item .content {
    margin-bottom: 18px;
  }
}
@media only screen and (max-width: 575px) {
  .testimonial-item .content {
    margin-bottom: 10px;
  }
}
.testimonial-item .content p {
  color: #05103b;
  font-size: 21px;
  line-height: 1.667;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 991px) {
  .testimonial-item .content p {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  .testimonial-item .content p {
    font-size: 15px;
    margin-bottom: 0;
  }
}
.testimonial-item .content .quote-icon {
  position: absolute;
  pointer-events: none;
  top: -10px;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
@media only screen and (max-width: 767px) {
  .testimonial-item .content .quote-icon {
    width: 80%;
  }
}
@media only screen and (max-width: 575px) {
  .testimonial-item .content .quote-icon {
    width: 20%;
  }
}
.testimonial-item .client-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  .testimonial-item .client-info {
    display: block;
  }
}
.testimonial-item .client-info .desc h5 {
  color: #ff5e14;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 17px;
  position: relative;
  padding-left: 44px;
}
@media only screen and (max-width: 991px) {
  .testimonial-item .client-info .desc h5 {
    font-size: 14px;
    margin-bottom: 12px;
  }
}
@media only screen and (max-width: 575px) {
  .testimonial-item .client-info .desc h5 {
    display: inline-block;
  }
}
.testimonial-item .client-info .desc h5:before {
  background-color: #ff5e14;
  content: "";
  position: absolute;
  width: 35px;
  height: 1px;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.testimonial-item .client-info .desc h4 {
  font-size: 26px;
}
@media only screen and (max-width: 991px) {
  .testimonial-item .client-info .desc h4 {
    font-size: 18px;
  }
}
.testimonial-item .client-info .rating {
  position: relative;
  top: -2px;
}
@media only screen and (max-width: 991px) {
  .testimonial-item .client-info .rating {
    font-size: 13px;
  }
}
.testimonial-item .client-info .rating span {
  color: #c7bfb2;
}
.testimonial-item .client-info .rating span.rating-color {
  color: #ffa200;
}

.testimonial-slider-container .swiper-pagination {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 70px auto 0;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: relative;
}
@media only screen and (max-width: 575px) {
  .testimonial-slider-container .swiper-pagination {
    margin: 50px auto 0;
  }
}
.testimonial-slider-container .swiper-pagination.swiper-pagination-bullets .swiper-pagination-bullet {
  background-color: #d4ccc8;
  border-radius: 50%;
  height: 12px;
  margin: 0 5px;
  opacity: 1;
  width: 12px;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
}
.testimonial-slider-container .swiper-pagination.swiper-pagination-bullets .swiper-pagination-bullet:hover {
  background-color: #ff5e14;
}
.testimonial-slider-container .swiper-pagination.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #ff5e14;
}

/*
-----------------------------------------------------------------------
    Divider CSS
-----------------------------------------------------------------------
*/
.divider-area.divider-default-area {
  background-color: #ff5e14;
  overflow: hidden;
  position: relative;
  padding: 64px 0 70px;
}
@media only screen and (max-width: 991px) {
  .divider-area.divider-default-area {
    padding: 54px 0 60px;
  }
}
@media only screen and (max-width: 767px) {
  .divider-area.divider-default-area {
    padding: 44px 0 50px;
  }
}
@media only screen and (max-width: 575px) {
  .divider-area.divider-default-area {
    text-align: center;
  }
}
@media only screen and (max-width: 575px) {
  .divider-area.divider-default-area .content {
    margin-bottom: 20px;
  }
}
.divider-area.divider-default-area .content .title {
  color: #fff;
  font-size: 45px;
  line-height: 1.2889;
  margin-bottom: 0;
}
@media only screen and (max-width: 1199px) {
  .divider-area.divider-default-area .content .title {
    font-size: 36px;
  }
}
@media only screen and (max-width: 991px) {
  .divider-area.divider-default-area .content .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .divider-area.divider-default-area .content .title {
    font-size: 24px;
  }
}
@media only screen and (max-width: 1199px) {
  .divider-area.divider-default-area .content .title br {
    display: none;
  }
}
.divider-area.divider-default-area .divider-btn {
  text-align: end;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 575px) {
  .divider-area.divider-default-area .divider-btn {
    text-align: center;
  }
}
.divider-area.divider-default-area .divider-btn .btn-theme {
  color: #05103b;
  font-size: 18px;
  padding: 25px 29.96px;
  position: relative;
  top: 3px;
}
@media only screen and (max-width: 767px) {
  .divider-area.divider-default-area .divider-btn .btn-theme {
    font-size: 14px;
    padding: 17px 15px;
  }
}
.divider-area.divider-default-area .divider-btn .btn-theme:hover {
  background-color: #05103b;
  color: #fff;
}
.divider-area.divider-default-area .divider-btn .btn-theme i {
  margin-left: 10px;
}
.divider-area.divider-default-area .shape-group .shape-style4 {
  bottom: 0;
  left: calc(50% - 60px);
  position: absolute;
  pointer-events: none;
}
@media only screen and (max-width: 575px) {
  .divider-area.divider-default-area .shape-group .shape-style4 {
    left: -100px;
  }
}
@media only screen and (max-width: 479.98px) {
  .divider-area.divider-default-area .shape-group .shape-style4 {
    left: -190px;
  }
}
@media only screen and (max-width: 359.98px) {
  .divider-area.divider-default-area .shape-group .shape-style4 {
    left: -300px;
  }
}
.divider-area.divider-style1-area {
  padding: 140px 0 0;
}
@media only screen and (max-width: 1199px) {
  .divider-area.divider-style1-area {
    padding: 100px 0 0;
  }
}
@media only screen and (max-width: 767px) {
  .divider-area.divider-style1-area {
    padding: 80px 0 0;
  }
}
.divider-area.divider-style2-area {
  padding: 0;
}

.divider-wrap {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #ff5e14;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
}
@media only screen and (max-width: 479.98px) {
  .divider-wrap {
    display: block;
  }
}
.divider-wrap .column-left {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 255px;
  padding: 0 0 0 70px;
  width: calc(50% - 35px);
}
@media only screen and (max-width: 991px) {
  .divider-wrap .column-left {
    min-height: 205px;
    padding: 0 0 0 30px;
  }
}
@media only screen and (max-width: 767px) {
  .divider-wrap .column-left {
    min-height: 190px;
    padding: 0 0 0 15px;
  }
}
@media only screen and (max-width: 575px) {
  .divider-wrap .column-left {
    min-height: 170px;
  }
}
@media only screen and (max-width: 479.98px) {
  .divider-wrap .column-left {
    min-height: auto;
    padding: 41px 15px 44px;
    text-align: center;
    width: 100%;
  }
}
.divider-wrap .column-left .content p {
  color: #fff;
  font-weight: 700;
  font-size: 35px;
  line-height: 1.2;
}
@media only screen and (max-width: 1199px) {
  .divider-wrap .column-left .content p {
    font-size: 28px;
  }
}
@media only screen and (max-width: 991px) {
  .divider-wrap .column-left .content p {
    font-size: 24px;
  }
}
@media only screen and (max-width: 767px) {
  .divider-wrap .column-left .content p {
    font-size: 22px;
  }
}
@media only screen and (max-width: 575px) {
  .divider-wrap .column-left .content p {
    font-size: 18px;
  }
}
.divider-wrap .column-right {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  min-height: 255px;
  overflow: hidden;
  padding-left: 85px;
  padding-right: 84px;
  position: relative;
  right: 0;
  text-align: center;
  width: calc(50% + 36px);
  clip-path: polygon(15% 0%, 100% 0%, 100% 100%, 100% 100%, 0% 100%);
  -webkit-clip-path: polygon(15% 0%, 100% 0%, 100% 100%, 100% 100%, 0% 100%);
}
@media only screen and (max-width: 991px) {
  .divider-wrap .column-right {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    min-height: 205px;
    padding-right: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .divider-wrap .column-right {
    min-height: 190px;
  }
}
@media only screen and (max-width: 575px) {
  .divider-wrap .column-right {
    min-height: 170px;
    padding-left: 40px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 479.98px) {
  .divider-wrap .column-right {
    background-image: url("../image/bg1.webp");
    background-repeat: no-repeat;
    background-size: cover;
    left: -3px;
    min-height: auto;
    padding: 40px 15px;
    width: calc(100% + 3px);
    clip-path: none;
    -webkit-clip-path: none;
  }
}
.divider-wrap .column-right:before {
  background-image: url("../image/bg1.webp");
  background-repeat: no-repeat;
  background-size: cover;
  content: "";
  height: 100%;
  left: 4px;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
  clip-path: polygon(15% 0%, 100% 0%, 100% 100%, 100% 100%, 0% 100%);
  -webkit-clip-path: polygon(15% 0%, 100% 0%, 100% 100%, 100% 100%, 0% 100%);
}
@media only screen and (max-width: 479.98px) {
  .divider-wrap .column-right:before {
    display: none;
  }
}
.divider-wrap .column-right .content .title {
  color: #fff;
  font-weight: 700;
  font-size: 62.81px;
  line-height: 1;
  margin-bottom: 0;
}
@media only screen and (max-width: 1199px) {
  .divider-wrap .column-right .content .title {
    font-size: 48px;
  }
}
@media only screen and (max-width: 991px) {
  .divider-wrap .column-right .content .title {
    font-size: 44px;
  }
}
@media only screen and (max-width: 767px) {
  .divider-wrap .column-right .content .title {
    font-size: 34px;
  }
}

/*
-----------------------------------------------------------------------
  Service CSS
-----------------------------------------------------------------------
*/
.service-area.service-default-area {
  background-color: #f5f5f5;
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.service-area.service-default-area .container {
  padding: 141px 15px 131px;
  position: relative;
}
@media only screen and (max-width: 1199px) {
  .service-area.service-default-area .container {
    padding: 97px 15px 100px;
  }
}
@media only screen and (max-width: 767px) {
  .service-area.service-default-area .container {
    padding: 77px 15px 72px;
  }
}
.service-area.service-default-area .section-title {
  margin-bottom: 79px;
}
@media only screen and (max-width: 1199px) {
  .service-area.service-default-area .section-title {
    margin-bottom: 61px;
  }
}
@media only screen and (max-width: 767px) {
  .service-area.service-default-area .section-title {
    margin-bottom: 40px;
  }
}
.service-area.service-default-area .section-title .subtitle {
  margin-bottom: 18px;
}
@media only screen and (max-width: 1199px) {
  .service-area.service-default-area .section-title .subtitle {
    margin-bottom: 15px;
  }
}
.service-area.service-default-area .shape-group .shape-style2 {
  left: -145px;
  opacity: 0.06;
  position: absolute;
  pointer-events: none;
  top: 0;
  z-index: -1;
}
.service-area.service-default-area .shape-group .shape-style3 {
  opacity: 0.06;
  position: absolute;
  pointer-events: none;
  right: -132px;
  top: 0;
  z-index: -1;
}
.service-area.service-grid-area {
  padding: 140px 0;
}
@media only screen and (max-width: 1199px) {
  .service-area.service-grid-area {
    padding: 100px 0;
  }
}
@media only screen and (max-width: 767px) {
  .service-area.service-grid-area {
    padding: 80px 0;
  }
}
.service-area.service-grid-area .service-item {
  margin-bottom: 30px;
}

.btn-service {
  padding: 21.5px 33px;
  margin-top: 30px;
}
@media only screen and (max-width: 767px) {
  .btn-service {
    font-size: 15px;
    margin-top: 25px;
    padding: 17px 25px;
  }
}
.btn-service i {
  margin-left: 10px;
}

.service-item {
  text-align: center;
}
.service-item .thumb {
  border-radius: 2px 2px 0 0;
  overflow: hidden;
}
.service-item .thumb img {
  width: 100%;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.service-item .content {
  background-color: #f5f5f5;
  border-radius: 0 0 0 2px;
  padding: 31px 12px 29px;
  position: relative;
  -webkit-clip-path: polygon(0% 0%, 100% 0%, 100% 70%, 91% 100%, 0% 100%);
          clip-path: polygon(0% 0%, 100% 0%, 100% 70%, 91% 100%, 0% 100%);
}
@media only screen and (max-width: 1199px) {
  .service-item .content {
    padding: 28px 12px 30px;
  }
}
@media only screen and (max-width: 575px) {
  .service-item .content {
    padding: 25px 10px 27px;
  }
}
.service-item .content:after {
  background-color: #ff5e14;
  content: "";
  height: 0;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.service-item .content .title {
  font-size: 23px;
  line-height: 1;
  margin-bottom: 0;
}
@media only screen and (max-width: 1199px) {
  .service-item .content .title {
    font-size: 20px;
  }
}
@media only screen and (max-width: 575px) {
  .service-item .content .title {
    font-size: 18px;
  }
}
.service-item .content .title a {
  color: #05103b;
}
.service-item:hover .thumb img {
  transform: scale(1.08);
  -webkit-transform: scale(1.08);
  -moz-transform: scale(1.08);
  -ms-transform: scale(1.08);
  -o-transform: scale(1.08);
}
.service-item:hover .content:after {
  background-color: #ff5e14;
  height: 100%;
}
.service-item:hover .content .title a {
  color: #fff;
}

.service-slider-container .swiper-pagination {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 73px auto 0;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  position: relative;
}
@media only screen and (max-width: 767px) {
  .service-slider-container .swiper-pagination {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin: 40px auto 0;
  }
}
.service-slider-container .swiper-pagination.swiper-pagination-bullets .swiper-pagination-bullet {
  background-color: #d4ccc8;
  border-radius: 50%;
  height: 8;
  margin: 0 5.1px;
  opacity: 1;
  width: 8;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
}
.service-slider-container .swiper-pagination.swiper-pagination-bullets .swiper-pagination-bullet:hover {
  background-color: #ff5e14;
}
.service-slider-container .swiper-pagination.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #ff5e14;
}

.get-started {
  color: #05103b;
  font-weight: 700;
  font-size: 17px;
  margin-top: -28px;
}
@media only screen and (max-width: 767px) {
  .get-started {
    margin-top: 38px;
    text-align: center;
  }
}
.get-started a {
  color: #ff5e14;
  position: relative;
  z-index: 1;
}

.service-details-area {
  padding: 140px 0 115px;
}
@media only screen and (max-width: 1199px) {
  .service-details-area {
    padding: 100px 0 75px;
  }
}
@media only screen and (max-width: 991px) {
  .service-details-area {
    padding: 100px 0 37px;
  }
}
@media only screen and (max-width: 767px) {
  .service-details-area {
    padding: 80px 0 27px;
  }
}

.sidebar-area .widget-title {
  margin-bottom: 39px;
}
@media only screen and (max-width: 575px) {
  .sidebar-area .widget-title {
    margin-bottom: 30px;
  }
}
.sidebar-area.service-sidebar-area {
  margin-right: 30px;
}
@media only screen and (max-width: 1199px) {
  .sidebar-area.service-sidebar-area {
    margin-right: 0;
  }
}

.widget-item {
  margin-bottom: 63px;
}

.widget-title .title {
  font-size: 30px;
}
@media only screen and (max-width: 575px) {
  .widget-title .title {
    font-size: 24px;
  }
}

.widget-search-box .form-input-item {
  position: relative;
}
.widget-search-box .form-input-item input {
  border: 1px solid #eeeeee;
  border-radius: 2px;
  color: #000;
  font-weight: 400;
  font-size: 16px;
  padding: 15px 60px 15px 25px;
  width: 100%;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.widget-search-box .form-input-item input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #656565;
}
.widget-search-box .form-input-item input::-moz-placeholder {
  /* Firefox 19+ */
  color: #656565;
}
.widget-search-box .form-input-item input:-ms-input-placeholder {
  /* IE 10+ */
  color: #656565;
}
.widget-search-box .form-input-item input:-moz-placeholder {
  /* Firefox 18- */
  color: #656565;
}
.widget-search-box .form-input-item input:focus {
  border-color: #ff5e14;
}
.widget-search-box .form-input-item button {
  background-color: #ff5e14;
  border: none;
  border-radius: 2px;
  color: #fff;
  font-size: 15px;
  line-height: 1;
  padding: 16.5px 16.5px;
  position: absolute;
  right: 6px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  transition: all 0.4s ease-in-out;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
}
.widget-search-box .form-input-item button:hover {
  background-color: #05103b;
}

.widget-categories {
  background-color: #f5f5f5;
  padding: 50px 35px;
}
@media only screen and (max-width: 575px) {
  .widget-categories {
    padding: 30px 15px;
  }
}
.widget-categories ul {
  margin-bottom: 0;
}
.widget-categories ul li {
  margin-bottom: 18px;
}
.widget-categories ul li:last-child {
  margin-bottom: 0;
}
.widget-categories ul li a {
  background-color: #fff;
  color: #656565;
  display: block;
  font-size: 16px;
  font-weight: 500;
  padding: 18px 23px 16px 24px;
  line-height: 1;
  transition: all 0.4s ease-in-out;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
}
@media only screen and (max-width: 575px) {
  .widget-categories ul li a {
    font-size: 15px;
  }
}
.widget-categories ul li a span {
  float: right;
}
.widget-categories ul li a:hover {
  background-color: #ff5e14;
  color: #fff;
}

.widget-tags {
  background-color: #f5f5f5;
  padding: 45px 25px 35px 35px;
}
@media only screen and (max-width: 575px) {
  .widget-tags {
    padding: 30px 10px 20px 15px;
  }
}
.widget-tags ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 0;
}
@media only screen and (max-width: 1199px) {
  .widget-tags ul {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: start;
  }
}
.widget-tags ul li {
  margin-right: 10px;
  margin-bottom: 10px;
}
.widget-tags ul li a {
  background-color: #fff;
  border-radius: 2px;
  color: #656565;
  display: inline-block;
  font-size: 15px;
  font-weight: 500;
  line-height: 1;
  padding: 17.5px 27px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.widget-tags ul li a:hover {
  background-color: #ff5e14;
  color: #fff;
}
.widget-tags ul li .tags-padding {
  padding: 17.5px 49px;
}
@media only screen and (max-width: 1199px) {
  .widget-tags ul li .tags-padding {
    padding: 17.5px 19px;
  }
}

.service-details-body .thumb img {
  border-radius: 2px;
}
.service-details-body .content .title {
  font-size: 40px;
  line-height: 1.2;
  margin-bottom: 22px;
  margin-top: 30px;
}
@media only screen and (max-width: 1199px) {
  .service-details-body .content .title {
    font-size: 36px;
  }
}
@media only screen and (max-width: 767px) {
  .service-details-body .content .title {
    font-size: 24px;
  }
}
.service-details-body .content p {
  color: #656565;
  margin-bottom: 16px;
}
@media only screen and (max-width: 767px) {
  .service-details-body .content p {
    font-size: 14px;
  }
}
.service-details-body .content ul {
  margin-bottom: 16px;
}
.service-details-body .content ul li {
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  color: #656565;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-weight: 500;
  font-size: 19px;
  margin-bottom: 5px;
}
@media only screen and (max-width: 767px) {
  .service-details-body .content ul li {
    font-size: 15px;
  }
}
@media only screen and (max-width: 575px) {
  .service-details-body .content ul li {
    font-size: 14px;
  }
}
.service-details-body .content ul li i {
  color: #ff5e14;
  margin-right: 25px;
  position: relative;
  top: 1px;
}
@media only screen and (max-width: 575px) {
  .service-details-body .content ul li i {
    margin-right: 10px;
  }
}

/*
-----------------------------------------------------------------------
  Featured CSS
-----------------------------------------------------------------------
*/
.featured-wrp {
  border-radius: 2px;
  margin-left: 48px;
  background-color: #f5f5f5;
  padding: 78px 30px 75px 41px;
}
@media only screen and (max-width: 1199px) {
  .featured-wrp {
    margin-left: 30px;
    padding: 40px 20px 45px;
  }
}
@media only screen and (max-width: 991px) {
  .featured-wrp {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin: 100px 0 0;
    padding: 50px 15px 19px;
  }
}
@media only screen and (max-width: 767px) {
  .featured-wrp {
    margin-top: 80px;
    padding: 55px 12px 24px;
  }
}
@media only screen and (max-width: 575px) {
  .featured-wrp {
    display: block;
    padding: 65px 15px 64px;
  }
}

.featured-item {
  margin-bottom: 39px;
}
@media only screen and (max-width: 1199px) {
  .featured-item {
    margin-bottom: 29px;
  }
}
@media only screen and (max-width: 991px) {
  .featured-item {
    margin: 0 12px 30px;
  }
}
@media only screen and (max-width: 767px) {
  .featured-item {
    margin: 0 10px 30px;
  }
}
@media only screen and (max-width: 575px) {
  .featured-item {
    margin: 0 0 30px;
  }
}
.featured-item:last-child {
  margin-bottom: 0;
}
.featured-item .title {
  font-size: 21px;
  font-weight: 700;
  margin-bottom: 13px;
}
@media only screen and (max-width: 1199px) {
  .featured-item .title {
    font-size: 18px;
    margin-bottom: 10px;
  }
}
@media only screen and (max-width: 991px) {
  .featured-item .title {
    font-size: 16px;
    margin-bottom: 12px;
  }
}
@media only screen and (max-width: 767px) {
  .featured-item .title {
    font-size: 18px;
  }
}
.featured-item .title a {
  color: #05103b;
}
.featured-item .title a:hover {
  color: #ff5e14;
}
.featured-item p {
  line-height: 1.5625;
  margin-bottom: 0;
}
@media only screen and (max-width: 991px) {
  .featured-item p {
    font-size: 13px;
  }
}

/*
-----------------------------------------------------------------------
	Form CSS
-----------------------------------------------------------------------
*/
.comment-form-wrap .section-title .title {
  color: #131b13;
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 45px;
  margin-top: 73px;
}
@media only screen and (max-width: 575px) {
  .comment-form-wrap .section-title .title {
    font-size: 24px;
    margin-bottom: 35px;
  }
}

.form-group {
  margin-bottom: 18px;
}
.form-group .form-control {
  background-color: #f5f5f5;
  border-radius: 0;
  border: 1px solid transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #000;
  font-weight: 400;
  font-size: 16px;
  padding: 14.5px 26px;
}
.form-group .form-control:focus {
  border-color: #ebebeb;
  background-color: #fff;
}
.form-group .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #656565;
  font-weight: 400;
  font-size: 16px;
}
.form-group .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #656565;
  font-weight: 400;
  font-size: 16px;
}
.form-group .form-control:-ms-input-placeholder {
  /* IE 10+ */
  color: #656565;
  font-weight: 400;
  font-size: 16px;
}
.form-group .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: #656565;
  font-weight: 400;
  font-size: 16px;
}
.form-group textarea.form-control {
  min-height: 285px;
}
@media only screen and (max-width: 767px) {
  .form-group textarea.form-control {
    min-height: 160px;
  }
}
.form-group .btn-theme {
  font-weight: 500;
  font-size: 17px;
  margin-bottom: 1px;
  margin-top: 21px;
  padding: 20.5px 33.5px;
  text-transform: capitalize;
}
@media only screen and (max-width: 767px) {
  .form-group .btn-theme {
    font-size: 15px;
    padding: 17px 23px;
  }
}
.form-group .btn-theme i {
  font-size: 19px;
  margin-right: 11px;
}
@media only screen and (max-width: 767px) {
  .form-group .btn-theme i {
    font-size: 15px;
    margin-left: 4px;
    margin-right: 0;
  }
}

.tracking-form-wrap {
  margin-top: -140px;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .tracking-form-wrap {
    margin-top: 0;
    padding-top: 80px;
  }
}
@media only screen and (max-width: 575px) {
  .tracking-form-wrap {
    padding-top: 70px;
  }
}
.tracking-form-wrap .tracking-searchbox {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #fff;
  border-radius: 2px;
  -webkit-box-shadow: 0px 0px 47.5px 2.5px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 47.5px 2.5px rgba(0, 0, 0, 0.1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-right: 13px;
  width: 100%;
  transition: all 0.4s ease-out;
  -webkit-transition: all 0.4s ease-out;
  -moz-transition: all 0.4s ease-out;
  -ms-transition: all 0.4s ease-out;
  -o-transition: all 0.4s ease-out;
}
@media only screen and (max-width: 1199px) {
  .tracking-form-wrap .tracking-searchbox {
    width: auto;
  }
}
@media only screen and (max-width: 767px) {
  .tracking-form-wrap .tracking-searchbox {
    background-color: rgba(255, 255, 255, 0.93);
    bottom: 30px;
    display: block;
    left: 30px;
    padding-right: 0;
    right: 30px;
  }
}
.tracking-form-wrap .tracking-searchbox .form-control {
  border: none;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #656565;
  font-size: 15px;
  font-weight: 400;
  padding: 11px 15px 11px 30px;
}
@media only screen and (max-width: 991px) {
  .tracking-form-wrap .tracking-searchbox .form-control {
    font-size: 14px;
  }
}
@media only screen and (max-width: 767px) {
  .tracking-form-wrap .tracking-searchbox .form-control {
    background: transparent;
    font-size: 15px;
    height: 70px;
    padding: 11px 15px;
  }
}
.tracking-form-wrap .tracking-searchbox .form-control option {
  font-size: 15px;
  font-weight: 400;
}
@media only screen and (max-width: 991px) {
  .tracking-form-wrap .tracking-searchbox .form-control option {
    font-size: 14px;
  }
}
.tracking-form-wrap .tracking-searchbox .form-control.select-dropdown {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-right: 1px solid #ebecf0;
  cursor: pointer;
  height: 85px;
  position: relative;
}
@media only screen and (max-width: 991px) {
  .tracking-form-wrap .tracking-searchbox .form-control.select-dropdown {
    height: 70px;
  }
}
@media only screen and (max-width: 767px) {
  .tracking-form-wrap .tracking-searchbox .form-control.select-dropdown {
    background-color: transparent;
    border-bottom: 1px solid #ebecf0;
    border-right: none;
  }
}
.tracking-form-wrap .tracking-searchbox .track-dropdown {
  min-width: 321px;
  position: relative;
}
@media only screen and (max-width: 1199px) {
  .tracking-form-wrap .tracking-searchbox .track-dropdown {
    min-width: 29%;
  }
}
@media only screen and (max-width: 991px) {
  .tracking-form-wrap .tracking-searchbox .track-dropdown {
    min-width: 28%;
  }
}
@media only screen and (max-width: 767px) {
  .tracking-form-wrap .tracking-searchbox .track-dropdown {
    width: 100%;
  }
}
.tracking-form-wrap .tracking-searchbox .track-dropdown.style-two {
  min-width: 366px;
}
@media only screen and (max-width: 1199px) {
  .tracking-form-wrap .tracking-searchbox .track-dropdown.style-two {
    min-width: 29%;
  }
}
@media only screen and (max-width: 991px) {
  .tracking-form-wrap .tracking-searchbox .track-dropdown.style-two {
    min-width: 28%;
  }
}
.tracking-form-wrap .tracking-searchbox .track-dropdown:after {
  content: "";
  color: #656565;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  font-family: "IcoFont";
  position: absolute;
  right: 37px;
  top: calc(50% - 8px);
  pointer-events: none;
}
@media only screen and (max-width: 767px) {
  .tracking-form-wrap .tracking-searchbox .track-dropdown:after {
    right: 19px;
  }
}
.tracking-form-wrap .tracking-searchbox .btn-theme {
  font-size: 17px;
  min-width: 190px;
  max-height: 60px;
  padding: 20.5px 42px;
  text-align: left;
}
@media only screen and (max-width: 991px) {
  .tracking-form-wrap .tracking-searchbox .btn-theme {
    font-size: 14px;
    min-width: 138px;
    padding: 17px 19px;
  }
}
@media only screen and (max-width: 767px) {
  .tracking-form-wrap .tracking-searchbox .btn-theme {
    font-size: 15px;
    height: 70px;
    max-height: none;
    padding: 17px 15px;
    width: 100%;
  }
}
.tracking-form-wrap .tracking-searchbox .btn-theme .icon {
  font-size: 20px;
  right: 39px;
}
@media only screen and (max-width: 991px) {
  .tracking-form-wrap .tracking-searchbox .btn-theme .icon {
    right: 16px;
  }
}

/*
-----------------------------------------------------------------------
  Project CSS
-----------------------------------------------------------------------
*/
.project-default-area {
  padding: 137px 0 0;
  position: relative;
}
@media only screen and (max-width: 1199px) {
  .project-default-area {
    padding: 97px 0 0;
  }
}
@media only screen and (max-width: 767px) {
  .project-default-area {
    padding: 76px 0 0;
  }
}
.project-default-area .container-fluid {
  padding: 0 75px;
}
@media only screen and (max-width: 1399px) {
  .project-default-area .container-fluid {
    padding: 0 30px;
  }
}
@media only screen and (max-width: 991px) {
  .project-default-area .container-fluid {
    padding: 0 15px;
  }
}
.project-default-area .section-title {
  margin-bottom: 67px;
}
@media only screen and (max-width: 1199px) {
  .project-default-area .section-title {
    margin-bottom: 57px;
  }
}
@media only screen and (max-width: 767px) {
  .project-default-area .section-title {
    margin-bottom: 48px;
  }
}
.project-default-area .section-title .title, .project-default-area .section-title .subtitle {
  color: #fff;
}
.project-default-area .bg-shape-style {
  border-radius: 2px;
  height: calc(100% - 148px);
  left: 50%;
  position: absolute;
  max-width: 930px;
  top: 0;
  width: 100%;
  z-index: -1;
  transform: translate(-50%, 0%);
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
}
@media only screen and (max-width: 1199px) {
  .project-default-area .bg-shape-style {
    height: calc(100% - 108px);
    max-width: 700px;
  }
}
@media only screen and (max-width: 991px) {
  .project-default-area .bg-shape-style {
    max-width: 550px;
  }
}
@media only screen and (max-width: 767px) {
  .project-default-area .bg-shape-style {
    height: calc(100% - 88px);
    max-width: 440px;
  }
}
@media only screen and (max-width: 575px) {
  .project-default-area .bg-shape-style {
    left: 0;
    max-width: none;
    right: 0;
    width: 100%;
    transform: translate(0%, 0%);
    -webkit-transform: translate(0%, 0%);
    -moz-transform: translate(0%, 0%);
    -ms-transform: translate(0%, 0%);
    -o-transform: translate(0%, 0%);
  }
}

.project-grid-area {
  padding: 140px 0;
}
@media only screen and (max-width: 1199px) {
  .project-grid-area {
    padding: 100px 0;
  }
}
@media only screen and (max-width: 767px) {
  .project-grid-area {
    padding: 80px 0;
  }
}

.project-item .thumb {
  overflow: hidden;
}
.project-item .thumb img {
  border-radius: 2px;
  width: 100%;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.project-item .content {
  padding: 35px 15px 0;
  text-align: center;
}
@media only screen and (max-width: 991px) {
  .project-item .content {
    padding: 22px 12px 0;
  }
}
.project-item .content .title {
  font-size: 25px;
  margin-bottom: 12px;
}
@media only screen and (max-width: 1199px) {
  .project-item .content .title {
    font-size: 22px;
  }
}
@media only screen and (max-width: 991px) {
  .project-item .content .title {
    font-size: 18px;
  }
}
.project-item .content .title a {
  color: #fff;
}
.project-item .content .category {
  font-size: 15px;
  font-weight: 500;
}
@media only screen and (max-width: 991px) {
  .project-item .content .category {
    font-size: 13px;
  }
}
.project-item .content .category a {
  color: #fff;
}
.project-item:hover .thumb img {
  transform: scale(1.15);
  -webkit-transform: scale(1.15);
  -moz-transform: scale(1.15);
  -ms-transform: scale(1.15);
  -o-transform: scale(1.15);
}

.projects-slider-container .project-item {
  overflow: hidden;
}
.projects-slider-container .project-item .content {
  display: none;
  opacity: 0;
  overflow: hidden;
  -webkit-animation-duration: 0.95s;
  animation-duration: 0.95s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  transition: all 0.4s ease-in-out;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
}
.projects-slider-container .swiper-slide.swiper-slide-active .project-item .content {
  display: block;
  height: 100%;
  opacity: 1;
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}
.projects-slider-container .swiper-btn-next, .projects-slider-container .swiper-btn-prev {
  border: 1px solid #eaeaea;
  bottom: 211px;
  background-color: transparent;
  border-radius: 50%;
  color: #05103b;
  cursor: pointer;
  display: inline-block;
  font-size: 22px;
  left: calc(50% - 633px);
  width: 55px;
  height: 55px;
  line-height: 55px;
  position: absolute;
  text-align: center;
  z-index: 1;
  transition: all 0.25s ease-in-out;
  -webkit-transition: all 0.25s ease-in-out;
  -moz-transition: all 0.25s ease-in-out;
  -ms-transition: all 0.25s ease-in-out;
  -o-transition: all 0.25s ease-in-out;
}
.projects-slider-container .swiper-btn-next:hover, .projects-slider-container .swiper-btn-prev:hover {
  border-color: #ff5e14;
  color: #ff5e14;
}
@media (max-width: 1419px) {
  .projects-slider-container .swiper-btn-next, .projects-slider-container .swiper-btn-prev {
    left: calc(50% - 540px);
  }
}
@media only screen and (max-width: 1199px) {
  .projects-slider-container .swiper-btn-next, .projects-slider-container .swiper-btn-prev {
    bottom: 170px;
    left: calc(50% - 425px);
  }
}
@media only screen and (max-width: 991px) {
  .projects-slider-container .swiper-btn-next, .projects-slider-container .swiper-btn-prev {
    left: calc(50% - 350px);
  }
}
@media only screen and (max-width: 767px) {
  .projects-slider-container .swiper-btn-next, .projects-slider-container .swiper-btn-prev {
    bottom: 138px;
    height: 35px;
    left: calc(50% - 273px);
    line-height: 35px;
    width: 35px;
  }
}
@media only screen and (max-width: 575px) {
  .projects-slider-container .swiper-btn-next, .projects-slider-container .swiper-btn-prev {
    color: #fff;
    left: 0;
    right: auto;
  }
}
.projects-slider-container .swiper-btn-next {
  left: calc(50% + 578px);
}
@media (max-width: 1419px) {
  .projects-slider-container .swiper-btn-next {
    left: calc(50% + 485px);
  }
}
@media only screen and (max-width: 1199px) {
  .projects-slider-container .swiper-btn-next {
    left: calc(50% + 370px);
  }
}
@media only screen and (max-width: 991px) {
  .projects-slider-container .swiper-btn-next {
    left: calc(50% + 295px);
  }
}
@media only screen and (max-width: 767px) {
  .projects-slider-container .swiper-btn-next {
    left: calc(50% + 238px);
  }
}
@media only screen and (max-width: 575px) {
  .projects-slider-container .swiper-btn-next {
    left: auto;
    right: 0;
  }
}
.projects-slider-container .swiper-pagination {
  bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  left: 50%;
  margin-top: 218px;
  max-width: 1170px;
  position: relative;
  width: 100%;
  transform: translate(-50%, 0);
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  -o-transform: translate(-50%, 0);
}
@media only screen and (max-width: 1199px) {
  .projects-slider-container .swiper-pagination {
    margin-top: 178px;
  }
}
@media only screen and (max-width: 767px) {
  .projects-slider-container .swiper-pagination {
    margin-top: 138px;
  }
}
.projects-slider-container .swiper-pagination .swiper-pagination-bullet {
  background-color: #f5f5f5;
  border-radius: 0;
  height: 8px;
  margin: 0;
  opacity: 1;
  width: 100%;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.projects-slider-container .swiper-pagination .swiper-pagination-bullet:hover {
  background-color: #ddd;
}
.projects-slider-container .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #ff5e14;
}

.portfolio-filter-menu {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #f5f5f5;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 85px 60px;
  padding: 20px 0 22px;
}
@media only screen and (max-width: 1199px) {
  .portfolio-filter-menu {
    margin: 0 25px 50px;
  }
}
@media only screen and (max-width: 767px) {
  .portfolio-filter-menu {
    margin: 0 0 50px;
  }
}
@media only screen and (max-width: 479.98px) {
  .portfolio-filter-menu {
    display: block;
    padding: 20px 15px 22px;
  }
}
.portfolio-filter-menu .btn-portfolio button {
  border: none;
  background-color: transparent;
  color: #05103b;
  font-weight: 500;
  font-size: 17px;
  line-height: 1;
}
@media only screen and (max-width: 575px) {
  .portfolio-filter-menu .btn-portfolio button {
    font-size: 14px;
  }
}
.portfolio-filter-menu .btn-portfolio button:hover, .portfolio-filter-menu .btn-portfolio button.active {
  color: #ff5e14;
  background-color: transparent;
}
.portfolio-filter-menu span {
  color: #05103b;
  display: block;
  font-weight: 500;
  font-size: 17px;
  margin: 0 20px;
}
@media only screen and (max-width: 767px) {
  .portfolio-filter-menu span {
    font-size: 15px;
    margin: 0 12px;
  }
}
@media only screen and (max-width: 575px) {
  .portfolio-filter-menu span {
    margin: 0 8px;
  }
}

.portfolio-item .inner-content {
  position: relative;
}
.portfolio-item .inner-content .thumb {
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
}
.portfolio-item .inner-content .thumb img {
  width: 100%;
}
.portfolio-item .inner-content .thumb:before {
  background-color: #05103b;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  pointer-events: none;
  transition: 0.4s;
  -webkit-transition: 0.4s;
  -moz-transition: 0.4s;
  -ms-transition: 0.4s;
  -o-transition: 0.4s;
}
.portfolio-item .inner-content .portfolio-info {
  bottom: 40px;
  opacity: 0;
  padding: 0 15px;
  position: absolute;
  left: 50%;
  width: 100%;
  text-align: center;
  -webkit-transform: translate(-50%);
          transform: translate(-50%);
}
.portfolio-item .inner-content .portfolio-info .title {
  font-size: 30px;
  margin-bottom: 5px;
  -webkit-transform: translateY(-20px);
          transform: translateY(-20px);
  transition: 0.4s;
  -webkit-transition: 0.4s;
  -moz-transition: 0.4s;
  -ms-transition: 0.4s;
  -o-transition: 0.4s;
}
@media only screen and (max-width: 1199px) {
  .portfolio-item .inner-content .portfolio-info .title {
    font-size: 22px;
  }
}
.portfolio-item .inner-content .portfolio-info .title a {
  color: #fff;
}
.portfolio-item .inner-content .portfolio-info .category {
  color: #ff5e14;
  font-weight: 400;
  font-size: 15px;
  line-height: 1;
  position: relative;
  -webkit-transform: translateY(20px);
          transform: translateY(20px);
  transition: 0.4s;
  -webkit-transition: 0.4s;
  -moz-transition: 0.4s;
  -ms-transition: 0.4s;
  -o-transition: 0.4s;
}
.portfolio-item .inner-content .btn-project {
  background-color: #ff5e14;
  border-radius: 50%;
  color: #fff;
  opacity: 0;
  position: absolute;
  font-size: 22px;
  top: 50%;
  left: 50%;
  width: 55px;
  height: 55px;
  line-height: 55px;
  text-align: center;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  transition: 0.4s;
  -webkit-transition: 0.4s;
  -moz-transition: 0.4s;
  -ms-transition: 0.4s;
  -o-transition: 0.4s;
}
@media only screen and (max-width: 767px) {
  .portfolio-item .inner-content .btn-project {
    font-size: 18px;
    height: 35px;
    line-height: 35px;
    width: 35px;
  }
}
.portfolio-item .inner-content:hover .thumb:before {
  opacity: 0.88;
  visibility: visible;
}
.portfolio-item .inner-content:hover .portfolio-info {
  opacity: 1;
  visibility: visible;
}
.portfolio-item .inner-content:hover .portfolio-info .title, .portfolio-item .inner-content:hover .portfolio-info .category {
  top: 0;
  -webkit-transform: translate(0);
          transform: translate(0);
}
.portfolio-item .inner-content:hover .btn-project {
  opacity: 1;
  top: 42%;
}

.btn-projects {
  border: 1px solid #e9e9e9;
  background-color: #f5f5f5;
  color: #05103b;
  padding: 20.5px 33.5px;
  margin-top: 30px;
}
@media only screen and (max-width: 767px) {
  .btn-projects {
    font-size: 15px;
    margin-top: 20px;
    padding: 17px 22px;
  }
}
.btn-projects i {
  margin-left: 7px;
}
.btn-projects:hover {
  background-color: #ff5e14;
  border-color: #ff5e14;
}

.project-details-area {
  padding: 141px 0 115px;
}
@media only screen and (max-width: 1199px) {
  .project-details-area {
    padding: 100px 0 75px;
  }
}
@media only screen and (max-width: 991px) {
  .project-details-area {
    padding: 100px 0 47px;
  }
}
@media only screen and (max-width: 767px) {
  .project-details-area {
    padding: 80px 0 27px;
  }
}

.project-sidebar-area {
  margin-left: 30px;
}
@media only screen and (max-width: 1199px) {
  .project-sidebar-area {
    margin-left: 0;
  }
}
.project-sidebar-area .widget-item {
  margin-bottom: 53px;
}

.widget-info {
  background-color: #05103b;
  border-radius: 2px;
  padding: 59px 40px 39px 70px;
}
@media only screen and (max-width: 991px) {
  .widget-info {
    padding: 59px 40px 39px 35px;
  }
}
.widget-info .info-item {
  margin-bottom: 26px;
}
.widget-info .info-item span {
  color: #ff5e14;
  display: block;
  font-size: 16px;
  margin-bottom: 3px;
}
.widget-info .info-item h4 {
  color: #fff;
  font-size: 22px;
  margin-bottom: 0;
}

.project-details-body .thumb img {
  border-radius: 2px;
}
.project-details-body .content .title {
  font-size: 40px;
  margin: 53px 0 22px;
}
@media only screen and (max-width: 767px) {
  .project-details-body .content .title {
    font-size: 30px;
  }
}
.project-details-body .content p {
  margin-bottom: 16px;
}
@media only screen and (max-width: 767px) {
  .project-details-body .content p {
    font-size: 15px;
  }
}
.project-details-body .content .thumb-inner {
  margin-top: 26px;
}
.project-details-body .content .thumb-inner img {
  border-radius: 2px;
}

/*
-----------------------------------------------------------------------
    Blog CSS
-----------------------------------------------------------------------
*/
.blog-area.blog-default-area {
  padding: 137px 0 140px;
}
@media only screen and (max-width: 1199px) {
  .blog-area.blog-default-area {
    padding: 97px 0 100px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-area.blog-default-area {
    padding: 77px 0 80px;
  }
}
.blog-area.blog-grid-area {
  padding: 140px 0;
}
@media only screen and (max-width: 1199px) {
  .blog-area.blog-grid-area {
    padding: 100px 0;
  }
}
@media only screen and (max-width: 767px) {
  .blog-area.blog-grid-area {
    padding: 80px 0;
  }
}

.post-item .thumb {
  margin-right: 150px;
  overflow: hidden;
}
@media only screen and (max-width: 1199px) {
  .post-item .thumb {
    margin-right: 100px;
  }
}
@media only screen and (max-width: 479.98px) {
  .post-item .thumb {
    margin-right: 15px;
  }
}
.post-item .thumb img {
  border-radius: 2px;
  width: 100%;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.post-item .content {
  background-color: #f5f5f5;
  border-radius: 2px;
  padding: 53px 45px 44px 53px;
  margin-left: 115px;
  margin-top: -200px;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 1199px) {
  .post-item .content {
    margin-left: 65px;
    margin-top: -150px;
  }
}
@media only screen and (max-width: 991px) {
  .post-item .content {
    margin-left: 50px;
    padding: 30px;
  }
}
@media only screen and (max-width: 479.98px) {
  .post-item .content {
    margin-left: 15px;
    padding: 30px 15px;
  }
}
.post-item .content .meta-tag {
  background-color: #ff5e14;
  border-radius: 2px;
  color: #fff;
  display: inline-block;
  font-size: 15px;
  font-weight: 700;
  padding: 7px 21px;
}
.post-item .content .title {
  font-size: 23px;
  line-height: 1.3043;
  margin: 26px 0 16px;
}
@media only screen and (max-width: 991px) {
  .post-item .content .title {
    font-size: 18px;
    line-height: 1.4;
    margin: 20px 0 12px;
  }
}
.post-item .content .title a {
  color: #05103b;
}
.post-item .content .title a:hover {
  color: #ff5e14;
}
.post-item .content p {
  font-size: 16px;
  margin-bottom: 19px;
}
.post-item .content .btn-link {
  color: #ff5e14;
  font-weight: 700;
  font-size: 16px;
  line-height: 1;
  position: relative;
  padding-left: 54px;
  text-transform: capitalize;
}
.post-item .content .btn-link:before {
  content: "";
  background-color: #ff5e14;
  position: absolute;
  min-width: 35px;
  height: 3px;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.post-item .content .btn-link:hover {
  color: #05103b;
}
.post-item .content .btn-link:hover:before {
  background-color: #05103b;
}
.post-item:hover .thumb img {
  transform: scale(1.15);
  -webkit-transform: scale(1.15);
  -moz-transform: scale(1.15);
  -ms-transform: scale(1.15);
  -o-transform: scale(1.15);
}

.shape-group .shape-style1 {
  position: absolute;
  top: 13.1%;
  right: 13.9%;
  left: auto;
  z-index: -1;
}
@media only screen and (max-width: 1399px) {
  .shape-group .shape-style1 {
    right: 4%;
  }
}
@media only screen and (max-width: 991px) {
  .shape-group .shape-style1 {
    width: 12%;
  }
}
@media only screen and (max-width: 767px) {
  .shape-group .shape-style1 {
    top: 100px;
  }
}

.post-item2 {
  margin-bottom: 65px;
}
@media only screen and (max-width: 1199px) {
  .post-item2 {
    margin-bottom: 50px;
  }
}
.post-item2 .thumb {
  border-radius: 2px;
  margin-bottom: 34px;
  overflow: hidden;
}
.post-item2 .thumb img {
  width: 100%;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}
.post-item2 .content {
  margin-top: 0;
}
.post-item2 .content .author {
  color: #9b9ea1;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 9px;
}
@media only screen and (max-width: 991px) {
  .post-item2 .content .author {
    font-size: 13px;
  }
}
.post-item2 .content .author a {
  color: #0f034a;
  margin-left: 5px;
}
.post-item2 .content .author a:hover {
  color: #ff5e14;
}
.post-item2 .content .title {
  font-size: 23px;
  line-height: 1.523;
  margin-bottom: 11px;
}
@media only screen and (max-width: 1199px) {
  .post-item2 .content .title {
    font-size: 18px;
  }
}
@media only screen and (max-width: 991px) {
  .post-item2 .content .title {
    font-size: 16px;
  }
}
@media only screen and (max-width: 767px) {
  .post-item2 .content .title {
    font-size: 17px;
  }
}
.post-item2 .content .title a {
  color: #05103b;
}
.post-item2 .content .title a:hover {
  color: #ff5e14;
}
.post-item2 .content p {
  color: #656565;
  line-height: 1.625;
  margin-bottom: 15px;
}
@media only screen and (max-width: 991px) {
  .post-item2 .content p {
    font-size: 15px;
  }
}
.post-item2 .content .meta {
  color: #0f034a;
  font-size: 15px;
}
@media only screen and (max-width: 991px) {
  .post-item2 .content .meta {
    font-size: 13px;
  }
}
.post-item2 .content .meta .post-date {
  margin-right: 8px;
}
.post-item2 .content .meta .dots {
  margin-right: 10px;
}
.post-item2 .content .meta .dots:before {
  background-color: #0f034a;
  border-radius: 50%;
  content: "";
  display: inline-block;
  width: 4px;
  height: 4px;
}
.post-item2:hover .thumb img {
  transform: scale(1.15);
  -webkit-transform: scale(1.15);
  -moz-transform: scale(1.15);
  -ms-transform: scale(1.15);
  -o-transform: scale(1.15);
}

.content-4column .post-item2 .thumb {
  margin-bottom: 20px;
}
.content-4column .post-item2 .content .author {
  font-size: 14px;
}
.content-4column .post-item2 .content .title {
  font-size: 18px;
}
.content-4column .post-item2 .content p {
  font-size: 15px;
}

.widget-post {
  background-color: #f5f5f5;
  padding: 42px 23px 42px 39px;
}
@media only screen and (max-width: 575px) {
  .widget-post {
    padding: 30px 15px;
  }
}

.widget-blog-post {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #f1ede7;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 19px;
  padding-bottom: 21px;
}
@media only screen and (max-width: 1199px) {
  .widget-blog-post {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}
@media only screen and (max-width: 991px) {
  .widget-blog-post {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}
.widget-blog-post:last-child {
  border: none;
  margin-bottom: 0;
  padding-bottom: 0;
}
.widget-blog-post .thumb {
  margin-right: 24px;
  min-width: 71px;
  min-height: 70px;
}
.widget-blog-post .thumb img {
  width: 100%;
}
@media only screen and (max-width: 1199px) {
  .widget-blog-post .content {
    margin-top: -5px;
  }
}
@media only screen and (max-width: 991px) {
  .widget-blog-post .content {
    margin-top: 0;
  }
}
.widget-blog-post .content h4 {
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 4px;
}
.widget-blog-post .content h4 a {
  color: #05103b;
}
.widget-blog-post .content h4 a:hover {
  color: #ff5e14;
}
.widget-blog-post .content .meta .post-date {
  color: #ff5e14;
  font-size: 14px;
  line-height: 1;
}
.widget-blog-post .content .meta .post-date i {
  font-size: 12px;
  margin-right: 4px;
  position: relative;
  top: -1px;
}

.blog-details-area {
  padding: 134px 0 140px;
}
@media only screen and (max-width: 1199px) {
  .blog-details-area {
    padding: 93px 0 99px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-details-area {
    padding: 73px 0 79px;
  }
}
.blog-details-area .related-posts-area {
  background-color: #f5f5f5;
}
.blog-details-area .related-posts-area .related-posts {
  padding: 76px 0 2px;
}
.blog-details-area .related-posts-area .related-posts .inner-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 0;
}
@media only screen and (max-width: 767px) {
  .blog-details-area .related-posts-area .related-posts .inner-title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 575px) {
  .blog-details-area .related-posts-area .related-posts .inner-title {
    font-size: 24px;
    position: relative;
    top: -10px;
  }
}
@media only screen and (max-width: 575px) {
  .blog-details-area .related-posts-area .related-posts .post-item2 {
    margin-bottom: 30px;
  }
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container {
  margin-top: -47px;
}
@media only screen and (max-width: 359.98px) {
  .blog-details-area .related-posts-area .related-posts .related-post-slider-container {
    margin-top: 30px;
    padding-bottom: 35px;
  }
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-next,
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-prev {
  border: 2px solid #dddbe3;
  border-radius: 2px;
  color: #05103b;
  display: inline-block;
  font-size: 25px;
  float: right;
  height: 52px;
  left: auto;
  line-height: 49px;
  margin-bottom: 45px;
  margin-top: 0;
  position: relative;
  right: 0;
  text-align: center;
  top: 0;
  width: 52px;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
}
@media only screen and (max-width: 767px) {
  .blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-next,
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-prev {
    height: 42px;
    line-height: 40px;
    width: 42px;
  }
}
@media only screen and (max-width: 359.98px) {
  .blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-next,
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-prev {
    display: none;
  }
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-next:after,
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-prev:after {
  display: none;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-next:hover,
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-prev:hover {
  background-color: #ff5e14;
  border-color: #ff5e14;
  color: #fff;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .swiper-button-prev {
  margin-right: 20px;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .post-item .thumb {
  margin-bottom: 34px;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .post-item .thumb img {
  width: 100%;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .post-item .content .author {
  font-weight: 500;
  margin-bottom: 9px;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .post-item .content .author a {
  color: #131b13;
  margin-left: 3px;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .post-item .content .author a:hover {
  color: #ff5e14;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .post-item .content .title {
  color: #10253b;
  font-weight: 700;
  font-size: 22px;
  margin-bottom: 6px;
}
.blog-details-area .related-posts-area .related-posts .related-post-slider-container .post-item .content .meta span {
  color: #131b13;
  font-weight: 500;
  font-size: 14px;
}

.post-details-content {
  padding-bottom: 48px;
}
.post-details-content .post-details-body .thumb {
  margin-bottom: 51px;
}
.post-details-content .post-details-body .content-top {
  text-align: center;
}
.post-details-content .post-details-body .content-top .meta {
  color: #656565;
  font-size: 15px;
  margin-bottom: 35px;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content-top .meta {
    margin-bottom: 25px;
  }
}
@media only screen and (max-width: 479.98px) {
  .post-details-content .post-details-body .content-top .meta {
    font-size: 13px;
  }
}
.post-details-content .post-details-body .content-top .meta .author {
  margin-right: 12px;
}
.post-details-content .post-details-body .content-top .meta .author a {
  color: #05103b;
  font-weight: 500;
}
.post-details-content .post-details-body .content-top .meta .author a:hover {
  color: #ff5e14;
}
.post-details-content .post-details-body .content-top .meta .post-date {
  margin-right: 15px;
}
.post-details-content .post-details-body .content-top .meta .dots {
  margin-right: 11px;
  position: relative;
  top: -2px;
}
.post-details-content .post-details-body .content-top .meta .dots:before {
  background-color: #0f034a;
  border-radius: 50%;
  content: "";
  display: inline-block;
  width: 4px;
  height: 4px;
}
.post-details-content .post-details-body .content-top .title {
  font-size: 42px;
  max-width: 1086px;
  margin: 0 auto;
  line-height: 1.309;
}
@media only screen and (max-width: 1199px) {
  .post-details-content .post-details-body .content-top .title {
    font-size: 36px;
  }
}
@media only screen and (max-width: 991px) {
  .post-details-content .post-details-body .content-top .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content-top .title {
    font-size: 24px;
  }
}
@media only screen and (max-width: 575px) {
  .post-details-content .post-details-body .content-top .title {
    font-size: 20px;
  }
}
.post-details-content .post-details-body .content-top .widget-tags {
  background-color: transparent;
  margin-bottom: 60px;
  margin-top: 46px;
  padding: 0;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content-top .widget-tags {
    margin-bottom: 40px;
  }
}
.post-details-content .post-details-body .content-top .widget-tags ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content-top .widget-tags ul li {
    margin-right: 0;
  }
}
.post-details-content .post-details-body .content-top .widget-tags ul li a {
  background-color: #f5f5f5;
  border-radius: 2px;
  color: #383e38;
  display: block;
  font-weight: 400;
  font-size: 15px;
  margin-right: 20px;
  padding: 11.5px 39.67px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content-top .widget-tags ul li a {
    font-size: 14px;
    margin-right: 10px;
    padding: 11px 23px;
  }
}
.post-details-content .post-details-body .content-top .widget-tags ul li a:hover, .post-details-content .post-details-body .content-top .widget-tags ul li a.active {
  background-color: #ff5e14;
  color: #fff;
}
.post-details-content .post-details-body .content-top .widget-tags ul li:last-child a {
  margin-right: 0;
}
.post-details-content .post-details-body .content .title {
  font-size: 27px;
  margin-bottom: 19px;
  line-height: 1.48148;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content .title {
    font-size: 18px;
  }
}
.post-details-content .post-details-body .content .title.title-style2 {
  font-size: 26px;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content .title.title-style2 {
    font-size: 18px;
  }
}
.post-details-content .post-details-body .content p {
  line-height: 1.8125;
  margin-bottom: 20px;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content p {
    font-size: 15px;
  }
}
.post-details-content .post-details-body .content .list-title .title {
  margin-bottom: 42px;
  margin-top: 29px;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content .list-title .title {
    margin-bottom: 30px;
  }
}
.post-details-content .post-details-body .content .list-style {
  margin-bottom: 43px;
}
.post-details-content .post-details-body .content .list-style li a {
  border: 1px solid #ededed;
  border-radius: 10px;
  color: #ff5e14;
  display: block;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 10px;
  padding: 15px 28px;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content .list-style li a {
    font-size: 14px;
    padding: 11px 20px;
  }
}
.post-details-content .post-details-body .content .list-style li a:hover {
  background-color: #f5f5f5;
  border-color: #f5f5f5;
}
.post-details-content .post-details-body .content .list-style li a i {
  margin-right: 13px;
}
.post-details-content .post-details-body .content .thumb-inner {
  margin-bottom: 42px;
  margin-top: 17px;
}
.post-details-content .post-details-body .content .blockquote-item {
  background-color: #ff5e14;
  padding: 68px 70px;
  margin-top: 45px;
  margin-bottom: 42px;
}
@media only screen and (max-width: 991px) {
  .post-details-content .post-details-body .content .blockquote-item {
    padding: 48px 50px;
  }
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content .blockquote-item {
    padding: 40px 30px;
  }
}
@media only screen and (max-width: 575px) {
  .post-details-content .post-details-body .content .blockquote-item {
    padding: 40px 15px;
  }
}
.post-details-content .post-details-body .content .blockquote-item .content {
  padding: 0;
  text-align: center;
}
.post-details-content .post-details-body .content .blockquote-item .content p {
  color: #fff;
  font-weight: 700;
  font-size: 44px;
  margin-bottom: 0;
  line-height: 1.545454;
}
@media only screen and (max-width: 991px) {
  .post-details-content .post-details-body .content .blockquote-item .content p {
    font-size: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content .blockquote-item .content p {
    font-size: 20px;
  }
}
.post-details-content .post-details-body .content .blockquote-item .content p span {
  font-weight: 700;
}
.post-details-content .post-details-body .content .widget-social-icons {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #f3f3f3;
  border-top: 1px solid #f3f3f3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 17px 68px;
  margin-top: 43px;
  margin-bottom: 32px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  .post-details-content .post-details-body .content .widget-social-icons {
    padding: 15px;
  }
}
@media only screen and (max-width: 767px) and (max-width: 479.98px) {
  .post-details-content .post-details-body .content .widget-social-icons {
    display: block;
  }
}
.post-details-content .post-details-body .content .widget-social-icons span {
  color: #0a1e43;
  font-weight: 600;
  font-size: 21px;
}
@media only screen and (max-width: 575px) {
  .post-details-content .post-details-body .content .widget-social-icons span {
    display: inline-block;
    font-size: 18px;
    margin-bottom: 15px;
  }
}
.post-details-content .post-details-body .content .widget-social-icons a {
  background-color: #f5f5f5;
  border-radius: 2px;
  color: #22262a;
  display: inline-block;
  font-size: 14px;
  margin-left: 13px;
  height: 45px;
  width: 45px;
  line-height: 45px;
  text-align: center;
}
@media only screen and (max-width: 575px) {
  .post-details-content .post-details-body .content .widget-social-icons a {
    height: 35px;
    line-height: 35px;
    margin-left: 8px;
    width: 35px;
  }
}
@media only screen and (max-width: 479.98px) {
  .post-details-content .post-details-body .content .widget-social-icons a {
    margin-bottom: 10px;
  }
}
@media only screen and (max-width: 479.98px) {
  .post-details-content .post-details-body .content .widget-social-icons a:first-child {
    margin-left: 0;
  }
}
.post-details-content .post-details-body .content .widget-social-icons a:hover {
  background-color: #ff5e14;
  color: #fff;
}

.comment-area {
  padding: 100px 0 0;
}
@media only screen and (max-width: 767px) {
  .comment-area {
    padding: 70px 0 0;
  }
}
.comment-area .comment-view-area .title {
  font-size: 30px;
  margin-bottom: 39px;
}
@media only screen and (max-width: 575px) {
  .comment-area .comment-view-area .title {
    font-size: 24px;
  }
}
.comment-area .comment-view-area .comment-content {
  border: 1px solid #e6e6e6;
  padding: 43px 41px 23px 36px;
}
@media only screen and (max-width: 575px) {
  .comment-area .comment-view-area .comment-content {
    padding: 30px 15px;
  }
}
.comment-area .comment-view-area .comment-content.comment-content-style2 {
  margin-left: 100px;
  margin-top: 30px;
}
@media only screen and (max-width: 575px) {
  .comment-area .comment-view-area .comment-content.comment-content-style2 {
    margin-left: 30px;
  }
}
.comment-area .comment-view-area .comment-content .single-comment .author-info {
  position: relative;
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .thumb {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-width: 90px;
  min-height: 90px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: absolute;
  top: -15px;
}
@media only screen and (max-width: 767px) {
  .comment-area .comment-view-area .comment-content .single-comment .author-info .thumb {
    top: 0;
  }
}
@media only screen and (max-width: 479.98px) {
  .comment-area .comment-view-area .comment-content .single-comment .author-info .thumb {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-bottom: 20px;
    position: relative;
  }
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .author-details {
  margin-bottom: 31px;
  padding-left: 125px;
}
@media only screen and (max-width: 575px) {
  .comment-area .comment-view-area .comment-content .single-comment .author-info .author-details {
    padding-left: 105px;
  }
}
@media only screen and (max-width: 479.98px) {
  .comment-area .comment-view-area .comment-content .single-comment .author-info .author-details {
    padding-left: 0;
  }
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .author-details .title {
  font-size: 22px;
  margin-bottom: 6px;
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .author-details ul {
  margin-bottom: 0;
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .author-details ul li {
  color: #626262;
  font-weight: 400;
  font-size: 15px;
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .author-details ul li span {
  color: #ff5e14;
  margin-left: 3px;
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .author-details .btn-reply {
  background-color: #f4f4f4;
  position: absolute;
  top: 7px;
  right: 0;
  font-weight: 700;
  font-size: 14px;
  padding: 10.5px 20.45px;
}
@media only screen and (max-width: 767px) {
  .comment-area .comment-view-area .comment-content .single-comment .author-info .author-details .btn-reply {
    display: inline-block;
    margin-top: 8px;
    padding: 5px 15px;
    position: relative;
  }
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .author-details .btn-reply:hover {
  background-color: #ff5e14;
  color: #fff;
}
.comment-area .comment-view-area .comment-content .single-comment .author-info .author-details .btn-reply i {
  margin-right: 11px;
}
.comment-area .comment-view-area .comment-content .single-comment .author-info p {
  color: #656565;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.66666;
  padding-left: 10px;
}
@media only screen and (max-width: 479.98px) {
  .comment-area .comment-view-area .comment-content .single-comment .author-info p {
    font-size: 14px;
    padding-left: 0;
  }
}

/*
-----------------------------------------------------------------------
	Contact CSS
-----------------------------------------------------------------------
*/
.contact-area {
  padding: 131px 0 139px;
}
@media only screen and (max-width: 1199px) {
  .contact-area {
    padding: 91px 0 99px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-area {
    padding: 71px 0 79px;
  }
}
.contact-area .section-title {
  margin-bottom: 42px;
}
@media only screen and (max-width: 991px) {
  .contact-area .section-title {
    margin-bottom: 40px;
  }
}

.contact-info {
  background-color: #f5f5f5;
  border-radius: 2px;
  margin-left: 70px;
  margin-top: 9px;
  padding: 94px 68px 87px;
}
@media only screen and (max-width: 1199px) {
  .contact-info {
    margin-left: 0;
  }
}
@media only screen and (max-width: 991px) {
  .contact-info {
    padding: 70px 30px 59px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-info {
    padding: 70px 30px;
  }
}
@media only screen and (max-width: 359.98px) {
  .contact-info {
    padding: 50px 15px;
  }
}

.contact-info-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 60px;
}
@media only screen and (max-width: 991px) {
  .contact-info-item {
    margin-bottom: 0;
  }
}
@media only screen and (max-width: 767px) {
  .contact-info-item {
    margin-bottom: 30px;
  }
}
.contact-info-item .icon {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #ff5e14;
  border-radius: 2px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 85px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-right: 40px;
  min-width: 85px;
  width: 85px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
@media only screen and (max-width: 991px) {
  .contact-info-item .icon {
    height: 55px;
    margin-right: 15px;
    min-width: 55px;
    width: 55px;
  }
}
@media only screen and (max-width: 991px) {
  .contact-info-item .icon img {
    width: 30px;
  }
}
.contact-info-item .content {
  margin-top: -6px;
}
@media only screen and (max-width: 991px) {
  .contact-info-item .content {
    margin-top: -5px;
  }
}
.contact-info-item .content h4 {
  color: #05103b;
  font-size: 28px;
  margin-bottom: 16px;
}
@media only screen and (max-width: 991px) {
  .contact-info-item .content h4 {
    font-size: 20px;
    margin-bottom: 5px;
  }
}
.contact-info-item .content p {
  color: #656565;
  font-size: 19px;
  font-weight: 500;
  line-height: 34px;
}
@media only screen and (max-width: 991px) {
  .contact-info-item .content p {
    font-size: 14px;
    line-height: 1.6;
  }
}
.contact-info-item .content a {
  color: #656565;
  display: block;
  font-size: 19px;
  font-weight: 500;
}
@media only screen and (max-width: 991px) {
  .contact-info-item .content a {
    font-size: 14px;
  }
}
.contact-info-item .content a:hover {
  color: #ff5e14;
}
.contact-info-item:hover .icon {
  background-color: #05103b;
}

.contact-form-wrapper .form-group {
  margin-bottom: 20px;
}
.contact-form-wrapper .form-group .btn-theme {
  margin-top: 19px;
}

/*
-----------------------------------------------------------------------
  Footer CSS
-----------------------------------------------------------------------
*/
.footer-area.default-style .footer-main {
  background-color: #05103b;
  padding: 139px 0 74px;
  position: relative;
}
@media only screen and (max-width: 1199px) {
  .footer-area.default-style .footer-main {
    padding: 95px 0 33px;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area.default-style .footer-main {
    padding: 80px 0 50px;
  }
}
.footer-area.default-style .footer-main .footer-shape {
  position: absolute;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
@media only screen and (max-width: 767px) {
  .footer-area .widget-item {
    margin-bottom: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area .widget-title {
    display: none;
  }
}
.footer-area .widget-title.widget-collapsed-title {
  display: block;
  position: relative;
  background-color: #424242;
  padding: 20px 15px;
  border-radius: 10px 10px 0 0;
  cursor: pointer;
  font-size: 17px;
  margin-bottom: 0;
  transition: all 0.1s ease-in-out;
  -webkit-transition: all 0.1s ease-in-out;
  -moz-transition: all 0.1s ease-in-out;
  -ms-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
}
@media only screen and (min-width: 768px) {
  .footer-area .widget-title.widget-collapsed-title {
    display: none;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area .widget-title.widget-collapsed-title {
    padding: 18px 20px;
  }
}
.footer-area .widget-title.widget-collapsed-title:before {
  font-family: "IcoFont";
  font-size: 14px;
  content: "";
  position: absolute;
  right: 0;
  height: 100%;
  width: 60px;
  text-align: center;
  top: calc(50% - 8px);
}
.footer-area .widget-title.widget-collapsed-title.collapsed {
  border-radius: 10px;
}
.footer-area .widget-title.widget-collapsed-title:not(.collapsed):before {
  content: "";
  font-family: "IcoFont";
}
.footer-area .widget-collapse-body {
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -ms-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
}
@media only screen and (min-width: 768px) {
  .footer-area .widget-collapse-body {
    display: block;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area .widget-collapse-body {
    background-color: #535353;
    padding: 0 22px 20px;
  }
}
@media only screen and (max-width: 767px) {
  .footer-area .widget-collapse-body .widget-blog-wrap, .footer-area .widget-collapse-body .widget-menu-wrap {
    padding-top: 35px;
  }
}

.reveal-footer {
  bottom: 0;
  left: 0;
  position: fixed;
  width: 100%;
}
@media only screen and (max-width: 991px) {
  .reveal-footer {
    position: relative;
  }
}

.site-wrapper-reveal {
  position: relative;
}
@media only screen and (max-width: 991px) {
  .site-wrapper-reveal {
    margin-bottom: 0 !important;
  }
}

.widget-title {
  color: #fff;
  line-height: 1;
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 44px;
}
@media only screen and (max-width: 991px) {
  .widget-title {
    font-size: 24px;
    margin-bottom: 35px;
  }
}

.widget-blog-wrap {
  margin-top: -8px;
}

.blog-post-item {
  margin-bottom: 18px;
}
.blog-post-item .content .title {
  color: #d8dadf;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 19px;
  line-height: 31px;
  margin-bottom: 9px;
}
@media only screen and (max-width: 767px) {
  .blog-post-item .content .title {
    font-size: 16px;
  }
}
.blog-post-item .content .title .icon {
  color: #d8dadf;
  font-size: 17px;
  margin-right: 16px;
  margin-top: 8px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.blog-post-item .content .title a {
  color: #d8dadf;
}
.blog-post-item .content .title a:hover {
  color: #ff5e14;
}
.blog-post-item .content .title:hover .icon {
  color: #ff5e14;
}
.blog-post-item .content .meta-date {
  display: block;
  font-size: 14px;
  margin-left: 33px;
}
@media only screen and (max-width: 767px) {
  .blog-post-item .content .meta-date {
    font-size: 13px;
  }
}
.blog-post-item .content .meta-date i {
  margin-right: 2px;
}
.blog-post-item .content .meta-date a:hover {
  color: #fff;
}

.widget-menu-wrap .nav-menu {
  display: block;
  margin-bottom: 0;
  margin-left: -1px;
}
.widget-menu-wrap .nav-menu li a {
  color: #d8dadf;
  font-size: 17px;
  line-height: 1;
  margin-bottom: 28px;
  display: block;
}
@media only screen and (max-width: 767px) {
  .widget-menu-wrap .nav-menu li a {
    font-size: 15px;
    margin-bottom: 25px;
  }
}
.widget-menu-wrap .nav-menu li a i {
  color: #d8dadf;
  margin-right: 16px;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}
.widget-menu-wrap .nav-menu li a:hover {
  color: #ff5e14;
}
.widget-menu-wrap .nav-menu li a:hover i {
  color: #ff5e14;
  margin-right: 17px;
}

.footer-bottom {
  background-color: #091649;
  padding: 21px 0;
}

.widget-copyright p {
  color: #fff;
  font-size: 16px;
  text-align: center;
}
.widget-copyright p span {
  color: #ff5e14;
}
.widget-copyright p i {
  color: #ff5e14;
  font-size: 15px;
  margin: 0 2px 0 1px;
}
.widget-copyright p a {
  color: #ff5e14;
}