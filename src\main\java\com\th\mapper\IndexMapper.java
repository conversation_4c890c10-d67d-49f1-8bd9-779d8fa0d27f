package com.th.mapper;

import com.th.pojo.*;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import java.util.ArrayList;

@Repository
public interface IndexMapper {

    @Select({"select * from `config` where companyId = #{companyId}"})
    ArrayList<Config> getConfigs(Integer companyId);

    @Select({"select * from `news` where catalog = #{catalog} and companyId = #{companyId} order by id DESC limit 6"})
    Page<News> getNewsByCatalog(@Param("catalog") String catalog, @Param("companyId") Integer companyId);

    @Select({"select * from `project` where companyId = #{companyId} order by id DESC limit 6"})
    Page<Project> getProjectsLimitSix(Integer companyId);

    @Select({"select * from `partner` where companyId = #{companyId} order by id DESC limit 6"})
    Page<Partner> getPartnerslimitSix(Integer companyId);

    @Select({"select * from `news` where catalog = #{catalog} and companyId = #{companyId} order by id DESC"})
    Page<News> getNewsByCatalogPage(@Param("catalog") String catalog, @Param("companyId") Integer companyId);

    @Select({"select * from `news` where id = #{id}"})
    News getNewsById(int id);

    @Select({"select * from `project` where id = #{id}"})
    Project getProjectById(int id);

    @Select({"select * from `project` where companyId = #{companyId} order by id DESC"})
    Page<Project> getProjectsPage(Integer companyId);

    @Select({"select * from `product` where companyId = #{companyId} order by id DESC"})
    Page<Product> getProductsPage(Integer companyId);

    @Select({"select * from `product` where id = #{id}"})
    Product getProductById(int id);
}
