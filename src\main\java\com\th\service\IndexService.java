package com.th.service;

import com.th.mapper.IndexMapper;
import org.springframework.stereotype.Service;
import com.th.pojo.*;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.ArrayList;

@Service
public class IndexService {

    @Autowired
    public IndexMapper indexMapper;

    public ArrayList<Config> getConfigs(Integer companyId) {return indexMapper.getConfigs(companyId);}

    public Page<News> getNewsByCatalog(String catalog, Integer companyId) {return indexMapper.getNewsByCatalog(catalog, companyId);}

    public Page<Project> getProjectsLimitSix(Integer companyId) {
        return indexMapper.getProjectsLimitSix(companyId);
    }

    public Page<Partner> getPartnerslimitSix(Integer companyId) {
        return indexMapper.getPartnerslimitSix(companyId);
    }

    public Page<News> getNewsByCatalogPage(String catalog, Integer companyId) {return indexMapper.getNewsByCatalogPage(catalog, companyId);}

    public News getNewsById(int id) { return indexMapper.getNewsById(id);}

    public Project getProjectById(int id) { return indexMapper.getProjectById(id);}

    public Page<Project> getProjectsPage(Integer companyId) {return indexMapper.getProjectsPage(companyId);}

    public Page<Product> getProductsPage(Integer companyId) {return indexMapper.getProductsPage(companyId);}

    public Product getProductById(int id) {return indexMapper.getProductById(id);}
}
