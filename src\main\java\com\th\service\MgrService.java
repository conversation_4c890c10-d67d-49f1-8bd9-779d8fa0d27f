package com.th.service;

import com.th.mapper.MgrMapper;
import com.th.pojo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MgrService {

    @Autowired
    public MgrMapper mgrMapper;

    public Company selectCompanyByUsername(String username, Integer companyId) {
        return mgrMapper.selectCompanyByUsername(username, companyId);
    }

    public Company selectCompanyByWebsite(String website) {
        return mgrMapper.selectCompanyByWebsite(website);
    }

    public Company selectCompanyById(Integer id) {
        return mgrMapper.selectCompanyById(id);
    }

    public boolean updatePassword(Integer id, String password) {
        return mgrMapper.updatePassword(id, password);
    }
}
