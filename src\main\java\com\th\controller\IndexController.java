package com.th.controller;

import com.th.common.Constant;
import com.th.common.NumberHelper;
import com.th.pojo.*;
import com.th.service.IndexService;
import com.th.service.TemplateService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Controller
public class IndexController {

    @Autowired
    private IndexService indexService;

    @Autowired
    private TemplateService templateService;

    /**
     * 获取系统配置信息
     *
     * @return
     */
    public HashMap<String, Config> config(HttpSession session) {
        Integer companyId = getCompanyId(session);
        ArrayList<Config> configArrayList = indexService.getConfigs(getCompanyId(session));
        // List转Map，基于JDK1.8的Stream API和函数式接口实现，同时还可去重
        HashMap<String, Config> configHashMap = (HashMap<String, Config>) configArrayList.stream().collect(Collectors.toMap(Config::getKey, Function.identity()));
        for (String configAttr : Constant.CONFIG_ATTRS) {
            if (!configHashMap.containsKey(configAttr)) {
                Config config = new Config();
                config.setCompanyId(companyId);
                config.setKey(configAttr);
                config.setValue("");
                configHashMap.put(configAttr, config);
            }
        }
        return configHashMap;
    }

    /**
     * 首页
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/")
    public String home (HttpSession session, Model model){
        Integer companyId = getCompanyId(session);
        ArrayList<News> industryNews = indexService.getNewsByCatalog("行业资讯", companyId);
        ArrayList<News> companyNews = indexService.getNewsByCatalog("公司新闻", companyId);
        ArrayList<Project> projects = indexService.getProjectsLimitSix(companyId);
        ArrayList<Partner> partners = indexService.getPartnerslimitSix(companyId);
        model.addAttribute("configs", config(session));
        model.addAttribute("industryNews", industryNews);
        model.addAttribute("companyNews", companyNews);
        model.addAttribute("projects", projects);
        model.addAttribute("partners", partners);
        return buildTemplatePath(session, "index/index.html");
    }

    /**
     * 首页
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/Index/index")
    public String index (HttpSession session, Model model){
        Integer companyId = getCompanyId(session);
        ArrayList<News> industryNews = indexService.getNewsByCatalog("行业资讯", companyId);
        ArrayList<News> companyNews = indexService.getNewsByCatalog("公司新闻", companyId);
        ArrayList<Project> projects = indexService.getProjectsLimitSix(companyId);
        ArrayList<Partner> partners = indexService.getPartnerslimitSix(companyId);
        model.addAttribute("configs", config(session));
        model.addAttribute("industryNews", industryNews);
        model.addAttribute("companyNews", companyNews);
        model.addAttribute("projects", projects);
        model.addAttribute("partners", partners);
        return buildTemplatePath(session, "index/index.html");
    }

    /**
     * 关于我们 - 公司简介
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/About/about")
    public String about (HttpSession session, Model model){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/about.html");
    }

    /**
     * 关于我们 - 经营范围
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/About/scope")
    public String scope (HttpSession session, Model model){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/scope.html");
    }

    /**
     * 关于我们 - 企业文化
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/About/civilization")
    public String civilization (HttpSession session, Model model){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/civilization.html");
    }

    /**
     * 关于我们 - 人才体系
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/About/setup")
    public String setup (HttpSession session, Model model){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/setup.html");
    }

    /**
     * 关于我们 - 合作伙伴
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/About/partner")
    public String partner (HttpSession session, Model model){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/partner.html");
    }

    /**
     * 联系我们 - 联系方式
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/Contact/contact")
    public String contact (HttpSession session, Model model){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/contact.html");
    }

    /**
     * 联系我们 - 在线留言
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/Contact/online")
    public String online (HttpSession session, Model model){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/online.html");
    }

    /**
     * 人才招聘 - 人才中心
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/Talent/talent")
    public String talent (HttpSession session, Model model){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/talent.html");
    }

    /**
     * 新闻中心 - 行业资讯
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/News/industry")
    public String industry (@RequestParam(defaultValue = "1") int pageNo, @RequestParam(defaultValue = "5") int pageSize, HttpSession session, Model model) {
        PageHelper.startPage(pageNo,pageSize);
        PageInfo<News> pageInfo = new PageInfo<>(indexService.getNewsByCatalogPage("行业资讯", getCompanyId(session)));
        model.addAttribute(pageInfo);
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/industry.html");
    }

    /**
     * 新闻中心 - 公司新闻
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/News/company")
    public String company (@RequestParam(defaultValue = "1") int pageNo, @RequestParam(defaultValue = "5") int pageSize, HttpSession session, Model model) {
        PageHelper.startPage(pageNo,pageSize);
        PageInfo<News> pageInfo = new PageInfo<>(indexService.getNewsByCatalogPage("公司新闻", getCompanyId(session)));
        model.addAttribute(pageInfo);
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/company.html");
    }

    /**
     * 新闻详情
     *
     * @param model
     * @param newsId
     * @return
     */
    @GetMapping(value = "/News/newsDetail/id/{newsId}")
    public String newsDetail (HttpSession session, Model model, @PathVariable String newsId){
        int id = 0;
        if (NumberHelper.isInteger(newsId)) {
            id = Integer.parseInt(newsId);
            News news = indexService.getNewsById(id);
            if (null == news) {
                return "error.html";
            } else {
                model.addAttribute("news", news);
                model.addAttribute("configs", config(session));
            }
        }else{
            return "error.html";
        }
        return buildTemplatePath(session, "index/news_detail.html");
    }

    /**
     * 渠道
     *
     * @param pageNo
     * @param pageSize
     * @param model
     * @return
     */
    @GetMapping(value = "/Project/projects")
    public String projects (@RequestParam(defaultValue = "1") int pageNo, @RequestParam(defaultValue = "8") int pageSize, HttpSession session, Model model) {
        PageHelper.startPage(pageNo,pageSize);
        PageInfo<Project> pageInfo = new PageInfo<>(indexService.getProjectsPage(getCompanyId(session)));
        model.addAttribute(pageInfo);
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/projects.html");
    }

    @GetMapping(value = "/Project/projectDetail/id/{projectId}")
    public String projectDetail (HttpSession session, Model model, @PathVariable String projectId){
        PageHelper.startPage(0,10);
        PageInfo<Project> pageInfo = new PageInfo<>(indexService.getProjectsPage(getCompanyId(session)));
        model.addAttribute(pageInfo);
        int id = 0;
        if (NumberHelper.isInteger(projectId)) {
            id = Integer.parseInt(projectId);
            Project project = indexService.getProjectById(id);
            if (null == project) {
                return "error.html";
            } else {
                model.addAttribute("project", project);
                model.addAttribute("configs", config(session));
            }
        }else{
            return "error.html";
        }
        return buildTemplatePath(session, "index/project_detail.html");
    }

    /**
     * 下载中心
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/Download/download")
    public String download (@RequestParam(defaultValue = "1") int pageNo, @RequestParam(defaultValue = "3") int pageSize, HttpSession session, Model model) {
        PageHelper.startPage(pageNo,pageSize);
        PageInfo<Product> pageInfo = new PageInfo<>(indexService.getProductsPage(getCompanyId(session)));
        model.addAttribute(pageInfo);
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/download.html");
    }

    /**
     * 产品下载
     *
     * @param model
     * @param productId
     * @return
     */
    @GetMapping(value = "/Download/product/id/{productId}")
    public String product (HttpSession session, Model model, @PathVariable String productId){
        int id = 0;
        if (NumberHelper.isInteger(productId)) {
            id = Integer.parseInt(productId);
            Product product = indexService.getProductById(id);
            if (null == product) {
                return "error.html";
            } else {
                model.addAttribute("product", product);
            }
        }else{
            return "error.html";
        }
        return buildTemplatePath(session, "index/product.html");
    }

    /**
     * ITS-查件
     *
     * @param model
     * @return
     */
    @GetMapping(value = "/Its/track/{nos}")
    public String queryTrack (HttpSession session, Model model, @PathVariable String nos){
        model.addAttribute("configs", config(session));
        return buildTemplatePath(session, "index/track.html");
    }



    /**
     * 获取公司ID
     * @param session
     * @return
     */
    private Integer getCompanyId(HttpSession session) {
        return (Integer)session.getAttribute(Constant.COMPANY_ID);
    }

    /**
     * 构建模板路径
     * @param session
     * @param viewName
     * @return
     */
    private String buildTemplatePath(HttpSession session, String viewName) {
        Integer companyId = getCompanyId(session);
        return templateService.buildTemplatePath(companyId, viewName);
    }

    /**
     * 游客跳转到OMS
     * @param page
     * @return
     */
    @GetMapping(value = "/itsoms/{page}")
    public String itsoms(HttpSession session, @PathVariable String page){
        return buildTemplatePath(session, "index/itsoms/" + page + ".html");
    }


}
