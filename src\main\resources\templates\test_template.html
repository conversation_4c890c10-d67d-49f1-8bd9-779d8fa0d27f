<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .template-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .current-template {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">🎨 模板测试工具</h1>
        
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header">
                        <h3>模板切换测试</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button class="btn btn-info" onclick="getCurrentTemplate()">查看当前模板</button>
                            <button class="btn btn-success" onclick="window.open('/', '_blank')">打开首页</button>
                        </div>
                        
                        <div id="currentTemplateInfo" class="alert alert-info" style="display: none;"></div>
                        
                        <h4>可用模板列表：</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="template-card">
                                    <h5>默认模板 (default)</h5>
                                    <p>经典企业门户模板，稳重大气</p>
                                    <button class="btn btn-primary btn-sm" onclick="setTemplate('default')">切换到此模板</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="template-card">
                                    <h5>现代风格 (modern)</h5>
                                    <p>现代简约设计，适合科技企业</p>
                                    <button class="btn btn-primary btn-sm" onclick="setTemplate('modern')">切换到此模板</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="template-card">
                                    <h5>商务风格 (business)</h5>
                                    <p>专业商务设计，适合传统企业</p>
                                    <button class="btn btn-primary btn-sm" onclick="setTemplate('business')">切换到此模板</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="template-card">
                                    <h5>创意风格 (creative)</h5>
                                    <p>创意设计，适合创新型企业</p>
                                    <button class="btn btn-primary btn-sm" onclick="setTemplate('creative')">切换到此模板</button>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="template-card" style="background-color: #fff3cd; border-color: #ffeaa7;">
                                    <h5>🆕 商业专业版 (moban6072)</h5>
                                    <p>高端商业模板，专业企业形象 - <strong>新增模板</strong></p>
                                    <button class="btn btn-warning btn-sm" onclick="setTemplate('moban6072')">切换到此模板</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h4>测试步骤：</h4>
                            <ol>
                                <li>点击"查看当前模板"查看当前使用的模板</li>
                                <li>选择一个模板点击"切换到此模板"</li>
                                <li>点击"打开首页"查看效果</li>
                                <li>重复步骤2-3测试不同模板</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function getCurrentTemplate() {
            fetch('/test/template')
                .then(response => response.json())
                .then(data => {
                    const info = document.getElementById('currentTemplateInfo');
                    info.innerHTML = `
                        <h5>当前模板信息：</h5>
                        <p><strong>公司ID：</strong> ${data.companyId}</p>
                        <p><strong>当前模板：</strong> ${data.currentTemplate}</p>
                        <p><strong>模板路径：</strong> ${data.templatePath}</p>
                        <p><strong>可用模板：</strong> ${data.availableTemplates.join(', ')}</p>
                    `;
                    info.style.display = 'block';
                })
                .catch(error => {
                    alert('获取模板信息失败：' + error);
                });
        }

        function setTemplate(templateCode) {
            if (confirm(`确定要切换到 ${templateCode} 模板吗？`)) {
                fetch(`/test/setTemplate?template=${templateCode}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('模板切换成功！请打开首页查看效果。');
                            getCurrentTemplate(); // 刷新当前模板信息
                        } else {
                            alert('模板切换失败：' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('网络错误：' + error);
                    });
            }
        }

        // 页面加载时自动获取当前模板信息
        window.onload = function() {
            getCurrentTemplate();
        };
    </script>
</body>
</html>
