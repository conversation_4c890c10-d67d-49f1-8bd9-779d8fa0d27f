<!doctype html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title th:text="${product.title} + 'APP下载页'"/>
    <link rel="shortcut icon" href="/static/index/img/favicon.ico">
    <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700&amp;subset=latin-ext" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/static/index/app/assets/css/plugins.css">
    <link rel="stylesheet" type="text/css" href="/static/index/app/assets/css/main.css">
    <style>
        .tip_p{color: #fff;font-weight: 300;position: absolute;z-index: 99;right: 15px;top: 15px;}
    </style>
</head>
<body>
<p class="tip_p">右上角点击打开浏览器下载 <i class="fa fa-location-arrow"></i></p>
<div class="preloader">
    <div class="spinner">
        <div class="bounce-1"></div>
        <div class="bounce-2"></div>
        <div class="bounce-3"></div>
    </div>
</div>
<div class="hero">
    <div class="front-content">
        <div class="container-fluid">
            <div class="col-sm-5 full-height">
                <div class="container-mid">
                    <div class="animation-container animation-fade-right" data-animation-delay="0">
                        <div class="phone-slider owl-carousel">
                            <img src="/static/index/app/assets/img/slides/leftApp1.jpg" alt="image">
                            <img src="/static/index/app/assets/img/slides/leftApp2.jpg" alt="image">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-7 text-left full-height">

                <div class="container-mid">
                    <div class="animation-container animation-fade-down" data-animation-delay="0">

                        <img class="img-responsive logo" src="/static/index/img/logo.png" width="200" alt="image">

                    </div>
                    <div class="animation-container animation-fade-left" data-animation-delay="200">

                        <p class="subline" th:text="${product.remark}"/>

                    </div>
                    <div class="animation-container animation-fade-up" data-animation-delay="400">
                        <a class="download-button" th:href="'/UploadFilePath/product/' + ${product.android}" th:if="${product.isOpenA eq '1'}">
                            <i class="fa fa-android" aria-hidden="true"></i>安卓下载
                        </a>
                        <a class="download-button" th:href="'/UploadFilePath/product/' + ${product.ios}" th:if="${product.isOpenI eq '1'}">
                            <i class="fa fa-apple" aria-hidden="true"></i>IOS下载
                        </a>
                    </div>
                </div>
            </div>


        </div>
        <div class="footer">
            <div class="container-fluid">
                <!--<div class="col-xs-6 text-left">
                    <p class="popup-links"><a href="#" data-featherlight="#popup-faq">介绍1</a>
                        &amp; <a href="#" data-featherlight="#popup-privacy-terms">介绍2</a></p>
                </div>
                <div class="col-xs-6 text-right">
                    <p><a href="http://www.guacloud.com/">瓜云</a> </p>
                </div>-->
            </div>
        </div>
    </div>

    <div class="background-content parallax-on">
        <div class="background-content-inner">

            <div class="background-overlay"></div>
            <div class="background-img layer" data-depth="0.05"></div>

        </div>

    </div>


</div>

<div id="popup-faq" class="popup">

    <div class="container-fluid">


        <h4>介绍1</h4>

        <div class="item">

            <p class="question">问题1？</p>
            <p class="answer">回答1！</p>

        </div>
        <div class="item">

            <p class="question">问题2？</p>
            <p class="answer">回答2！</p>

        </div>


    </div>


</div>
<div id="popup-privacy-terms" class="popup">
    <div class="container-fluid">


        <h4>介绍2</h4>

        <div class="item">

            <p class="headline">问题1？</p>
            <p>回答1！</p>

        </div>
        <div class="item">

            <p class="headline">问题2？</p>
            <p>回答2！</p>

        </div>

    </div>


</div>
<script type="text/javascript" src="/static/index/app/assets/js/plugins.js"></script>
<script type="text/javascript" src="/static/index/app/assets/js/main.js"></script>


</body>


</html>