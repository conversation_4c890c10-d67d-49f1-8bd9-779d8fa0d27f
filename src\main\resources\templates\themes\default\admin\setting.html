<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>

<link rel="stylesheet" href="/static/admin/simditor/css/simditor.css">
<script src="/static/admin/simditor/js/module.min.js"></script>
<script src="/static/admin/simditor/js/hotkeys.min.js"></script>
<script src="/static/admin/simditor/js/uploader.min.js"></script>
<script src="/static/admin/simditor/js/simditor.min.js"></script>
<script src="/static/admin/simditor/js/jquery.form.js"></script>
<style>
    .m{ width: 100%; margin-left: auto; margin-right: auto; }
    .note-editor.note-frame {border: 1px solid #eee!important;}
    .note-editor .note-editing-area {min-height: 500px;}
</style>

<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('系统管理', '系统设置')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">

            <div class="container-fluid">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <ul class="nav nav-tabs page-tabs">
                                <li class="active"> <a href="javascript:void(0)">基本</a> </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <div class="edit-form">
                                        <div class="form-group">
                                            <label>网站名称</label>
                                            <input class="form-control" type="text" id="website_title" th:value="${configs.get('website_title').value}" placeholder="请输入网站名称" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>网站域名</label>
                                            <input class="form-control" type="text" id="website_domain" th:value="${configs.get('website_domain').value}" placeholder="本网站域名（前面不加http://或https://），如：qxhz.gw.goto56.com" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>会员系统地址（OMS）</label>
                                            <input class="form-control" type="text" id="oms_url" th:value="${configs.get('oms_url').value}" placeholder="客户登录入口。如：http://abc.oms.goto56.com" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>会员系统编号（OMS）</label>
                                            <input class="form-control" type="text" id="oms_company_id" th:value="${configs.get('oms_company_id').value}" placeholder="请输入会员系统编号" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Seo关键字</label>
                                            <input class="form-control" type="text" id="seo_keywords" th:value="${configs.get('seo_keywords').value}" placeholder="请输入Seo关键字" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Seo描述</label>
                                            <input class="form-control" type="text" id="seo_description" th:value="${configs.get('seo_description').value}" placeholder="请输入Seo描述" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>公司名称</label>
                                            <input class="form-control" type="text" id="website_company" th:value="${configs.get('website_company').value}" placeholder="请输入公司名称" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>联系人</label>
                                            <input class="form-control" type="text" id="website_admin" th:value="${configs.get('website_admin').value}" placeholder="请输入联系人" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>联系电话</label>
                                            <input class="form-control" type="text" id="website_phone" th:value="${configs.get('website_phone').value}" placeholder="请输入联系电话" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>公司邮箱</label>
                                            <input class="form-control" type="text" id="website_email" th:value="${configs.get('website_email').value}" placeholder="请输入公司邮箱" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>公司传真</label>
                                            <input class="form-control" type="text" id="website_fax" th:value="${configs.get('website_fax').value}" placeholder="请输入公司传真" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>公司地址</label>
                                            <input class="form-control" type="text" id="website_address" th:value="${configs.get('website_address').value}" placeholder="请输入公司地址" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>备案号</label>
                                            <input class="form-control" type="text" id="website_record" th:value="${configs.get('website_record').value}" placeholder="请输入备案号" autocomplete="off" required>
                                        </div>
                                        <!--<div class="form-group">-->
                                            <!--<label>微信公众号二维码预览（点击上传图片）</label>-->
                                            <!--<img id="previewImg" style="cursor: pointer;" th:src="'/UploadFilePath/vx/' + ${configs.get('website_qrcode').value}" th:error="'/UploadFilePath/error.jpg'" height="250">-->
                                        <!--</div>-->
                                        <!--<div class="form-group">-->
                                            <!--<label>微信公众号</label>-->
                                            <!--<input class="form-control" type="text" id="website_qrcode" th:value="${configs.get('website_qrcode').value}" placeholder="请输入微信公众号" autocomplete="off" required readonly>-->
                                        <!--</div>-->

                                        <div class="form-group">
                                            <label>网站Logo（点击上传图片）</label>
                                            <img id="previewImg" style="cursor: pointer;" th:src="'/UploadFilePath/logo/' + ${configs.get('website_logo').value}" height="50">
                                            <input class="form-control" type="hidden" id="website_logo" th:value="${configs.get('website_logo').value}" placeholder="logo" autocomplete="off">
                                        </div>

                                        <div class="form-group">
                                            <button class="btn btn-primary m-r-5 down">确 定</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form id='uploadFileForm' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadPhoto" value="点击上传图片"/>
                <input type="text" name="fileType" value="logo">
            </form>
        </main>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(e) {

        /**
         * down - click
         */
        $(".down").on('click',function() {
            var target = {
                oms_url : $("#oms_url").val(),
                oms_company_id : $("#oms_company_id").val(),
                website_logo : $("#website_logo").val(),
                website_title : $("#website_title").val(),
                website_domain : $("#website_domain").val(),
                seo_keywords : $("#seo_keywords").val(),
                seo_description : $("#seo_description").val(),
                website_company : $("#website_company").val(),
                website_admin : $("#website_admin").val(),
                website_phone : $("#website_phone").val(),
                website_email : $("#website_email").val(),
                website_fax : $("#website_fax").val(),
                website_address : $("#website_address").val(),
                website_record : $("#website_record").val(),
                website_qrcode : $("#website_qrcode").val()
            };

            $.ajax({
                url: "/Admin/configAction",
                type: "POST",
                dataType: "json",
                contentType:"application/json;charset=UTF-8",
                data: JSON.stringify(target),
                success: function(data) {
                    if(data.status == true){
                        alert("保存成功!");
                        window.location.href = "/Admin/index.html";
                    }
                    else{
                        alert("保存失败!");
                    }
                },
                error: function (xhr) {
                    alert(xhr);
                }
            });
        });

        /**
         * previewImg - click
         */
        $("#previewImg").click(function() {
            $("#uploadPhoto").click();
        });

        /**
         * uploadPhoto - change
         */
        $("#uploadPhoto").change(function() {
            var website_logo = $("#website_logo");
            $("#uploadFileForm").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        $("#previewImg").prop("src",'/UploadFilePath/logo/' + newFileName);
                        website_logo.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });
    });




</script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>