package com.th.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class Project {
    private int id;
    private Integer companyId;
    private String title;
    private String remark;
    private String content;
    private String picture;
    private Timestamp createTime;
    private Timestamp updateTime;
}
