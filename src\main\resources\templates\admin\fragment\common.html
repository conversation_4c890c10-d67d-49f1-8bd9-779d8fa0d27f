<!doctype html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

    <!-- 页头 -->
    <head th:fragment="head">
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <title th:text="${configs.get('website_title').value + '丨后台管理系统'}"/>
        <meta name="author" content="Vegeta"/>
        <meta name="keywords" th:content="${configs.get('seo_keywords').value}"/>
        <meta name="description" th:content="${configs.get('seo_description').value}"/>
        <link rel="shortcut icon" href="/static/index/img/favicon.ico">
        <script type="text/javascript" src="/static/index/js/jquery-2.1.4.min.js"></script>
        <link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
        <link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
        <link href="/static/admin/css/style.min.css" rel="stylesheet">
    </head>

    <!-- 左侧导航 -->
    <aside class="lyear-layout-sidebar" th:fragment="aside">
        <!-- logo -->
        <div id="logo" class="sidebar-header">
            <a href="javascript:void(0)" style="padding: 20px 0;font-size: 18px;letter-spacing: 1px;color: rgb(46,160,209) !important;" th:text="${configs.get('website_title').value}"/>
        </div>
        <div class="lyear-layout-sidebar-scroll">
            <nav class="sidebar-main">
                <ul class="nav nav-drawer">
                    <li class="nav-item nav-item-has-subnav">
                        <a href="javascript:void(0)" href-data="index.html,setting.html,slider.html,about.html,scope.html,civilization.html,setup.html,talent.html,log.html"><i class="mdi mdi-home"></i>系统管理</a>
                        <ul class="nav nav-subnav">
                            <li> <a href="/Admin/index.html"> <i class="mdi mdi-link"></i> 系统信息</a> </li>
                            <li> <a href="/Admin/setting.html"> <i class="mdi mdi-link"></i> 系统设置</a> </li>
                            <li> <a href="/Admin/slider.html"> <i class="mdi mdi-link"></i> 首页设置</a> </li>
                            <li> <a href="/Admin/about.html"> <i class="mdi mdi-link"></i> 公司简介</a> </li>
                            <li> <a href="/Admin/scope.html"> <i class="mdi mdi-link"></i> 经营范围</a> </li>
                            <li> <a href="/Admin/civilization.html"> <i class="mdi mdi-link"></i> 企业文化</a> </li>
                            <li> <a href="/Admin/setup.html"> <i class="mdi mdi-link"></i> 人才体系</a> </li>
                            <li> <a href="/Admin/talent.html"> <i class="mdi mdi-link"></i> 人才招聘</a> </li>
                            <li> <a href="/Admin/log.html"> <i class="mdi mdi-link"></i> 日志信息</a> </li>
                            <li> <a href="/Admin/password.html"> <i class="mdi mdi-link"></i> 修改密码</a> </li>
                        </ul>
                    </li>

                    <li class="nav-item nav-item-has-subnav">
                        <a href="javascript:void(0)" href-data="project.html,addProject.html,updateProject.html"><i class="mdi mdi-unity"></i>渠道管理</a>
                        <ul class="nav nav-subnav">
                            <li> <a href="/Admin/project.html"> <i class="mdi mdi-link"></i> 渠道列表</a> </li>
                            <li> <a href="/Admin/addProject.html"> <i class="mdi mdi-link"></i> 添加渠道</a> </li>
                        </ul>
                    </li>

                    <li class="nav-item nav-item-has-subnav">
                        <a href="javascript:void(0)" href-data="news.html,addNews.html,updateNews.html"><i class="mdi mdi-newspaper"></i>资讯管理</a>
                        <ul class="nav nav-subnav">
                            <li> <a href="/Admin/news.html"> <i class="mdi mdi-link"></i> 资讯列表</a> </li>
                            <li> <a href="/Admin/addNews.html"> <i class="mdi mdi-link"></i> 添加资讯</a> </li>
                        </ul>
                    </li>

                    <!--<li class="nav-item nav-item-has-subnav">-->
                            <!--<a href="javascript:void(0)" href-data="product.html,addProduct.html,updateProduct.html"><i class="mdi mdi-orbit"></i>产品管理</a>-->
                        <!--<ul class="nav nav-subnav">-->
                            <!--<li> <a href="/Admin/product.html"> <i class="mdi mdi-link"></i> 产品列表</a> </li>-->
                            <!--<li> <a href="/Admin/addProduct.html"> <i class="mdi mdi-link"></i> 添加产品</a> </li>-->
                        <!--</ul>-->
                    <!--</li>-->

                    <li class="nav-item nav-item-has-subnav">
                        <a href="javascript:void(0)" href-data="partner.html,addPartner.html,updatePartner.html"><i class="mdi mdi-human-greeting"></i>客户管理</a>
                        <ul class="nav nav-subnav">
                            <li> <a href="/Admin/partner.html"> <i class="mdi mdi-link"></i> 客户列表</a> </li>
                            <li> <a href="/Admin/addPartner.html"> <i class="mdi mdi-link"></i> 添加客户</a> </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
        <script>
            // 选中菜单
            var uri = window.location.pathname;
            var uriArray = uri.split("/");

            if (uri != null && uri != "") {
                // 获取 nav-drawer 第一层的 li
                var lis = $(".nav-drawer > li");
                lis.each(function(e) {
                    var li = $(this);
                    var a = li.find("a");
                    var href = a.attr("href-data");

                    if (href.indexOf(uriArray[2]) != -1) {
                        li.addClass("active open");
                        var li2s = li.find("li")
                        li2s.each(function(e) {
                            var li2 = $(this)
                            var a2 = li2.find("a");
                            var href2 = a2.attr("href");
                            if (href2.indexOf(uriArray[2]) != -1) {
                                li2.addClass("active");
                            }
                        })
                    }
                });
            }
        </script>
    </aside>

    <!-- 头部信息 -->
    <header class="lyear-layout-header" th:fragment="header(parent, children)">
        <nav class="navbar navbar-default">
            <div class="topbar">
                <div class="topbar-left">
                    <div class="lyear-aside-toggler">
                        <span class="lyear-toggler-bar"></span>
                        <span class="lyear-toggler-bar"></span>
                        <span class="lyear-toggler-bar"></span>
                    </div>
                    <span class="navbar-page-title" th:text="' ' + ${parent} + ''" th:if="${children eq ''}"/>
                    <span class="navbar-page-title" th:text="' ' + ${parent} + ' / ' + ${children}" th:if="${children ne ''}"/>
                </div>
                <ul class="topbar-right">
                    <li class="dropdown dropdown-profile">
                        <a href="javascript:void(0)" data-toggle="dropdown">
                            <span th:text="'欢迎系统管理员！' + ${session.admin}"/>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li><a href="/Index/index.html" target="_blank"><i class="mdi mdi-home"></i> 前台网站</a></li>
                            <li><a href="/Admin/loginOut"><i class="mdi mdi-logout-variant"></i> 退出登录</a></li>
                        </ul>
                    </li>
                    <!-- 切换主题配色 -->
                    <li class="dropdown dropdown-skin">
                        <span data-toggle="dropdown" class="icon-palette"><i class="mdi mdi-palette"></i></span>
                        <ul class="dropdown-menu dropdown-menu-right" data-stopPropagation="true">
                            <li class="drop-title"><p>主题</p></li>
                            <li class="drop-skin-li clearfix">
                                <span class="inverse">
                                    <input type="radio" name="site_theme" value="default" id="site_theme_1" checked>
                                    <label for="site_theme_1"></label>
                                </span>
                                    <span>
                                    <input type="radio" name="site_theme" value="dark" id="site_theme_2">
                                    <label for="site_theme_2"></label>
                                </span>
                                    <span>
                                    <input type="radio" name="site_theme" value="translucent" id="site_theme_3">
                                    <label for="site_theme_3"></label>
                                </span>
                            </li>
                            <li class="drop-title"><p>LOGO</p></li>
                            <li class="drop-skin-li clearfix">
                                <span class="inverse">
                                    <input type="radio" name="logo_bg" value="default" id="logo_bg_1" checked>
                                    <label for="logo_bg_1"></label>
                                </span>
                                <span>
                                    <input type="radio" name="logo_bg" value="color_2" id="logo_bg_2">
                                    <label for="logo_bg_2"></label>
                                </span>
                                <span>
                                    <input type="radio" name="logo_bg" value="color_3" id="logo_bg_3">
                                    <label for="logo_bg_3"></label>
                                </span>
                                <span>
                                    <input type="radio" name="logo_bg" value="color_4" id="logo_bg_4">
                                    <label for="logo_bg_4"></label>
                                </span>
                                <span>
                                    <input type="radio" name="logo_bg" value="color_5" id="logo_bg_5">
                                    <label for="logo_bg_5"></label>
                                </span>
                                <span>
                                    <input type="radio" name="logo_bg" value="color_6" id="logo_bg_6">
                                    <label for="logo_bg_6"></label>
                                </span>
                                <span>
                                    <input type="radio" name="logo_bg" value="color_7" id="logo_bg_7">
                                    <label for="logo_bg_7"></label>
                                </span>
                                <span>
                                    <input type="radio" name="logo_bg" value="color_8" id="logo_bg_8">
                                    <label for="logo_bg_8"></label>
                                </span>
                            </li>
                            <li class="drop-title"><p>头部</p></li>
                            <li class="drop-skin-li clearfix">
                              <span class="inverse">
                                <input type="radio" name="header_bg" value="default" id="header_bg_1" checked>
                                <label for="header_bg_1"></label>
                              </span>
                                <span>
                                    <input type="radio" name="header_bg" value="color_2" id="header_bg_2">
                                    <label for="header_bg_2"></label>
                                </span>
                                <span>
                                    <input type="radio" name="header_bg" value="color_3" id="header_bg_3">
                                    <label for="header_bg_3"></label>
                                </span>
                                <span>
                                    <input type="radio" name="header_bg" value="color_4" id="header_bg_4">
                                    <label for="header_bg_4"></label>
                                </span>
                                <span>
                                    <input type="radio" name="header_bg" value="color_5" id="header_bg_5">
                                    <label for="header_bg_5"></label>
                                </span>
                                <span>
                                    <input type="radio" name="header_bg" value="color_6" id="header_bg_6">
                                    <label for="header_bg_6"></label>
                                </span>
                                <span>
                                    <input type="radio" name="header_bg" value="color_7" id="header_bg_7">
                                    <label for="header_bg_7"></label>
                                </span>
                                <span>
                                    <input type="radio" name="header_bg" value="color_8" id="header_bg_8">
                                    <label for="header_bg_8"></label>
                                </span>
                            </li>
                            <li class="drop-title"><p>侧边栏</p></li>
                            <li class="drop-skin-li clearfix">
                                <span class="inverse">
                                    <input type="radio" name="sidebar_bg" value="default" id="sidebar_bg_1" checked>
                                    <label for="sidebar_bg_1"></label>
                                </span>
                                <span>
                                    <input type="radio" name="sidebar_bg" value="color_2" id="sidebar_bg_2">
                                    <label for="sidebar_bg_2"></label>
                                </span>
                                <span>
                                    <input type="radio" name="sidebar_bg" value="color_3" id="sidebar_bg_3">
                                    <label for="sidebar_bg_3"></label>
                                </span>
                                <span>
                                    <input type="radio" name="sidebar_bg" value="color_4" id="sidebar_bg_4">
                                    <label for="sidebar_bg_4"></label>
                                </span>
                                <span>
                                    <input type="radio" name="sidebar_bg" value="color_5" id="sidebar_bg_5">
                                    <label for="sidebar_bg_5"></label>
                                </span>
                                <span>
                                    <input type="radio" name="sidebar_bg" value="color_6" id="sidebar_bg_6">
                                    <label for="sidebar_bg_6"></label>
                                </span>
                                <span>
                                    <input type="radio" name="sidebar_bg" value="color_7" id="sidebar_bg_7">
                                    <label for="sidebar_bg_7"></label>
                                </span>
                                <span>
                                    <input type="radio" name="sidebar_bg" value="color_8" id="sidebar_bg_8">
                                    <label for="sidebar_bg_8"></label>
                                </span>
                            </li>
                        </ul>
                    </li>
                    <!--/ 切换主题配色 -->
                </ul>

            </div>
        </nav>

    </header>
</html>