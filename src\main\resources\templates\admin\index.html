<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>
<style>
    .card {margin-bottom: 24px;background-color: #fff;-webkit-box-shadow: none;box-shadow: none;}
    .content_box_body .div_p > span {width: 75px;text-align: justify;text-align-last: justify;;display: inline-block;color: #000;font-weight: bold;}
    .content_box_body .div_p > p {font-size: 14px;line-height: 30px;color: #54667a;text-indent: 30px;display: inline-block;}
</style>
<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('系统管理', '系统信息')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <ul class="nav nav-tabs page-tabs">
                                <li class="active"> <a href="javascript:void(0)">系统信息</a> </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <div class="content_box_body">
                                        <!--<div class="div_p">-->
                                            <!--<span>开发语言</span>：-->
                                            <!--<p th:text="'Java'"/>-->
                                        <!--</div>-->
                                        <!--<div class="div_p">-->
                                            <!--<span>JDK版本</span>：-->
                                            <!--<p th:text="'1.8'"/>-->
                                        <!--</div>-->
                                        <!--<div class="div_p">-->
                                            <!--<span>开发工具</span>：-->
                                            <!--<p th:text="'IDEA'"/>-->
                                        <!--</div>-->
                                        <!--<div class="div_p">-->
                                            <!--<span>服务器</span>：-->
                                            <!--<p th:text="'Apache Tomcat 服务器'"/>-->
                                        <!--</div>-->
                                        <!--<div class="div_p">-->
                                            <!--<span>数据库</span>：-->
                                            <!--<p th:text="'MySQL 5.6.17 数据库服务器'"/>-->
                                        <!--</div>-->
                                        <div class="div_p">
                                            <span>网站名称</span>：
                                            <p th:text="${configs.get('website_title').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>会员系统地址（OMS）</span>：
                                            <p th:text="${configs.get('oms_url').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>会员系统编号（OMS）</span>：
                                            <p th:text="${configs.get('oms_company_id').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>Seo关键字</span>：
                                            <p th:text="${configs.get('seo_keywords').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>Seo描述</span>：
                                            <p th:text="${configs.get('seo_description').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>网站域名</span>：
                                            <p th:text="${configs.get('website_domain').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>公司名称</span>：
                                            <p th:text="${configs.get('website_company').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>联系人</span>：
                                            <p th:text="${configs.get('website_admin').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>联系电话</span>：
                                            <p th:text="${configs.get('website_phone').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>公司邮箱</span>：
                                            <p th:text="${configs.get('website_email').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>公司传真</span>：
                                            <p th:text="${configs.get('website_fax').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>公司地址</span>：
                                            <p th:text="${configs.get('website_address').value}"/>
                                        </div>
                                        <div class="div_p">
                                            <span>备案号</span>：
                                            <p th:text="${configs.get('website_record').value}"/>
                                        </div>
                                        <div class="form-group">
                                            <label>网站Logo</label>
                                            <img id="previewImg" style="cursor: pointer;" th:src="'/UploadFilePath/logo/' + ${configs.get('website_logo').value}" height="50">
                                        </div>
                                        <!--<div class="div_p">-->
                                            <!--<span>微信公众号</span>：-->
                                            <!--<p>-->
                                                <!--<img th:src="'/UploadFilePath/vx/' + ${configs.get('website_qrcode').value}" ondragstart="return false" width="100"/>-->
                                            <!--</p>-->
                                        <!--</div>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>