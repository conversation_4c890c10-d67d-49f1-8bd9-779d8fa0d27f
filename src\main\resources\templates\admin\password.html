<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>

<link rel="stylesheet" href="/static/admin/simditor/css/simditor.css">
<script src="/static/admin/simditor/js/module.min.js"></script>
<script src="/static/admin/simditor/js/hotkeys.min.js"></script>
<script src="/static/admin/simditor/js/uploader.min.js"></script>
<script src="/static/admin/simditor/js/simditor.min.js"></script>
<script src="/static/admin/simditor/js/jquery.form.js"></script>
<style>
    .m{ width: 100%; margin-left: auto; margin-right: auto; }
    .note-editor.note-frame {border: 1px solid #eee!important;}
    .note-editor .note-editing-area {min-height: 500px;}
</style>

<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('系统管理', '修改密码')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">

            <div class="container-fluid">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <ul class="nav nav-tabs page-tabs">
                                <li class="active"> <a href="javascript:void(0)">基本</a> </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <div class="edit-form">
                                        <div class="form-group">
                                            <label>账号</label>
                                            <input class="form-control" type="text" id="username" value="admin" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label>原密码</label>
                                            <input class="form-control" type="password" id="oldPassword" value="" placeholder="请输入原密码" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>新密码</label>
                                            <input class="form-control" type="password" id="password" value="" placeholder="请输入新密码" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>确认密码</label>
                                            <input class="form-control" type="password" id="confirmPassword" value="" placeholder="请再次输入新密码" autocomplete="off" required>
                                        </div>

                                        <div class="form-group">
                                            <button class="btn btn-primary m-r-5 down">确 定</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(e) {

        /**
         * down - click
         */
        $(".down").on('click',function() {
            var oldPassword = $("#oldPassword").val()
            var password = $("#password").val()
            var confirmPassword = $("#confirmPassword").val()

            if (password != confirmPassword) {
                alert("两次输入的新密码不一致");
                return false;
            }
            var target = {
                oldPassword : oldPassword,
                password : password
            };

            $.ajax({
                url: "/Admin/updatePassword",
                type: "POST",
                dataType: "json",
                contentType:"application/json;charset=UTF-8",
                data: JSON.stringify(target),
                success: function(data) {
                    if(data.status == true){
                        alert("保存成功!");
                    }
                    else{
                        alert("保存失败!" + data.msg);
                    }
                },
                error: function (xhr) {
                    alert(xhr);
                }
            });
        });

        /**
         * previewImg - click
         */
        $("#previewImg").click(function() {
            $("#uploadPhoto").click();
        });

        /**
         * uploadPhoto - change
         */
        $("#uploadPhoto").change(function() {
            var website_qrcode = $("#website_qrcode");
            $("#uploadFileForm").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        $("#previewImg").prop("src",'/UploadFilePath/vx/' + newFileName);
                        website_qrcode.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });
    });




</script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>