<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{index/fragment/common :: head}"></div>
<body>

<!-- 欢迎 -->
<div th:replace="~{index/fragment/common :: welcome}"></div>

<!-- 导航 -->
<div th:replace="~{index/fragment/common :: header}"></div>

<!-- 图片轮播 -->
<div th:replace="~{index/fragment/common :: jslider}"></div>

<!-- 内容 -->
<div class="pst_bg">
    <div class="pst">
        <i class="fa fa-home"></i>
        您当前的位置：
        <a href="/Download/download.html">下载中心</a>
        <i class="fa fa-arrow-right"></i>
        <a href="/Download/download.html">产品列表</a>
    </div>
</div>
<div class="scd clearfix">
    <div class="scd_l">
        <div class="s_name">
            下载中心
        </div>
        <div th:replace="~{index/fragment/common :: product_slide}"></div>
    </div>
    <div class="scd_r">
        <div class="r_name"><span>产品列表</span></div>
        <div class="new">
            <dl class="clearfix" th:each="product : ${pageInfo.getList()}">
                <dt><img th:src="'/UploadFilePath/product/' + ${product.picture}" th:alt="${product.title}"></dt>
                <dd>
                    <div class="title">
                        <a th:href="'/Download/product/id/' + ${product.id} + '.html'" target="_blank">
                            <p th:text="${product.title}"/>
                            <em th:text="'[' + ${#dates.format(product.updateTime,'yyyy-MM-dd')} + ']'"/>
                        </a>
                    </div>
                    <div class="des">通过硬件设备采集体温、血氧、血压、脉搏、呼吸值等参数，上传App以及发送至医院平台。</div>
                    <a th:href="'/Download/product/id/' + ${product.id} + '.html'" target="_blank" class="more1">点击下载</a>
                </dd>
            </dl>

            <div class="space_hx">&nbsp;</div>
            <div class="pages">
                <a th:href="@{/Download/download.html(pageNo=${pageInfo.getPrePage()})}" class="prev" th:if="${pageInfo.isHasPreviousPage()} eq true">上一页</a>
                <a th:href="'javascript:void(0)'" class="prev" style="cursor: not-allowed" th:if="${pageInfo.isHasPreviousPage()} eq false">上一页</a>

                <a class="now" href="javascript:void(0)" th:text="${pageInfo.pageNum}"/>

                <a th:href="@{/Download/download.html(pageNo=${pageInfo.getNextPage()})}" class="next" th:if="${pageInfo.isHasNextPage()} eq true">下一页</a>
                <a th:href="'javascript:void(0)'" class="next" style="cursor: not-allowed" th:if="${pageInfo.isHasNextPage()} eq false">下一页</a>
            </div>
        </div>
    </div>
</div>

<!-- 页脚与备案 -->
<div th:replace="~{index/fragment/common :: copyright}"></div>

</body>
</html>
