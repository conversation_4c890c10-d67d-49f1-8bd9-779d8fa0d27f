package com.th.service;

import com.th.pojo.*;
import com.th.mapper.AdminMapper;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;

@Service
public class AdminService {

    @Autowired
    public AdminMapper adminMapper;

    public ArrayList<Config> getConfigs(Integer companyId) {return adminMapper.getConfigs(companyId);}

    public Page<News> getNews(String search, Integer companyId) {return adminMapper.getNews(search, companyId);}

    public Integer addNewsAction(News news) {return adminMapper.addNewsAction(news);}

    public Integer deleteNews(Integer id) {return adminMapper.deleteNews(id);}

    public News selectNewsById(Integer id) {return adminMapper.selectNewsById(id);}

    public boolean updateNewsAction(News news) {return adminMapper.updateNewsAction(news);}

    public Page<Project> getProject(String search, Integer companyId) {return adminMapper.getProject(search, companyId);}

    public boolean addProjectAction(Project project) {return adminMapper.addProjectAction(project);}

    public Integer deleteProject(int id) {return adminMapper.deleteProject(id);}

    public Project selectProjectById(int id) {return adminMapper.selectProjectById(id);}

    public boolean updateProjectAction(Project project) {return adminMapper.updateProjectAction(project);}

    public Page<Partner> getPartner(String search, Integer companyId) {return adminMapper.getPartner(search, companyId);}

    public boolean addPartnerAction(Partner partner) {return adminMapper.addPartnerAction(partner);}

    public Integer deletePartner(Integer id) {return adminMapper.deletePartner(id);}

    public Partner selectPartnerById(Integer id) {return adminMapper.selectPartnerById(id);}

    public boolean updatePartnerAction(Partner partner) {return adminMapper.updatePartnerAction(partner);}

    public Page<Product> getProduct(String search, Integer companyId) {return adminMapper.getProduct(search, companyId);}

    public boolean addProductAction(Product product) {return adminMapper.addProductAction(product);}

    public Integer deleteProduct(Integer id) {return adminMapper.deleteProduct(id);}

    public Product selectProductById(Integer id) {return adminMapper.selectProductById(id);}

    public boolean updateProductAction(Product product) {return adminMapper.updateProductAction(product);}

    public Config getConfig(String key, Integer companyId) {
        return adminMapper.getConfig(key, companyId);
    }
    public boolean saveConfig(String key, String value, Integer companyId) {return adminMapper.saveConfig(key, value, companyId);}

    public boolean updateConfig(String key, String value, Integer companyId) {return adminMapper.updateConfig(key, value, companyId);}

    public Page<Log> getLog(String search, Integer companyId) {return adminMapper.getLog(search, companyId);}

    public boolean recordLog(String ip, String os, String browser, String root, Timestamp createTime, Integer companyId) {return adminMapper.recordLog(ip, os, browser, root, createTime, companyId);}
}
