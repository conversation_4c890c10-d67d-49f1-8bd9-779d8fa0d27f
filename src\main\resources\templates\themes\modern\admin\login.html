<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <title th:text="${configs.get('website_title').value} + '丨后台管理系统'"/>
    <meta name="author" content="Vegeta"/>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link rel="shortcut icon" href="/static/index/img/favicon.ico">
    <link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="/static/admin/css/style.min.css" rel="stylesheet">
    <script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
    <style>
        .lyear-wrapper {
            position: relative;
        }
        .lyear-login {
            display: flex !important;
            min-height: 100vh;
            align-items: center !important;
            justify-content: center !important;
        }
        .login-center {
            background: #fff;
            min-width: 38.25rem;
            padding: 2.14286em 3.57143em;
            border-radius: 5px;
            margin: 2.85714em 0;
        }
        .login-header {
            margin-bottom: 1.5rem !important;
        }
        .login-header span {
            font-size: 22px;
            font-weight: normal;
        }
        .login-center .has-feedback.feedback-left .form-control {
            padding-left: 38px;
            padding-right: 12px;
        }
        .login-center .has-feedback.feedback-left .form-control-feedback {
            left: 0;
            right: auto;
            width: 38px;
            height: 38px;
            line-height: 38px;
            z-index: 4;
            color: #dcdcdc;
        }
        .login-center .has-feedback.feedback-left.row .form-control-feedback {
            left: 15px;
        }
    </style>
</head>

<body>
<div class="row lyear-wrapper">
    <div class="lyear-login">
        <div class="login-center">
            <div class="login-header text-center">
                <span th:text="${configs.get('website_title').value} + '丨后台管理系统'"/>
            </div>
            <form action="/Admin/loginAction" method="post">
                <div class="form-group has-feedback feedback-left">
                    <input type="text" placeholder="请输入管理员账号" class="form-control" value="" name="username" id="username" autocomplete="off"/>
                    <span class="mdi mdi-account form-control-feedback" aria-hidden="true"></span>
                </div>
                <div class="form-group has-feedback feedback-left">
                    <input type="password" placeholder="请输入管理员密码" class="form-control" value="" id="password" name="password" autocomplete="off"/>
                    <span class="mdi mdi-lock form-control-feedback" aria-hidden="true"></span>
                </div>
                <div class="form-group">
                    <button class="btn btn-block btn-primary" type="submit">立即登录</button>
                </div>
            </form>
            <hr>
            <p class="message" style="text-align: center;color: red;" th:text="${msg}"/>
            <footer class="col-sm-12 text-center">
                <div class="m-b-0 sj">Copyright © 2018 - <span></span> <a href="/" th:text="${configs.get('website_title').value}"/></div>
            </footer>
        </div>
    </div>
</div>

<script>
    $("input").click(function(){
        $(".message").css("display","none");
    });

    /**
     * 版权所有截至时间
     */
    function time() {
        var date = new Date();
        var n = date.getFullYear();
        $('.sj span').eq(0).html(n);
    }

    time();
</script>
</body>
</html>