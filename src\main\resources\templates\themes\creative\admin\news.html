<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>
<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('资讯管理', '资讯列表')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-toolbar clearfix">
                                <form class="pull-right search-bar" method="get" action="/Admin/news.html" role="form">
                                    <div class="input-group">
                                        <div class="input-group-btn">
                                            <button class="btn btn-default dropdown-toggle" id="search-btn" data-toggle="dropdown" type="button" aria-haspopup="true" aria-expanded="false">
                                                标题 <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a tabindex="-1" href="javascript:void(0)" data-field="title">名称</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <input type="text" class="form-control" value="" name="search" placeholder="请输入资讯标题" autocomplete="off">
                                    </div>
                                </form>
                                <div class="toolbar-btn-action">
                                    <a class="btn btn-primary m-r-5" href="/Admin/addNews.html"><i class="mdi mdi-plus"></i> 添加资讯</a>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <!--<th>编号</th>-->
                                                <th>资讯标题</th>
                                                <th>资讯摘要</th>
                                                <th>资讯类别</th>
                                                <th>资讯图片</th>
                                                <th>创建时间</th>
                                                <th>修改时间</th>
                                                <th style="text-align: center">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr th:each="project:${pageInfo.getList()}">
                                                <!--<td th:text="${project.id}"/>-->
                                                <td th:text="${project.title}"/>
                                                <td th:text="${project.remark}"/>
                                                <td th:text="${project.catalog}"/>
                                                <td>
                                                    <img th:src="'/UploadFilePath/news/' + ${project.picture}" width="300" height="200"/>
                                                </td>
                                                <td th:text="${#dates.format(project.createTime, 'yyyy-MM-dd HH:mm:ss')}"/>
                                                <td th:text="${#dates.format(project.updateTime, 'yyyy-MM-dd HH:mm:ss')}"/>
                                                <td style="text-align: center">
                                                    <div class="btn-group">
                                                        <a class="btn btn-success m-r-5" th:href="'/Admin/updateNews.html?id='+${project.id}"><i class="mdi mdi-update"></i> 修改</a>
                                                    </div>
                                                    <div class="btn-group">
                                                        <a class="btn btn-danger m-r-5" th:href="'/Admin/deleteNews?id='+${project.id}"><i class="mdi mdi-delete"></i> 删除</a>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <ul class="pagination">
                                    <li th:if="${pageInfo.isHasPreviousPage()} eq true"><a th:href="@{/Admin/news(pageNo=${pageInfo.getPrePage()})}"><span>«</span></a></li>
                                    <li class="disabled" href="javascript:void(0)" th:if="${pageInfo.isHasPreviousPage()} eq false"><span>«</span></li>
                                    <li class="active" href="javascript:void(0)" th:utext="'<span>' + ${pageInfo.pageNum} + '</span>'"/>
                                    <li th:if="${pageInfo.isHasNextPage()} eq true"><a th:href="@{/Admin/news(pageNo=${pageInfo.getNextPage()})}"><span>»</span></a></li>
                                    <li class="disabled" href="javascript:void(0)" th:if="${pageInfo.isHasNextPage()} eq false"><span>»</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>