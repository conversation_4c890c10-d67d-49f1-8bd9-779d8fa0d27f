package com.th.mapper;

import com.th.pojo.*;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.ArrayList;

@Repository
public interface AdminMapper {

    @Select({"select * from `config` where companyId = #{companyId}"})
    ArrayList<Config> getConfigs(Integer companyId);

    @Select({"select * from `news` where companyId = #{companyId} and ",
            "if(#{search} != '', title = #{search}, 1 = 1)",
            "order by id DESC"})
    Page<News> getNews(@Param("search") String search, @Param("companyId") Integer companyId);

    @Insert({"insert into `news`(title, content, createTime, updateTime, remark, catalog, picture, companyId)",
            "values(#{title}, #{content}, #{createTime}, #{updateTime}, #{remark}, #{catalog}, #{picture}, #{companyId})"})
    Integer addNewsAction(News news);

    @Delete({"delete from `news` where id = #{id}"})
    Integer deleteNews(Integer id);

    @Select({"select * from `news` where id = #{id}"})
    News selectNewsById(Integer id);

    @Update({"update `news` set title = #{title}, remark = #{remark}, catalog = #{catalog}, content = #{content}, picture = #{picture}, updateTime = #{updateTime} where id = #{id}"})
    boolean updateNewsAction(News news);

    @Select({"select * from `project` where companyId = #{companyId} and ",
            "if(#{search} != '', title = #{search}, 1 = 1)",
            "order by id DESC"})
    Page<Project> getProject(@Param("search") String search, @Param("companyId") Integer companyId);

    @Insert({"insert into `project`(title, remark, content, picture, createTime, updateTime, companyId)",
            "values(#{title}, #{remark}, #{content}, #{picture}, #{createTime} , #{updateTime}, #{companyId})"})
    boolean addProjectAction(Project project);

    @Delete({"delete from `project` where id = #{id}"})
    int deleteProject(int id);

    @Select({"select * from `project` where id = #{id}"})
    Project selectProjectById(int id);

    @Update({"update `project` set title = #{title}, remark = #{remark}, content = #{content}, picture = #{picture}, updateTime = #{updateTime} where id = #{id}"})
    boolean updateProjectAction(Project project);

    @Select({"select * from `partner` where companyId = #{companyId} and ",
            "if(#{search} != '', company = #{search}, 1 = 1)",
            "order by id DESC"})
    Page<Partner> getPartner(@Param("search") String search, @Param("companyId") Integer companyId);

    @Insert({"insert into `partner`(company, url, picture, createTime, updateTime, companyId)",
            "values(#{company}, #{url}, #{picture}, #{createTime} , #{updateTime}, #{companyId})"})
    boolean addPartnerAction(Partner partner);

    @Delete({"delete from `partner` where id = #{id}"})
    Integer deletePartner(Integer id);

    @Select({"select * from `partner` where id = #{id}"})
    Partner selectPartnerById(Integer id);

    @Update({"update `partner` set company = #{company}, url = #{url}, picture = #{picture}, updateTime = #{updateTime} where id = #{id}"})
    boolean updatePartnerAction(Partner partner);

    @Select({"select * from `product` where companyId = #{companyId} and ",
            "if(#{search} != '', title = #{search}, 1 = 1)",
            "order by id DESC"})
    Page<Product> getProduct(@Param("search") String search, @Param("companyId") Integer companyId);

    @Insert({"insert into `product`(title, remark, isOpenA, android, isOpenI, ios, picture, createTime, updateTime, companyId)",
            "values(#{title}, #{remark}, #{isOpenA}, #{android}, #{isOpenI}, #{ios}, #{picture}, #{createTime} , #{updateTime}, #{companyId})"})
    boolean addProductAction(Product product);

    @Delete({"delete from `product` where id = #{id}"})
    Integer deleteProduct(Integer id);

    @Select({"select * from `product` where id = #{id}"})
    Product selectProductById(Integer id);

    @Update({"update `product` set",
            "title = #{title},",
            "remark = #{remark},",
            "isOpenA = #{isOpenA},",
            "android = #{android},",
            "isOpenI = #{isOpenI},",
            "ios = #{ios},",
            "picture = #{picture},",
            "updateTime = #{updateTime}",
            "where id = #{id}"})
    boolean updateProductAction(Product product);

    @Select({"select * from `config` where `key` = #{key} and companyId = #{companyId}"})
    Config getConfig(@Param("key") String key, @Param("companyId") Integer companyId);

    @Insert({"insert into `config` (`key`, value, companyId) ",
            "values(#{key}, #{value}, #{companyId})"})
    boolean saveConfig(@Param("key") String key, @Param("value") String value, @Param("companyId") Integer companyId);

    @Update({"update `config`",
            "set value = case `key`",
            "when '${key}' then '${value}'",
            "end",
            "where `key` = '${key}' and companyId = #{companyId}"})
    boolean updateConfig(@Param("key") String key, @Param("value") String value, @Param("companyId") Integer companyId);

    @Select({"select * from `log` where companyId = #{companyId} and ",
            "if(#{search} != '', root = #{search}, 1 = 1)",
            "order by id DESC"})
    Page<Log> getLog(@Param("search") String search, @Param("companyId") Integer companyId);

    @Insert({"insert into `log`(ip, os, browser, root, createTime, companyId)",
            "values(#{ip}, #{os}, #{browser}, #{root} , #{createTime}, #{companyId})"})
    boolean recordLog(@Param("ip") String ip,
                      @Param("os") String os,
                      @Param("browser") String browser,
                      @Param("root") String root,
                      @Param("createTime") Timestamp createTime,
                      @Param("companyId") Integer companyId);
}
