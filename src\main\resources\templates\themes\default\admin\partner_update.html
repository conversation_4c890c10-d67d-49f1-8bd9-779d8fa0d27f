<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{admin/fragment/common :: head}"></div>
<script src="/static/admin/simditor/js/jquery.form.js"></script>

<style>
    .m{ width: 100%; margin-left: auto; margin-right: auto; }
    .note-editor.note-frame {border: 1px solid #eee!important;}
    .note-editor .note-editing-area {min-height: 500px;}
</style>

<body>
<div class="lyear-layout-web">
    <div class="lyear-layout-container">
        <!-- 左侧导航 -->
        <div th:replace="~{admin/fragment/common :: aside}"></div>

        <!-- 头部信息 -->
        <div th:replace="~{admin/fragment/common :: header('客户管理', '修改客户')}"></div>

        <!-- 页面内容 -->
        <main class="lyear-layout-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <ul class="nav nav-tabs page-tabs">
                                <li class="active"> <a href="javascript:void(0)">基本</a> </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <form action="/Admin/updatePartnerAction" method="post" name="edit-form" class="edit-form">
                                        <input th:value="${partner.id}" name="id" type="hidden"/>
                                        <div class="form-group">
                                            <label>客户名称</label>
                                            <input class="form-control" type="text" name="company" placeholder="请输入客户名称" th:value="${partner.company}" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>网站地址</label>
                                            <input class="form-control" type="text" name="url" placeholder="请输入网站地址" th:value="${partner.url}" autocomplete="off" required>
                                        </div>
                                        <div class="form-group">
                                            <label>图片预览（点击上传图片）</label>
                                            <img id="previewImg" style="cursor: pointer;" th:src="'/UploadFilePath/partner/'+${partner.picture}" th:error="'/UploadFilePath/error.jpg'" height="300">
                                        </div>
                                        <div class="form-group">
                                            <label>公司图片</label>
                                            <input class="form-control" id="newFileNameCode" type="text" th:value="${partner.picture}" name="pic" placeholder="-" autocomplete="off" required readonly>
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary m-r-5">确 定</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form id='uploadFileForm' style="display: none" action="/Admin/uploadFileAction" method='post' enctype='multipart/form-data'>
                <input type="file" name="oneFile" id="uploadPhoto" value="点击上传图片"/>
                <input type="text" name="fileType" value="partner">
            </form>
        </main>
    </div>
</div>
<script type="text/javascript">
    $("#previewImg").click(function(){
        $("#uploadPhoto").click();
    });

    $(document).ready(function(e) {
        var newFileNameCode = $("#newFileNameCode");

        $("#uploadPhoto").change(function() {
            $("#uploadFileForm").ajaxSubmit({
                dataType: 'json',
                success: function(data) {
                    if(data.status == 0) {
                        var newFileName = data.newFileName;// 新文件名
                        $("#previewImg").prop("src",'/UploadFilePath/partner/' + newFileName);
                        newFileNameCode.attr("value", newFileName);
                        console.log("新文件名：" + newFileName);
                    }
                    else {
                        console.log(data);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    alert("服务器异常");
                }
            });
        });
    });
</script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
</body>
</html>