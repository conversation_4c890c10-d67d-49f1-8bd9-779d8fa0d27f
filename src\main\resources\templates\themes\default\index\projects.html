<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<div th:replace="~{index/fragment/common :: head}"></div>
<body>

<!-- 欢迎 -->
<div th:replace="~{index/fragment/common :: welcome}"></div>

<!-- 导航 -->
<div th:replace="~{index/fragment/common :: header}"></div>

<!-- 图片轮播 -->
<div th:replace="~{index/fragment/common :: jslider}"></div>

<!-- 内容 -->
<div class="pst_bg">
    <div class="pst">
        <i class="fa fa-home"></i>
        您当前的位置：
        <a href="/Project/projects.html">渠道</a>
    </div>
</div>
<div class="scd clearfix">
    <div class="scd_l">
        <div class="s_name">
            渠道
        </div>
        <div th:replace="~{index/fragment/common :: project_slide}"></div>
    </div>
    <div class="scd_r">
        <div class="r_name"><span>渠道</span></div>

        <div class="new">
            <!--产品展示-->
            <!--<div class="row">-->
                <!--<div class="col-lg-3 col-xs-6 pro" th:each="project : ${pageInfo.getList()}">-->
                    <!--<a href="javascript:void(0)">-->
                        <!--<img th:src="'/UploadFilePath/project/' + ${project.picture}" th:alt="${project.title}">-->
                        <!--<p th:text="${project.title}"/>-->
                    <!--</a>-->
                <!--</div>-->
            <!--</div>-->

            <div class="container mt">
                <div class="row">
                    <div class="col-xs-12 col-sm-4 col-md-4" th:each="project : ${pageInfo.getList()}">
                        <div class="recent-work-wrap">
                            <a th:href="'/Project/projectDetail/id/' + ${project.id}">
                                <img class="img-responsive" th:src="'/UploadFilePath/project/' + ${project.picture}" ondragstart="return false" th:alt="${project.title}">
                                <div class="overlay">
                                    <div class="recent-work-inner">
                                        <h3 th:text="${project.title}"/>
                                        <p th:text="${project.remark}"/>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!--产品展示-->

            <div class="space_hx">&nbsp;</div>
            <div class="pages">
                <a th:href="@{/Project/projects.html(pageNo=${pageInfo.getPrePage()})}" class="prev" th:if="${pageInfo.isHasPreviousPage()} eq true">上一页</a>
                <a th:href="'javascript:void(0)'" class="prev" style="cursor: not-allowed" th:if="${pageInfo.isHasPreviousPage()} eq false">上一页</a>

                <a class="now" href="javascript:void(0)" th:text="${pageInfo.pageNum}"/>

                <a th:href="@{/Project/projects.html(pageNo=${pageInfo.getNextPage()})}" class="next" th:if="${pageInfo.isHasNextPage()} eq true">下一页</a>
                <a th:href="'javascript:void(0)'" class="next" style="cursor: not-allowed" th:if="${pageInfo.isHasNextPage()} eq false">下一页</a>
            </div>
        </div>
    </div>
</div>

<!-- 页脚与备案 -->
<div th:replace="~{index/fragment/common :: copyright}"></div>

</body>
</html>
