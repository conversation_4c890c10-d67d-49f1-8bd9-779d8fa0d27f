/* Enhanced Tracking Area Styles */
.enhanced-tracking-area {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.enhanced-tracking-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/static/moban6072/picture/pattern-bg.png') repeat;
    opacity: 0.05;
    z-index: 1;
}

.enhanced-tracking-area .container {
    position: relative;
    z-index: 2;
}

/* Card Styles */
.enhanced-tracking-card,
.enhanced-shipping-card {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.enhanced-tracking-card:hover,
.enhanced-shipping-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Card Header */
.enhanced-tracking-card .card-header,
.enhanced-shipping-card .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    align-items: center;
    border: none;
}

.enhanced-shipping-card .card-header {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.header-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 24px;
}

.header-content .card-title {
    margin: 0 0 5px 0;
    font-size: 20px;
    font-weight: 600;
}

.header-content .card-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

/* Card Body */
.enhanced-tracking-card .card-body,
.enhanced-shipping-card .card-body {
    padding: 30px;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

.enhanced-textarea,
.enhanced-input,
.enhanced-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.enhanced-textarea:focus,
.enhanced-input:focus,
.enhanced-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    background: #ffffff;
    outline: none;
}

.enhanced-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Button Styles */
.btn-enhanced {
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced i {
    margin-right: 8px;
}

/* Tracking Buttons */
.tracking-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.external-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.external-buttons .btn {
    flex: 1;
    padding: 8px 15px;
    border-radius: 15px;
    font-size: 12px;
}

/* Dimension Inputs */
.dimension-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.dimension-inputs input {
    flex: 1;
}

.dimension-separator {
    color: #6c757d;
    font-weight: bold;
    font-size: 16px;
}

/* Results Area */
.tracking-results,
.shipping-results {
    margin-top: 25px;
    border-top: 2px solid #e9ecef;
    padding-top: 20px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.results-header h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.results-header i {
    margin-right: 8px;
    color: #007bff;
}

.results-content {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    min-height: 100px;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Track Item Styles */
.track-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.track-item .track-number {
    font-weight: 600;
    color: #007bff;
    margin-bottom: 5px;
}

.track-item .track-status {
    color: #28a745;
    font-size: 14px;
}

/* Shipping Result Styles */
.shipping-option {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.shipping-option .service-name {
    font-weight: 600;
    color: #495057;
}

.shipping-option .service-price {
    color: #28a745;
    font-weight: 600;
    font-size: 16px;
}

/* Error Styles */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #c3e6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-tracking-area {
        padding: 50px 0;
    }
    
    .enhanced-tracking-card .card-header,
    .enhanced-shipping-card .card-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }
    
    .header-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .enhanced-tracking-card .card-body,
    .enhanced-shipping-card .card-body {
        padding: 20px;
    }
    
    .dimension-inputs {
        flex-direction: column;
        gap: 10px;
    }
    
    .dimension-separator {
        display: none;
    }
    
    .external-buttons {
        flex-direction: column;
    }
    
    .tracking-buttons {
        gap: 10px;
    }
}

/* Animation for results appearing */
.results-fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
