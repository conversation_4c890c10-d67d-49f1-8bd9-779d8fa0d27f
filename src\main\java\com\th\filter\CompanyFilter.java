package com.th.filter;

import com.th.common.Constant;
import com.th.pojo.Company;
import com.th.service.MgrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

@Configuration
public class CompanyFilter implements Filter {
    @Autowired
    private MgrService mgrService;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, <PERSON><PERSON><PERSON>hai<PERSON> filter<PERSON>hain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse)servletResponse;
        HttpSession session = request.getSession();
        String serverName = request.getServerName();
        String requestUri = request.getRequestURI();

        if (requestUri.endsWith(".jpg")
                || requestUri.endsWith(".js")
                || requestUri.endsWith(".png")
                || requestUri.endsWith(".ico")
                || requestUri.endsWith(".jpeg")
                || requestUri.endsWith(".css")
                || requestUri.endsWith("error.html")) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        if (requestUri.endsWith("/Admin") || requestUri.endsWith("/Admin/")
                || requestUri.endsWith("/admin") || requestUri.endsWith("admin/")) {
            response.sendRedirect("/Admin/index.html");
            return;
        }

        String website = (String)session.getAttribute(Constant.WEBSITE);
        if (website != null && !"".equals(website)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        Company company = mgrService.selectCompanyByWebsite(serverName);
        if (company == null) {
            if (serverName.indexOf("www.") == 0) {
                serverName = serverName.replaceAll("www\\.", "");
                company = mgrService.selectCompanyByWebsite(serverName);
            }
        }
        if (company == null) {
            response.sendRedirect("/error.html");
            return;
        } else {
            session.setAttribute(Constant.WEBSITE, company.getWebsite());
            session.setAttribute(Constant.COMPANY_ID, company.getId());
            filterChain.doFilter(request, servletResponse);
            return;
        }
    }

    @Override
    public void destroy() {

    }

    class ModifyHttpServletRequestWrapper extends HttpServletRequestWrapper {

        private Map<String, String> customHeaders;

        public ModifyHttpServletRequestWrapper(HttpServletRequest request) {
            super(request);
            this.customHeaders = new HashMap<>();
        }

        public void putHeader(String name, String value) {
            this.customHeaders.put(name, value);
        }

        @Override
        public String getHeader(String name) {
            String value = this.customHeaders.get(name);
            if (value != null) {
                return value;
            }
            return ((HttpServletRequest) getRequest()).getHeader(name);
        }

        @Override
        public Enumeration<String> getHeaderNames() {
            Set<String> set = new HashSet<>(customHeaders.keySet());
            Enumeration<String> enumeration = ((HttpServletRequest) getRequest()).getHeaderNames();
            while (enumeration.hasMoreElements()) {
                String name = enumeration.nextElement();
                set.add(name);
            }
            return Collections.enumeration(set);
        }

    }
}
